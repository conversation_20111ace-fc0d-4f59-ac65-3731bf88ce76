package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.busi.mapper.BusiExtractExpertEvadeMapper;
import com.ruoyi.busi.domain.BusiExtractExpertEvade;
import com.ruoyi.busi.service.IBusiExtractExpertEvadeService;

/**
 * 专家抽取回避Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BusiExtractExpertEvadeServiceImpl extends ServiceImpl<BusiExtractExpertEvadeMapper, BusiExtractExpertEvade> implements IBusiExtractExpertEvadeService {
    /**
     * 查询专家抽取回避列表
     *
     * @param busiExtractExpertEvade 专家抽取回避
     * @return 专家抽取回避
     */
    @Override
    public List<BusiExtractExpertEvade> selectList(BusiExtractExpertEvade busiExtractExpertEvade) {
        QueryWrapper<BusiExtractExpertEvade> busiExtractExpertEvadeQueryWrapper = new QueryWrapper<>();
        busiExtractExpertEvadeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertEvade.getApplyId()), "apply_id", busiExtractExpertEvade.getApplyId());
        busiExtractExpertEvadeQueryWrapper.like(ObjectUtil.isNotEmpty(busiExtractExpertEvade.getEvadeName()), "evade_name", busiExtractExpertEvade.getEvadeName());
        busiExtractExpertEvadeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertEvade.getEvadeType()), "evade_type", busiExtractExpertEvade.getEvadeType());
        busiExtractExpertEvadeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertEvade.getDelFlag()), "del_flag", busiExtractExpertEvade.getDelFlag());
        busiExtractExpertEvadeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertEvade.getCreateTime()), "create_time", busiExtractExpertEvade.getCreateTime());
        String beginCreateBy = busiExtractExpertEvade.getParams().get("beginCreateBy") != null ? busiExtractExpertEvade.getParams().get("beginCreateBy") + "" : "";
        String endCreateBy = busiExtractExpertEvade.getParams().get("endCreateBy") + "" != null ? busiExtractExpertEvade.getParams().get("endCreateBy") + "" : "";
        busiExtractExpertEvadeQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateBy) && ObjectUtil.isNotEmpty(endCreateBy), "create_by", beginCreateBy, endCreateBy);
        busiExtractExpertEvadeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertEvade.getUpdateTime()), "update_time", busiExtractExpertEvade.getUpdateTime());
        String beginUpdateBy = busiExtractExpertEvade.getParams().get("beginUpdateBy") != null ? busiExtractExpertEvade.getParams().get("beginUpdateBy") + "" : "";
        String endUpdateBy = busiExtractExpertEvade.getParams().get("endUpdateBy") + "" != null ? busiExtractExpertEvade.getParams().get("endUpdateBy") + "" : "";
        busiExtractExpertEvadeQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateBy) && ObjectUtil.isNotEmpty(endUpdateBy), "update_by", beginUpdateBy, endUpdateBy);
        return list(busiExtractExpertEvadeQueryWrapper);
    }

    @Override
    public List<BusiExtractExpertEvade> getByApplyIds(List<Long> applyIds) {
        return list(new QueryWrapper<BusiExtractExpertEvade>().in("apply_id", applyIds));
    }
}
