package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
        import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.busi.mapper.BusiExtractExpertGroupMapper;
import com.ruoyi.busi.domain.BusiExtractExpertGroup;
import com.ruoyi.busi.service.IBusiExtractExpertGroupService;

/**
 * 专家组Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BusiExtractExpertGroupServiceImpl extends ServiceImpl<BusiExtractExpertGroupMapper, BusiExtractExpertGroup> implements IBusiExtractExpertGroupService {
    /**
     * 查询专家组列表
     *
     * @param busiExtractExpertGroup 专家组
     * @return 专家组
     */
    @Override
    public List<BusiExtractExpertGroup> selectList(BusiExtractExpertGroup busiExtractExpertGroup) {
        QueryWrapper<BusiExtractExpertGroup> busiExtractExpertGroupQueryWrapper = new QueryWrapper<>();
                        busiExtractExpertGroupQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertGroup.getApplyId()),"apply_id",busiExtractExpertGroup.getApplyId());
                        busiExtractExpertGroupQueryWrapper.like(ObjectUtil.isNotEmpty(busiExtractExpertGroup.getGroupName()),"group_name",busiExtractExpertGroup.getGroupName());
                        busiExtractExpertGroupQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertGroup.getExpertNumber()),"expert_number",busiExtractExpertGroup.getExpertNumber());
                        busiExtractExpertGroupQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertGroup.getExpertClassificationCode()),"expert_classification_code",busiExtractExpertGroup.getExpertClassificationCode());
                        busiExtractExpertGroupQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertGroup.getExpertType()),"expert_type",busiExtractExpertGroup.getExpertType());
                        busiExtractExpertGroupQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertGroup.getGroupAddress()),"group_address",busiExtractExpertGroup.getGroupAddress());
        return list(busiExtractExpertGroupQueryWrapper);
    }

    /**
     * 获取专家组集合
     * @param applyIds 专家申请ids
     * @return
     */
    @Override
    public List<BusiExtractExpertGroup> getByApplyIds(List<Long> applyIds) {
        return list(new QueryWrapper<BusiExtractExpertGroup>().in("apply_id",applyIds));
    }
}
