package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.busi.domain.BusiExtractExpertApply;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiExtractExpertApplyService;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.utils.LoginZFCGRequest;
import com.ruoyi.utils.ZhuanJiaInfoVo;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;
import com.ruoyi.busi.mapper.BusiExtractExpertResultMapper;
import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.ruoyi.busi.service.IBusiExtractExpertResultService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 专家抽取结果业务层处理实现类
 *
 * 该服务类负责处理专家抽取结果的相关业务逻辑，包括：
 * 1. 专家抽取结果的查询和管理
 * 2. 专家登录验证和项目查询
 * 3. 专家签到功能
 * 4. 与外部专家系统的集成
 * 5. 专家组长统计功能
 *
 * 核心功能：
 * - 支持多条件查询专家抽取结果
 * - 实现专家身份验证和项目匹配
 * - 自动处理专家签到状态更新
 * - 集成外部专家信息系统
 * - 统计专家担任组长的次数
 *
 * <AUTHOR>
 * @date 2024-06-06
 * @version 1.0
 */
@Service
public class BusiExtractExpertResultServiceImpl extends ServiceImpl<BusiExtractExpertResultMapper, BusiExtractExpertResult> implements IBusiExtractExpertResultService {

    // ==================== 依赖注入服务 ====================

    /** 专家抽取结果服务 - 用于处理专家抽取结果的业务逻辑 */
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;

    /** 专家抽取申请服务 - 用于处理专家抽取申请的业务逻辑 */
    @Autowired
    private IBusiExtractExpertApplyService busiExtractExpertApplyService;

    /** 招标公告服务 - 用于处理招标公告相关业务 */
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;

    /** 招标项目服务 - 用于处理招标项目相关业务 */
    @Autowired
    private IBusiTenderProjectService busiTenderProjectService;

    /** 用户详情服务 - 用于用户认证和授权 */
    @Autowired
    private UserDetailsService userDetailsService;

    // ==================== 外部系统配置参数 ====================

    /** 专家信息查询接口地址 - 用于获取专家详细信息 */
    @Value("${extractcode.zhuanJiaInfo}")
    String zhuanJiaInfo;

    /** 外部系统认证Token获取地址 */
    @Value("${extractcode.url}")
    String tokenUrl;

    /** 外部系统认证用户名 */
    @Value("${extractcode.username}")
    String username;

    /** 外部系统第三方认证密钥 */
    @Value("${extractcode.thirdPartySecret}")
    String thirdPartySecret;

    /** 外部系统认证密码 */
    @Value("${extractcode.password}")
    String password;
    /**
     * 根据条件查询专家抽取结果列表
     *
     * 支持多条件动态查询，包括申请ID、专家ID、专家姓名、专家编号、专家评价等条件
     * 使用 MyBatis-Plus 的 QueryWrapper 构建动态查询条件
     *
     * @param busiExtractExpertResult 查询条件对象，包含以下可选条件：
     *                               - applyId: 专家抽取申请ID（精确匹配）
     *                               - expertId: 专家ID（精确匹配）
     *                               - expertName: 专家姓名（模糊匹配）
     *                               - expertCode: 专家编号/身份证号（精确匹配）
     *                               - expertAppraise: 专家评价（精确匹配）
     * @return List<BusiExtractExpertResult> 符合条件的专家抽取结果列表
     */
    @Override
    public List<BusiExtractExpertResult> selectList(BusiExtractExpertResult busiExtractExpertResult) {
        // 创建查询条件构造器
        QueryWrapper<BusiExtractExpertResult> busiExtractExpertResultQueryWrapper = new QueryWrapper<>();

        // 动态添加查询条件（只有当条件值不为空时才添加）
        // 根据申请ID精确查询
        busiExtractExpertResultQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertResult.getApplyId()), "apply_id", busiExtractExpertResult.getApplyId());

        // 根据专家ID精确查询
        busiExtractExpertResultQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertResult.getExpertId()), "expert_id", busiExtractExpertResult.getExpertId());

        // 根据专家姓名模糊查询（支持部分匹配）
        busiExtractExpertResultQueryWrapper.like(ObjectUtil.isNotEmpty(busiExtractExpertResult.getExpertName()), "expert_name", busiExtractExpertResult.getExpertName());

        // 根据专家编号精确查询
        busiExtractExpertResultQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertResult.getExpertCode()), "expert_code", busiExtractExpertResult.getExpertCode());

        // 根据专家评价精确查询
        busiExtractExpertResultQueryWrapper.eq(ObjectUtil.isNotEmpty(busiExtractExpertResult.getExpertAppraise()), "expert_appraise", busiExtractExpertResult.getExpertAppraise());

        // 执行查询并返回结果
        return list(busiExtractExpertResultQueryWrapper);
    }

    /**
     * 根据项目ID获取专家抽取结果列表
     *
     * 通过项目ID查找对应的专家抽取申请，然后获取该申请下的所有专家抽取结果
     * 该方法主要用于获取某个具体项目的专家抽取情况
     *
     * @param projectId 项目ID，用于查找对应的专家抽取申请
     * @return List<BusiExtractExpertResult> 该项目的专家抽取结果列表
     *         - 如果项目存在且有唯一的抽取申请，返回对应的专家抽取结果
     *         - 如果项目不存在或抽取申请不唯一，返回空列表
     * @throws IOException 当调用外部接口时可能抛出的IO异常
     */
    @Override
    public List<BusiExtractExpertResult> getByProject(Long projectId) throws IOException {
        // 第一步：根据项目ID查询专家抽取申请记录
        QueryWrapper<BusiExtractExpertApply> appliQuery = new QueryWrapper();
        appliQuery.eq("project_id", projectId);  // 根据项目ID精确匹配
        List<BusiExtractExpertApply> applyList = busiExtractExpertApplyService.list(appliQuery);

        // 初始化返回结果列表
        List<BusiExtractExpertResult> r = new ArrayList<>();

        // 第二步：验证查询结果并获取专家抽取结果
        if (applyList != null && applyList.size() == 1) {
            // 确保项目有且仅有一个专家抽取申请（业务规则要求）
            BusiExtractExpertResult resultQuery = new BusiExtractExpertResult();
            resultQuery.setApplyId(applyList.get(0).getApplyId());  // 设置申请ID作为查询条件

            // 根据申请ID查询所有相关的专家抽取结果
            r = selectList(resultQuery);
        }
        // 注意：如果 applyList 为空或包含多个申请，将返回空列表
        // 这是为了确保数据的一致性和业务逻辑的正确性

        return r;
    }

    /**
     * 专家登录验证方法
     *
     * 通过专家身份证号验证专家身份，并返回专家的相关信息
     * 该方法主要用于专家系统的身份认证功能
     *
     * @param busiExtractExpertResult 包含专家编号（身份证号）的查询对象
     *                               - expertCode: 专家身份证号，用于身份验证
     * @return AjaxResult 登录验证结果
     *         - 成功：返回专家的所有抽取结果信息
     *         - 失败：返回错误信息（身份证号格式错误或未找到专家信息）
     */
    @Override
    public AjaxResult login(BusiExtractExpertResult busiExtractExpertResult) {
        // 第一步：验证身份证号格式
        // 身份证号长度必须至少15位（支持15位和18位身份证）
        if (busiExtractExpertResult.getExpertCode().length() < 15) {
            return AjaxResult.error("请检查身份证号是否输入错误");
        }

        // 第二步：根据身份证号查询专家的所有抽取结果信息
        List<BusiExtractExpertResult> expertCode = busiExtractExpertResultService.list(
            new QueryWrapper<BusiExtractExpertResult>()
                .eq("expert_code", busiExtractExpertResult.getExpertCode())  // 根据专家编号精确匹配
        );

        // 第三步：验证查询结果并返回相应信息
        if (!expertCode.isEmpty()) {
            // 找到专家信息，登录成功
            return AjaxResult.success(expertCode);
        } else {
            // 未找到专家信息，登录失败
            return AjaxResult.error("没有查询到相关专家/业主代表信息");
        }
    }

    /**
     * 获取登录专家的今日项目信息并处理签到
     *
     * 该方法是专家系统的核心功能之一，主要用于：
     * 1. 查询专家今日需要参与评标的项目
     * 2. 自动处理专家签到状态更新
     * 3. 返回项目详细信息供专家查看
     *
     * 业务流程：
     * 1. 根据专家编号查询所有相关的抽取结果
     * 2. 通过抽取结果找到对应的申请记录和项目
     * 3. 筛选出今日有评标安排的项目
     * 4. 自动更新专家签到状态
     * 5. 返回项目列表
     *
     * @param busiExtractExpertResult 包含专家编号的查询对象
     *                               - expertCode: 专家身份证号
     *                               - searchDate: 可选的查询日期（当前版本使用今日）
     * @return AjaxResult 查询结果
     *         - 成功：返回今日需要评标的项目列表
     *         - 无项目：返回提示信息
     */
    @Override
    public AjaxResult getLoginUserProject(BusiExtractExpertResult busiExtractExpertResult) {

        /*
         * 注释掉的代码块 - 原始实现方案
         * 该方案是先查询今日评标公告，再匹配专家，但逻辑相对复杂
         * 当前采用的方案是先查询专家相关项目，再筛选今日评标项目，逻辑更清晰
         */
       /*     // 获取当前日期的开始和结束时间
            LocalDate today = LocalDate.now();
            Date todayStart = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date todayEnd = Date.from(today.atStartOfDay(ZoneId.systemDefault()).plusDays(1).minusSeconds(1).toInstant());

            QueryWrapper<BusiTenderNotice> queryWrapper = new QueryWrapper<>();
            queryWrapper
                    .ge("bid_evaluation_time", todayStart) // 评标时间大于等于今天开始时间
                    .lt("bid_evaluation_time", todayEnd)   // 评标时间小于今天结束时间
                    .eq("del_flag", 0);                   // 假设你需要过滤掉已删除的记录
            //查询评标信息是今天的采购公告
           List<BusiTenderNotice> todayBidEvaluationNotices = busiTenderNoticeService.list(queryWrapper);
           List<Long> applyIds = todayBidEvaluationNotices.stream()
                .map(BusiTenderNotice::getProjectId)
                .collect(Collectors.toList());
            //查询申请结果
           List<BusiExtractExpertResult> expertCode = busiExtractExpertResultService.list(new QueryWrapper<BusiExtractExpertResult>()
                .eq("expert_code", busiExtractExpertResult.getExpertCode())
                .in("apply_id", applyIds)
                .eq("del_flag", 0));

        if (!expertCode.isEmpty()){
            return null;
        }else {
            return AjaxResult.error("没有查询到相关专家信息");
        }*/

        // ==================== 第一步：查询专家的所有抽取结果记录 ====================
        List<BusiExtractExpertResult> expertCode = busiExtractExpertResultService.list(new QueryWrapper<BusiExtractExpertResult>()
                .eq("expert_code", busiExtractExpertResult.getExpertCode())
                        .eq("is_avoid",0)
                .eq("del_flag", 0));

        // ==================== 第二步：提取申请ID并查询相关申请记录 ====================
        // 从专家抽取结果中提取申请ID列表
        List<Long> applyIds = expertCode.stream()
                .map(BusiExtractExpertResult::getApplyId)
                .collect(Collectors.toList());

        // 检查是否查询到专家信息
        if (!expertCode.isEmpty()) {
            // 通过申请ID查询专家抽取申请记录
            List<BusiExtractExpertApply> busiExtractExpertApplies = busiExtractExpertApplyService.listByIds(applyIds);

            // 从申请记录中提取项目ID列表
            List<Long> projectIds = busiExtractExpertApplies.stream()
                    .map(BusiExtractExpertApply::getProjectId)  // 获取项目ID
                    .collect(Collectors.toList());  // 收集为列表

            // ==================== 第三步：设置今日时间范围 ====================
            LocalDate today = LocalDate.now();  // 获取当前日期
            // 今天开始时间：00:00:00
            Date todayStart = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
            // 今天结束时间：23:59:59
            Date todayEnd = Date.from(today.atStartOfDay(ZoneId.systemDefault()).plusDays(1).minusSeconds(1).toInstant());

            // ==================== 第四步：构建查询条件，查询今日评标公告 ====================
            QueryWrapper<BusiTenderNotice> queryWrapper = new QueryWrapper<>();
            queryWrapper
                    .ge("bid_evaluation_time", todayStart)  // 评标时间大于等于今天开始时间
                    .lt("bid_evaluation_time", todayEnd)    // 评标时间小于今天结束时间
                    .in("project_id", projectIds)           // 项目ID必须在专家参与的项目列表中
                    .eq("del_flag", 0);                     // 过滤已删除的记录

            /*
             * 备用功能：根据指定日期查询（当前版本未启用）
             * 如果需要支持查询指定日期的评标安排，可以使用以下代码：
             *
             * queryWrapper
             *     .ge("bid_evaluation_time", busiExtractExpertResult.getSearchDate() + " 00:00:00")
             *     .lt("bid_evaluation_time", busiExtractExpertResult.getSearchDate() + " 23:59:59")
             *     .in("project_id", projectIds)
             *     .eq("del_flag", 0);
             */

            // ==================== 第五步：查询今日评标公告并提取项目ID ====================
            List<BusiTenderNotice> todayBidEvaluationNotices = busiTenderNoticeService.list(queryWrapper);

            // 从今天的评标公告中提取项目ID列表
            List<Long> todayProjectIds = todayBidEvaluationNotices.stream()
                    .map(BusiTenderNotice::getProjectId)
                    .collect(Collectors.toList());
            // ==================== 第六步：处理今日评标项目和专家签到 ====================
            if (todayProjectIds.size() > 0) {
                // 查询今天需要评标的项目详细信息
                List<BusiTenderProject> projectList = busiTenderProjectService.list(
                    new QueryWrapper<BusiTenderProject>().in("project_id", todayProjectIds)
                );

                // ==================== 第七步：自动处理专家签到 ====================
                // 循环处理每个项目，更新专家签到信息
                for (BusiTenderProject tenderProject : projectList) {
                    // 查询该项目的专家抽取申请记录
                    // extraction_type = 1 表示正式抽取（区别于备选抽取）
                    BusiExtractExpertApply busiExtractExpertApply = busiExtractExpertApplyService.getOne(
                        new QueryWrapper<BusiExtractExpertApply>()
                            .eq("project_id", tenderProject.getProjectId())
                            .eq("extraction_type", 1)  // 正式抽取类型
                    );

                    // 添加空值检查，防止空指针异常
                    if (busiExtractExpertApply == null) {
                        // 如果没有找到对应的申请记录，跳过当前项目
                        continue;
                    }

                    // 查询当前专家在该项目中的抽取结果记录
                    List<BusiExtractExpertResult> busiExtractExpertResults = busiExtractExpertResultService.list(
                        new QueryWrapper<BusiExtractExpertResult>()
                            .eq("apply_id", busiExtractExpertApply.getApplyId())  // 根据申请ID查询
                            .eq("expert_code", busiExtractExpertResult.getExpertCode())  // 根据专家编号查询
                    );

                    // 批量更新专家签到信息
                    busiExtractExpertResults.stream().forEach(result -> {
                        result.setSignTime(new Date());  // 设置签到时间为当前时间
                        result.setSignStatus(1);         // 设置签到状态为已签到（1=已签到，0=未签到）
                    });

                    // 批量保存或更新专家签到信息到数据库
                    busiExtractExpertResultService.saveOrUpdateBatch(busiExtractExpertResults);
                }

                // 返回今天需要评标的项目列表供前端显示
                return AjaxResult.success(projectList);
            } else {
                // 今天没有需要评标的项目
                return AjaxResult.success("今日没有查询到相关项目信息");
            }
        } else {
            // 没有查询到该专家的相关信息
            return AjaxResult.success("没有查询到相关项目信息");
        }
    }
    /**
     * 根据项目ID获取专家信息并统计组长次数
     *
     * 该方法是专家管理的核心功能，主要用于：
     * 1. 获取指定项目的所有专家信息
     * 2. 过滤掉需要回避的专家
     * 3. 统计每个专家担任组长的历史次数
     * 4. 从外部系统获取专家的详细信息
     *
     * 业务场景：
     * - 评标委员会组建时查看专家信息
     * - 专家组长选择时参考历史担任次数
     * - 专家回避情况的处理
     *
     * @param busiExtractExpertResult 查询条件对象
     *                               - projectId: 项目ID，必填参数
     * @return List<BusiExtractExpertResult> 专家抽取结果列表，包含：
     *         - 基本专家信息
     *         - 组长担任次数统计
     *         - 外部系统的专家详细信息
     * @throws IOException 调用外部专家系统接口时可能抛出的异常
     */
    @Transactional  // 确保数据一致性，涉及多表查询和外部接口调用
    @Override
    public List<BusiExtractExpertResult> getZhuanJiaByProjectId(BusiExtractExpertResult busiExtractExpertResult) throws IOException {

        // ==================== 第一步：查询项目的专家抽取申请记录 ====================
        List<BusiExtractExpertApply> busiExtractExpertApplies = busiExtractExpertApplyService.list(
            new QueryWrapper<BusiExtractExpertApply>()
                .eq("project_id", busiExtractExpertResult.getProjectId())  // 根据项目ID查询
                .eq("extraction_type", 1)  // 抽取类型为1（正式抽取，非备选）
        );

        // 如果没有找到抽取申请记录，直接返回空列表
        if (busiExtractExpertApplies == null || busiExtractExpertApplies.isEmpty()) {
            return new ArrayList<>();
        }

        // ==================== 第二步：提取申请ID并查询专家抽取结果 ====================
        // 从抽取申请中提取申请ID列表
        List<Long> applyIds = busiExtractExpertApplies.stream()
                .map(BusiExtractExpertApply::getApplyId)  // 获取申请ID
                .collect(Collectors.toList());  // 收集为列表

        // 获取不需要回避的专家信息
        // is_avoid = 0 表示该专家不需要回避
        // 注释掉的 is_owner = 0 可能用于过滤业主代表
        List<BusiExtractExpertResult> list = busiExtractExpertResultService.list(new QueryWrapper<BusiExtractExpertResult>()
                .in("apply_id", applyIds)  // 申请ID在列表中
                .eq("is_avoid", 0)         // 不需要回避的专家
                // .eq("is_owner", 0)      // 可选：过滤业主代表
        );
        // ==================== 第三步：统计专家担任组长的历史次数 ====================

        // 查询所有担任过组长的专家记录（expert_leader = 1 表示担任组长）
        List<BusiExtractExpertResult> expertLeader = busiExtractExpertResultService.list(
            new QueryWrapper<BusiExtractExpertResult>().eq("expert_leader", 1)
        );

        // 按专家ID分组，统计每个专家担任组长的次数
        Map<Long, List<BusiExtractExpertResult>> groupedByExpertId = expertLeader.stream()
                .collect(Collectors.groupingBy(BusiExtractExpertResult::getExpertId));

        // ==================== 第四步：为每个专家设置组长次数并获取详细信息 ====================
        // 循环处理每个专家，设置组长担任次数
        for (BusiExtractExpertResult extractExpertResult : list) {
            // 获取当前专家的组长担任记录
            List<BusiExtractExpertResult> busiExtractExpertResults = groupedByExpertId.get(extractExpertResult.getExpertId());

            /*
             * 注释掉的代码 - 原始逻辑有误
             * 原始代码的逻辑判断有问题，已修正为下面的正确逻辑
             */
            /*
            if ((null != busiExtractExpertResults) || !busiExtractExpertResults.isEmpty()) {
                System.out.println("busiExtractExpertResults 是空的。");
                extractExpertResult.setExpertLeaderCount(0);
            } else {
                System.out.println("busiExtractExpertResults 不是空的，它包含了元素。");
                extractExpertResult.setExpertLeaderCount(busiExtractExpertResults.size());
            }*/

            // 正确的逻辑：检查专家是否有担任组长的历史记录
            if (busiExtractExpertResults != null && !busiExtractExpertResults.isEmpty()) {
                // 专家有担任组长的历史记录
                System.out.println("专家ID: " + extractExpertResult.getExpertId() + " 担任组长次数: " + busiExtractExpertResults.size());
                extractExpertResult.setExpertLeaderCount(busiExtractExpertResults.size());  // 设置担任组长次数
            } else {
                // 专家没有担任组长的历史记录
                System.out.println("专家ID: " + extractExpertResult.getExpertId() + " 从未担任过组长");
                extractExpertResult.setExpertLeaderCount(0);  // 设置组长次数为0
            }
            // ==================== 第五步：调用外部系统获取专家详细信息 ====================

            // 创建HTTP客户端
//            OkHttpClient client = new OkHttpClient().newBuilder()
//                    .build();
//
//            // 构建请求，获取专家详细信息
//            Request request = new Request.Builder()
//                    .url(zhuanJiaInfo + extractExpertResult.getExpertId())  // 拼接专家信息查询URL
//                    .method("GET", null)  // 使用GET方法
//                    .addHeader("Token", getToken())  // 添加认证Token
//                    .build();
//
//            // 执行HTTP请求
//            Response response = client.newCall(request).execute();
//
//            // 解析响应结果为专家信息对象
//            ZhuanJiaInfoVo zhuanJiaInfoVo = JSONObject.parseObject(response.body().string(), ZhuanJiaInfoVo.class);
//
//            // 将外部系统的专家详细信息设置到结果对象中
//            extractExpertResult.setZhuanJiaInfoVo(zhuanJiaInfoVo.getExpert());
        }

        // 返回包含完整信息的专家列表
        return list;
    }
    /**
     * 获取外部专家系统的认证Token
     *
     * 该方法用于向外部专家系统申请访问令牌，以便后续调用专家信息接口
     * 采用第三方认证方式，需要提供用户名、密码和第三方密钥
     *
     * 认证流程：
     * 1. 构建包含认证信息的JSON请求体
     * 2. 发送POST请求到认证服务器
     * 3. 解析响应获取Token
     * 4. 返回Token供后续接口调用使用
     *
     * @return String 认证成功返回的Token字符串
     * @throws IOException 网络请求异常
     * @throws RuntimeException Token获取失败时抛出运行时异常
     */
    public String getToken() throws IOException {
        // 创建HTTP客户端
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();

        // 设置请求内容类型为JSON
        MediaType mediaType = MediaType.parse("application/json");

        // 构建认证请求体，包含时间戳、用户名、密码和第三方密钥
        String requestBodyJson = "{\r\n" +
                "\"t\":" + System.currentTimeMillis() + ",\r\n" +  // 时间戳，防止重放攻击
                "\"username\":\"" + username + "\",\r\n" +         // 认证用户名
                "\"password\":\"" + password + "\",\r\n" +         // 认证密码
                "\"thirdPartySecret\":\"" + thirdPartySecret + "\"\r\n" +  // 第三方密钥
                "}";

        RequestBody body = RequestBody.create(mediaType, requestBodyJson);

        // 输出请求体内容用于调试（生产环境应移除或使用日志）
        System.out.println("Token请求体: " + requestBodyJson);

        // 构建HTTP请求
        Request request = new Request.Builder()
                .url(tokenUrl)  // 认证服务器地址
                .method("POST", body)  // 使用POST方法
                .addHeader("Content-Type", "application/json")  // 设置内容类型
                .build();

        // 执行请求
        Response response = client.newCall(request).execute();

        // 处理响应结果
        if (response.body() != null) {
            // 解析JSON响应获取Token
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            return jsonObject.getString("token");
        } else {
            // 响应体为空，抛出异常
            throw new RuntimeException("请求专家抽取token失败：响应体为空");
        }
    }


    /**
     * 获取专家统计信息
     *
     * 该方法用于统计指定专家的参与项目情况，包括：
     * 1. 专家参与项目的总次数
     * 2. 专家的基本信息
     *
     * 主要用于专家履历查询和统计分析
     *
     * @param expertId 专家ID，用于查询指定专家的统计信息
     * @return Map 包含专家统计信息的Map对象
     *         - "zhuanJiaInfo": 专家基本信息（BusiExtractExpertResult对象）
     *         - "count": 专家参与项目的总次数（Integer）
     *
     * @apiNote 该方法假设专家至少参与过一个项目，否则获取专家信息时可能出现数组越界异常
     *          建议在生产环境中添加空值检查
     */
    @Override
    public Map getZhuanJiaCount(Long expertId) {
        // 统计专家参与项目的总次数
        int count = busiExtractExpertResultService.count(
            new QueryWrapper<BusiExtractExpertResult>().eq("expert_id", expertId)
        );

        // 获取专家的所有抽取结果记录
        List<BusiExtractExpertResult> busiExtractExpertResults = busiExtractExpertResultService.list(
            new QueryWrapper<BusiExtractExpertResult>().eq("expert_id", expertId)
        );

        // 构建返回结果Map
        Map map = new HashMap();

        // 注意：这里假设专家至少有一条记录，实际使用中应该添加空值检查
        // 建议改为：busiExtractExpertResults.isEmpty() ? null : busiExtractExpertResults.get(0)
        map.put("zhuanJiaInfo", busiExtractExpertResults.get(0));  // 专家基本信息
        map.put("count", count);  // 参与项目总次数

        return map;
    }

}
