package com.ruoyi.busi.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.busi.mapper.BusiOpenMessageRecordMapper;
import com.ruoyi.busi.domain.BusiOpenMessageRecord;
import com.ruoyi.busi.service.IBusiOpenMessageRecordService;

/**
 * 在线开标消息记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Service
public class BusiOpenMessageRecordServiceImpl extends ServiceImpl<BusiOpenMessageRecordMapper, BusiOpenMessageRecord> implements IBusiOpenMessageRecordService {

    @Autowired
    private IBaseEntInfoService baseEntInfoService;

    /**
     * 查询在线开标消息记录列表
     *
     * @param busiOpenMessageRecord 在线开标消息记录
     * @return 在线开标消息记录
     */
    @Override
    public List<BusiOpenMessageRecord> selectList(BusiOpenMessageRecord busiOpenMessageRecord) {
        QueryWrapper<BusiOpenMessageRecord> busiOpenMessageRecordQueryWrapper = new QueryWrapper<>();
                        busiOpenMessageRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiOpenMessageRecord.getSendId()),"send_id",busiOpenMessageRecord.getSendId());
                        busiOpenMessageRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiOpenMessageRecord.getProjectId()),"project_id",busiOpenMessageRecord.getProjectId());
                        busiOpenMessageRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiOpenMessageRecord.getContent()),"content",busiOpenMessageRecord.getContent());
                        busiOpenMessageRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiOpenMessageRecord.getType()),"type",busiOpenMessageRecord.getType());
                        busiOpenMessageRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiOpenMessageRecord.getStatus()),"status",busiOpenMessageRecord.getStatus());
                        busiOpenMessageRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(busiOpenMessageRecord.getSendTime()),"send_time",busiOpenMessageRecord.getSendTime());
            busiOpenMessageRecordQueryWrapper.apply(
                ObjectUtil.isNotEmpty(busiOpenMessageRecord.getParams().get("dataScope")),
        busiOpenMessageRecord.getParams().get("dataScope")+""
        );
        return list(busiOpenMessageRecordQueryWrapper);
    }

    @Override
    public AjaxResult historyMessages(Long projectId) {
        //List<BusiOpenMessageRecord> list = list(new QueryWrapper<BusiOpenMessageRecord>().eq("project_id", projectId));
        //获取消息记录
        List<BusiOpenMessageRecord> list = list(new QueryWrapper<BusiOpenMessageRecord>()
                .eq("project_id", projectId)
                .orderByAsc("send_time"));
        if (!list.isEmpty()){
            Set<Long> sendIdSet = new HashSet<>();
            for (BusiOpenMessageRecord record : list) {
                sendIdSet.add(record.getSendId());
            }
            List<Long> sendIdList = sendIdSet.stream().collect(Collectors.toList());
            //企业信息对象
            List<BaseEntInfo> baseEntInfos = baseEntInfoService.getByIds(sendIdList);
            Map<Long, List<BaseEntInfo>> baseEntInfoMap = baseEntInfos.stream()
                    .collect(Collectors.groupingBy(BaseEntInfo::getEntId));
            list.forEach(item -> {
                item.setBaseEntInfos(baseEntInfoMap.get(item.getSendId()));
                item.setSendName(baseEntInfoMap.get(item.getSendId()).get(0).getEntName());
            });

            return  AjaxResult.success(list);
        }else {
            return  AjaxResult.success(new ArrayList<BusiOpenMessageRecord>());
        }
    }
}
