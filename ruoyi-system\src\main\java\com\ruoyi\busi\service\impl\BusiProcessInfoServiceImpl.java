package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.base.service.IBaseTreeDataService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.mapper.BusiProcessInfoMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;
import com.ruoyi.eval.service.IEvalProjectEvaluationInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 全流程信息归档信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Service
public class BusiProcessInfoServiceImpl extends ServiceImpl<BusiProcessInfoMapper, BusiProcessInfo> implements IBusiProcessInfoService {

    @Autowired
    private IBaseTreeDataService baseTreeDataService;
    @Autowired
    private IBusiAttachmentService attachmentService;
    @Autowired
    private IBusiTenderIntentionService tenderIntentionService;
    @Autowired
    private IBusiTenderProjectService tenderProjectService;
    @Autowired
    private IBusiTenderNoticeService tenderNoticeService;
    @Autowired
    private IBusiBidOpeningService bidOpeningService;
    @Autowired
    private IBusiBidEvaluationService bidEvaluationService;
    @Autowired
    private IBusiWinningBidderNoticeService busiWinningBidderNoticeService;
    @Autowired
    private IBusiExpertTransactionContractService busiExpertTransactionContractService;
    @Autowired
    private IEvalProjectEvaluationInfoService iEvalProjectEvaluationInfoService;

    /**
     * 查询全流程信息归档信息列表
     *
     * @param busiProcessInfo 全流程信息归档信息
     * @return 全流程信息归档信息
     */
    @Override
    @DataScope(entAlias = "tenderer_id,agency_id")
    public List<BusiProcessInfo> selectList(BusiProcessInfo busiProcessInfo) {
        QueryWrapper<BusiProcessInfo> busiProcessInfoQueryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(busiProcessInfo.getParams().get("dataScope")) &&
                (!busiProcessInfo.getParams().containsKey("isScope") || Boolean.parseBoolean(busiProcessInfo.getParams().get("isScope").toString()))) {
            LoginUser user = SecurityUtils.getLoginUser();
            if (user != null) {
                busiProcessInfoQueryWrapper.inSql("project_id",
                        "SELECT project_id FROM busi_tender_project WHERE del_flag=0 AND "+busiProcessInfo.getParams().get("dataScope").toString());
            }
        }
        busiProcessInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(busiProcessInfo.getProjectId()),"project_id",busiProcessInfo.getProjectId());
        return list(busiProcessInfoQueryWrapper);
    }

    @Transactional
    @Override
    public boolean initInfo(Long projectId, String projectName){
        try {
            //新增全流程归档
            BusiProcessInfo busiProcessInfo = new BusiProcessInfo();
            busiProcessInfo.setBusiState(0);
            busiProcessInfo.setProjectId(projectId);
            busiProcessInfo.setProjectName(projectName);
            save(busiProcessInfo);
            return initAttachmentForProcess(busiProcessInfo.getProcessId());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

  @Transactional
    @Override
    public Boolean initAttachmentForProcess(Long processId) {
        BusiProcessInfo info = baseMapper.selectById(processId);
        if (info != null) {
            attachmentService.deleteByBusiId(processId);
            BaseTreeData baseTreeData = new BaseTreeData();
            baseTreeData.setType(10);
            List<BaseTreeData> datas = baseTreeDataService.selectListWithTree(baseTreeData);
            for (BaseTreeData first : datas) {
                switch (first.getThirdPid()) {
                    //意向页面字典 busi_tender_intention_attachment   tender_intention_attachment
                    case "busi_tender_intention_attachment":
                        BusiTenderIntention tenderIntention = tenderIntentionService.selectByProject(info.getProjectId());
                        if(tenderIntention!=null && tenderIntention.getIntentionId()!=null) {
                            List<BusiAttachment> intentionAttachments = attachmentService.selectByBusiId(tenderIntention.getIntentionId());
                            saveAttachments(intentionAttachments, first.getChildrens(), processId);
                        }
                        break;
                    //项目     tender_project_attachment
                    case "tender_project_attachment":
                        List<BusiAttachment> projectAttachments = attachmentService.selectByBusiId(info.getProjectId());
                        saveAttachments(projectAttachments, first.getChildrens(),processId);
                        break;
                    //公告 busi_tender_notice_attachment  tender_notice_attachment
                    case "busi_tender_notice_attachment":
                        BusiTenderNotice tenderNotice = tenderNoticeService.selectByProject(info.getProjectId());
                        List<BusiAttachment> noticeAttachments = attachmentService.selectByBusiId(tenderNotice.getNoticeId());
                        saveAttachments(noticeAttachments, first.getChildrens(),processId);
                        break;

                    //开标
                    case "bid_opening_attachment":
                        BusiBidOpening busiBidOpening = bidOpeningService.selectByProject(info.getProjectId());
                        List<BusiAttachment> bidOpeningAttachments = attachmentService.selectByBusiId(busiBidOpening.getBidOpeningId());
                        saveAttachments(bidOpeningAttachments, first.getChildrens(), processId);
                        break;
                    //评标记录
                    case "busi_bid_evaluation_attachment":
                        //BusiBidEvaluation busiBidEvaluation = bidEvaluationService.selectByProject(info.getProjectId());
                        EvalProjectEvaluationInfo evaluationInfoServiceOne = iEvalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", info.getProjectId()));
                        List<BusiAttachment> bidEvaluationAttachments = attachmentService.selectByBusiId(evaluationInfoServiceOne.getProjectEvaluationId());
                        saveAttachments(bidEvaluationAttachments, first.getChildrens(), processId);
                        break;
                    //成交结果公告
                    case "busi_bidder_notice_attachment":
                        BusiWinningBidderNotice bidderNotice = busiWinningBidderNoticeService.getOne(new QueryWrapper<BusiWinningBidderNotice>().eq("project_id",info.getProjectId()));
                        List<BusiAttachment> bidderNoticeAttachments = attachmentService.selectByBusiId(bidderNotice.getNoticeId());
                        saveAttachments(bidderNoticeAttachments, first.getChildrens(), processId);
                        break;
                    //成交合同
//                    case "busi_expert_transaction_contract_attachment":
//                        BusiExpertTransactionContract busiExpertTransactionContract = busiExpertTransactionContractService.getOne(new QueryWrapper<BusiExpertTransactionContract>().eq("project_id",info.getProjectId()));
//                        List<BusiAttachment> busiContractAttachments = attachmentService.selectByBusiId(busiExpertTransactionContract.getContractId());
//                        saveAttachments(busiContractAttachments, first.getChildrens(), processId);
//                        break;
                    default:
                        break;
                }
            }
            return true;
        }
        return false;
    }

    private void saveAttachments(List<BusiAttachment> attachments, List<BaseTreeData> datas, Long busiId){
        for (BaseTreeData data : datas) {
            for (BusiAttachment busiAttachment : attachments) {
                if (busiAttachment.getFileType().equals(data.getThirdId())) {
                    BusiAttachment attachment = new BusiAttachment();
                    attachment.setBusiId(busiId);
                    attachment.setFileName(busiAttachment.getFileName());
                    attachment.setFileSuffix(busiAttachment.getFileSuffix());
                    attachment.setFilePath(busiAttachment.getFilePath());
                    attachment.setFileType(data.getCode());
                    attachmentService.save(attachment);
                    break;
                }
            }
        }
    }

    @Override
    public List<BaseTreeData> selectProcessAttachment(Long processId) {
        BaseTreeData baseTreeData = new BaseTreeData();
        baseTreeData.setType(10);
        List<BusiAttachment> attachments = attachmentService.selectByBusiId(processId);
        List<BaseTreeData> datas = baseTreeDataService.selectListWithTree(baseTreeData);
        if (!attachments.isEmpty()){
            for (BaseTreeData frist : datas) {
                for (BaseTreeData data : frist.getChildrens()) {
                    for (BusiAttachment attachment : attachments) {
                        if (data.getCode().equals(attachment.getFileType())) {
                            data.setStrField1(attachment.getFilePath());
                            break;
                        }
                    }
                }
            }
        }
        return datas;
    }
}
