package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.busi.domain.BusiTenderDocumentsDownload;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.vo.NoticeInfoAndIdsVo;
import com.ruoyi.busi.domain.vo.ProjectInfoAndIdsVo;
import com.ruoyi.busi.mapper.BusiTenderDocumentsDownloadMapper;
import com.ruoyi.busi.service.IBusiTenderDocumentsDownloadService;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.annotation.DataScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采购文件下载记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
public class BusiTenderDocumentsDownloadServiceImpl extends ServiceImpl<BusiTenderDocumentsDownloadMapper, BusiTenderDocumentsDownload> implements IBusiTenderDocumentsDownloadService {

    @Autowired
    IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    IBusiTenderNoticeService iBusiTenderNoticeService;

    /**
     * 查询采购文件下载记录列表
     *
     * @param busiTenderDocumentsDownload 采购文件下载记录
     * @return 采购文件下载记录
     */
    @Override
    @DataScope(entAlias = "bidder_id")
    public List<BusiTenderDocumentsDownload> selectList(BusiTenderDocumentsDownload busiTenderDocumentsDownload) {
        QueryWrapper<BusiTenderDocumentsDownload> busiTenderDocumentsDownloadQueryWrapper = getBusiTenderDocumentsDownloadQueryWrapper(busiTenderDocumentsDownload);
        busiTenderDocumentsDownloadQueryWrapper .orderByDesc("create_time");
        List<BusiTenderDocumentsDownload> list = list(busiTenderDocumentsDownloadQueryWrapper);
        //if(busiTenderDocumentsDownload.getParams().containsKey("returnProject")){
        if (!list.isEmpty()){
            List<Long> projectIds = list.stream().map(item -> item.getProjectId()).collect(Collectors.toList());
            ProjectInfoAndIdsVo projectsByProjectIds = iBusiTenderProjectService.getProjectsByProjectIds(projectIds);
            Map<Long, BusiTenderProject> projectsMap = projectsByProjectIds.getProjectsMap();

            NoticeInfoAndIdsVo tenderNotisByProjectId = iBusiTenderNoticeService.getTenderNotisByProjectId(projectIds);
            Map<Long, BusiTenderNotice> noticeInfoAndIdsVoMap = tenderNotisByProjectId.getNoticesMap();

            list.forEach(item->{
                item.setProject(projectsMap.get(item.getProjectId()));
                item.setNotice(noticeInfoAndIdsVoMap.get(item.getProjectId()));
            });
        }
       // }
        return list;
    }

    private QueryWrapper<BusiTenderDocumentsDownload> getBusiTenderDocumentsDownloadQueryWrapper(BusiTenderDocumentsDownload busiTenderDocumentsDownload) {
        QueryWrapper<BusiTenderDocumentsDownload> busiTenderDocumentsDownloadQueryWrapper = new QueryWrapper<>();
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getDownloadId()), "download_id", busiTenderDocumentsDownload.getDownloadId());
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getProjectId()), "project_id", busiTenderDocumentsDownload.getProjectId());
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getBidderId()), "bidder_id", busiTenderDocumentsDownload.getBidderId());
        busiTenderDocumentsDownloadQueryWrapper.like(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getBidderName()), "bidder_name", busiTenderDocumentsDownload.getBidderName());
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getBidderCode()), "bidder_code", busiTenderDocumentsDownload.getBidderCode());
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getDownloadIp()), "download_ip", busiTenderDocumentsDownload.getDownloadIp());
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getDownloadTime()), "download_time", busiTenderDocumentsDownload.getDownloadTime());
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getRemark()), "remark", busiTenderDocumentsDownload.getRemark());
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getDelFlag()), "del_flag", busiTenderDocumentsDownload.getDelFlag());
        String beginCreateTime = busiTenderDocumentsDownload.getParams().get("beginCreateTime") != null ? busiTenderDocumentsDownload.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = busiTenderDocumentsDownload.getParams().get("endCreateTime") + "" != null ? busiTenderDocumentsDownload.getParams().get("endCreateTime") + "" : "";
        busiTenderDocumentsDownloadQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getCreateBy()), "create_by", busiTenderDocumentsDownload.getCreateBy());
        String beginUpdateTime = busiTenderDocumentsDownload.getParams().get("beginUpdateTime") != null ? busiTenderDocumentsDownload.getParams().get("beginUpdateTime") + "" : "";
        String endUpdateTime = busiTenderDocumentsDownload.getParams().get("endUpdateTime") + "" != null ? busiTenderDocumentsDownload.getParams().get("endUpdateTime") + "" : "";
        busiTenderDocumentsDownloadQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime, endUpdateTime);
        busiTenderDocumentsDownloadQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getUpdateBy()), "update_by", busiTenderDocumentsDownload.getUpdateBy());
        //已经报名的项目
        //1.已经报名但是没有开标的的
        busiTenderDocumentsDownloadQueryWrapper.orderByAsc(ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getParams().get("orderByAsc")),busiTenderDocumentsDownload.getParams().get("orderByAsc")+"");

        busiTenderDocumentsDownloadQueryWrapper.apply(
                ObjectUtil.isNotEmpty(busiTenderDocumentsDownload.getParams().get("dataScope"))
                        && (!busiTenderDocumentsDownload.getParams().containsKey("isScope") || Boolean.parseBoolean(busiTenderDocumentsDownload.getParams().get("isScope").toString())),
                busiTenderDocumentsDownload.getParams().get("dataScope") + ""
        );

        return busiTenderDocumentsDownloadQueryWrapper;
    }
}
