package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.enums.ProcessEnum;
import com.ruoyi.busi.mapper.BusiTenderIntentionMapper;
import com.ruoyi.busi.service.IBusiAttachmentService;
import com.ruoyi.busi.service.IBusiTenderIntentionService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.enums.DelFlagStatus;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.enums.ZcxhEnum;
import com.ruoyi.utils.AttachmentUtil;
import com.ruoyi.utils.BaseUtil;
import com.ruoyi.utils.PdfUtil;
import com.ruoyi.utils.ZcxhUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采购意向Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BusiTenderIntentionServiceImpl extends ServiceImpl<BusiTenderIntentionMapper, BusiTenderIntention> implements IBusiTenderIntentionService {

    @Autowired
    private IBusiTenderIntentionService iBusiTenderIntentionService;
    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;
    @Autowired
    private IBaseEntInfoService iBaseEntInfoService;
    @Autowired
    private IBusiTenderProjectService tenderProjectService;

    /**
     * 查询采购意向列表
     *
     * @param busiTenderIntention 采购意向
     * @return 采购意向
     */
    @Override
    @DataScope(entAlias = "tenderer_id")
    public List<BusiTenderIntention> selectList(BusiTenderIntention busiTenderIntention) {
        QueryWrapper<BusiTenderIntention> busiTenderIntentionQueryWrapper = getBusiTenderIntentionQueryWrapper(busiTenderIntention);
        busiTenderIntentionQueryWrapper.orderByDesc("create_time");
        List<BusiTenderIntention> list = list(busiTenderIntentionQueryWrapper);
        if (!list.isEmpty()){
            List<Long> entIds = list.stream().map(item -> item.getTendererId()).collect(Collectors.toList());
            List<BaseEntInfo> entList = iBaseEntInfoService.getByIds(entIds);
            Map<Long, BaseEntInfo> map = entList.stream()
                    .collect(Collectors.toMap(
                            BaseEntInfo::getEntId,
                            result -> result,
                            (existing, replacement) -> existing)); // 在键冲突时保留现有的元素
            list.forEach(item->{
                item.setTendererName(map.get(item.getTendererId()).getEntName());
            });
        }
        return list;
    }

    private static QueryWrapper<BusiTenderIntention> getBusiTenderIntentionQueryWrapper(BusiTenderIntention busiTenderIntention) {
        QueryWrapper<BusiTenderIntention> busiTenderIntentionQueryWrapper = new QueryWrapper<>();
        busiTenderIntentionQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderIntention.getIntentionCode()), "intention_code", busiTenderIntention.getIntentionCode());
        busiTenderIntentionQueryWrapper.like(ObjectUtil.isNotEmpty(busiTenderIntention.getIntentionName()), "intention_name", busiTenderIntention.getIntentionName());
        busiTenderIntentionQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderIntention.getIntentionContent()), "intention_content", busiTenderIntention.getIntentionContent());
        busiTenderIntentionQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderIntention.getBudgetAmount()), "budget_amount", busiTenderIntention.getBudgetAmount());
        busiTenderIntentionQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderIntention.getProjectDuration()), "project_duration", busiTenderIntention.getProjectDuration());
        busiTenderIntentionQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderIntention.getTenderMode()), "tender_mode", busiTenderIntention.getTenderMode());
        String beginIntentionStartTime = busiTenderIntention.getParams().get("beginIntentionStartTime") != null ? busiTenderIntention.getParams().get("beginIntentionStartTime") + "" : "";
        String endIntentionStartTime = busiTenderIntention.getParams().get("endIntentionStartTime") + "" != null ? busiTenderIntention.getParams().get("endIntentionStartTime") + "" : "";
        busiTenderIntentionQueryWrapper.between(ObjectUtil.isNotEmpty(beginIntentionStartTime) && ObjectUtil.isNotEmpty(endIntentionStartTime), "intention_start_time", beginIntentionStartTime, endIntentionStartTime);
        String beginIntentionEndTime = busiTenderIntention.getParams().get("beginIntentionEndTime") != null ? busiTenderIntention.getParams().get("beginIntentionEndTime") + "" : "";
        String endIntentionEndTime = busiTenderIntention.getParams().get("endIntentionEndTime") + "" != null ? busiTenderIntention.getParams().get("endIntentionEndTime") + "" : "";
        busiTenderIntentionQueryWrapper.between(ObjectUtil.isNotEmpty(beginIntentionEndTime) && ObjectUtil.isNotEmpty(endIntentionEndTime), "intention_end_time", beginIntentionEndTime, endIntentionEndTime);
        busiTenderIntentionQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderIntention.getDelFlag()), "del_flag", busiTenderIntention.getDelFlag());
        String beginCreateTime = busiTenderIntention.getParams().get("beginCreateTime") != null ? busiTenderIntention.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = busiTenderIntention.getParams().get("endCreateTime") + "" != null ? busiTenderIntention.getParams().get("endCreateTime") + "" : "";
        busiTenderIntentionQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        busiTenderIntentionQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderIntention.getCreateBy()), "create_by", busiTenderIntention.getCreateBy());
        String beginUpdateTime = busiTenderIntention.getParams().get("beginUpdateTime") != null ? busiTenderIntention.getParams().get("beginUpdateTime") + "" : "";
        String endUpdateTime = busiTenderIntention.getParams().get("endUpdateTime") + "" != null ? busiTenderIntention.getParams().get("endUpdateTime") + "" : "";
        busiTenderIntentionQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime, endUpdateTime);
        busiTenderIntentionQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderIntention.getUpdateBy()), "update_by", busiTenderIntention.getUpdateBy());
        if(busiTenderIntention.getParams().containsKey("addProject") && "true".equals(busiTenderIntention.getParams().get("addProject").toString())){
            if(busiTenderIntention.getParams().containsKey("dataScope")){
                busiTenderIntention.getParams().remove("dataScope");
                busiTenderIntentionQueryWrapper.eq("tenderer_id", SecurityUtils.getLoginUser().getEntId());
            }
            busiTenderIntentionQueryWrapper.notInSql("intention_id", "select project_intention_id from busi_tender_project where  del_flag=0 AND project_intention_id is not null");
        }

        BaseUtil.checkUser(busiTenderIntentionQueryWrapper, busiTenderIntention);
        busiTenderIntentionQueryWrapper.notIn(
                ObjectUtil.isNotEmpty(busiTenderIntention.getParams().get("notInIntentionIds")),
                "intention_id",
                (List)busiTenderIntention.getParams().get("notInIntentionIds")
        );
        busiTenderIntentionQueryWrapper.le(
                ObjectUtil.isNotEmpty(busiTenderIntention.getParams().get("leIntentionEndTime")),
                "intention_end_time",
                busiTenderIntention.getParams().get("leIntentionEndTime")
        );
        busiTenderIntentionQueryWrapper.ge(ObjectUtil.isNotEmpty(busiTenderIntention.getParams().get("geIntentionEndTime")),"intention_end_time", busiTenderIntention.getParams().get("geIntentionEndTime"));
        busiTenderIntentionQueryWrapper.orderByAsc(ObjectUtil.isNotEmpty(busiTenderIntention.getParams().get("orderByAsc")),busiTenderIntention.getParams().get("orderByAsc")+"");
        return busiTenderIntentionQueryWrapper;
    }

    @Transactional
    @Override
    public AjaxResult saveBusiTenderIntention(BusiTenderIntention busiTenderIntention) {
        boolean save = save(busiTenderIntention);

        if (!save) {
            throw  new RuntimeException("采购意向信息保存失败！");
        }
        List<BusiAttachment> attachments = busiTenderIntention.getAttachments();
        if (attachments.size()>0){
            List<BusiAttachment> batchSaveList = attachments.stream().map(item -> {
                item.setBusiId(busiTenderIntention.getIntentionId());
                item.setDelFlag(DelFlagStatus.OK.getCode());
                item.setCreateBy(busiTenderIntention.getCreateBy());
                item.setUpdateBy(busiTenderIntention.getUpdateBy());
                item.setUpdateTime(busiTenderIntention.getUpdateTime());
                item.setCreateTime(busiTenderIntention.getCreateTime());
                return item;
            }).collect(Collectors.toList());
            boolean b = iBusiAttachmentService.saveBatch(batchSaveList);
            if (!b) {
                throw  new RuntimeException("采购意向附件信息保存失败！");
            }
        }
        try {
            push2ZCXH(busiTenderIntention);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success("采购意向信息保存成功");
    }

    @Transactional
    @Override
    public AjaxResult editBusiTenderIntention(BusiTenderIntention busiTenderIntention) {
        boolean updateById = updateById(busiTenderIntention);
        if (!updateById) {
            return AjaxResult.error("采购意向信息更新失败！");
        }
        //附件管理
        List<BusiAttachment> newAttachments = busiTenderIntention.getAttachments().stream().map(item -> {
            item.setBusiId(busiTenderIntention.getIntentionId());
            return item;
        }).collect(Collectors.toList());
        iBusiAttachmentService.remove(new QueryWrapper<BusiAttachment>().eq("busi_id", busiTenderIntention.getIntentionId()));
        iBusiAttachmentService.saveOrUpdateBatch(newAttachments);
//        List<BusiAttachment> batchSaveList = attachments.stream().map(item -> {
//            item.setBusiId(busiTenderIntention.getIntentionId());
//            item.setDelFlag(DelFlagStatus.OK.getCode());
//            item.setCreateBy(busiTenderIntention.getCreateBy());
//            item.setUpdateBy(busiTenderIntention.getUpdateBy());
//            item.setUpdateTime(busiTenderIntention.getUpdateTime());
//            item.setCreateTime(busiTenderIntention.getCreateTime());
//            return item;
//        }).collect(Collectors.toList());
//        boolean b = iBusiAttachmentService.saveBatch(batchSaveList);
//        if (!b) {
//            return AjaxResult.error("采购意向附件信息保存失败！");
//        }
        return AjaxResult.success("采购意向信息更新成功！");
    }

    @Override
    public BusiTenderIntention selectByProject(Long tenderProjectId) {
        return baseMapper.selectByProject(tenderProjectId);
    }

    @Override
    @DataScope(entAlias = "tenderer_id")
    public int selectCount(BusiTenderIntention intentionQuery) {
        return count(getBusiTenderIntentionQueryWrapper(intentionQuery));
    }

    @Override
    public AjaxResult getBusiTenderIntentionById(Long intentionId) {
        BusiTenderIntention byId = iBusiTenderIntentionService.getById(intentionId);
        byId.setAttachments(iBusiAttachmentService.getByBusiId(byId.getIntentionId()));
        return AjaxResult.success(byId);
    }

    @Resource
    private PdfUtil pdfUtil;
    @Autowired
    private AttachmentUtil attachmentUtil;
    @Autowired
    private ZcxhUtil zcxhUtil;
    public void push2ZCXH(BusiTenderIntention busiTenderIntention){
        Map<String, Object> map = new HashMap<>();
        map.put("busiTenderIntention", busiTenderIntention);
        map.put("tenderer", SecurityUtils.getLoginUser().getUser().getEnt());
        map.put("fileItems", attachmentUtil.getAttachmentList(busiTenderIntention.getIntentionId(), ProcessEnum.TENDER_INTENTION.getAttachmentCode()));
        String ftlPath = "portal_templates/采购意向.ftl";
        try {
            String baseContent = pdfUtil.toHtml(map, ftlPath);
            busiTenderIntention.setBaseContent(baseContent);
            updateById(busiTenderIntention);

            JSONObject sendData = new JSONObject();
            sendData.put("noticeContent", busiTenderIntention.getBaseContent());
            sendData.put("noticeTitle", busiTenderIntention.getIntentionName());
            sendData.put("purchasingUnit", SecurityUtils.getLoginUser().getUser().getEnt().getEntName());
            sendData.put("noticeSource", "限额以下");
            sendData.put("noticeType", "采购意向");
            sendData.put("publishingTime", busiTenderIntention.getCreateTime().getTime());
            sendData.put("thirdId", busiTenderIntention.getIntentionId());
            sendData.put("tenderType", "");
            sendData.put("projetIndustry", "");
            sendData.put("noticeUrl", "https://xeyx.hbszfcgxh.com:10443");
            zcxhUtil.send2Zcxh(sendData, ZcxhEnum.NOTICE.getUrl());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
