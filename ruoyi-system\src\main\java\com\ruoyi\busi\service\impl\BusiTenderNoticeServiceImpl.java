package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.base.service.IBaseTreeDataService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.domain.vo.BusiTenderVo;
import com.ruoyi.busi.domain.vo.NoticeInfoAndIdsVo;
import com.ruoyi.busi.domain.vo.ProjectInfoAndIdsVo;
import com.ruoyi.busi.enums.ProcessEnum;
import com.ruoyi.busi.mapper.BusiTenderNoticeMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.DelFlagStatus;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.enums.ZcxhEnum;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.utils.AttachmentUtil;
import com.ruoyi.utils.BaseUtil;
import com.ruoyi.utils.PdfUtil;
import com.ruoyi.utils.ZcxhUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 采购公告信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BusiTenderNoticeServiceImpl extends ServiceImpl<BusiTenderNoticeMapper, BusiTenderNotice> implements IBusiTenderNoticeService {
    @Autowired
    private IBusiBiddingRecordService busiBiddingRecordService;
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;
    @Autowired
    private IBusiVenueOccupyService busiVenueOccupyService;
    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    private IBaseTreeDataService baseTreeDataService;
    @Autowired
    private IBaseEntInfoService baseEntInfoService;
    @Autowired
    private AttachmentUtil attachmentUtil;
    @Autowired
    private ZcxhUtil zcxhUtil;
    /**
     * 查询采购公告信息列表
     *
     * @param busiTenderNotice 采购公告信息
     * @return 采购公告信息
     */
    @Override
    public List<BusiTenderNotice> selectList(BusiTenderNotice busiTenderNotice) {

        QueryWrapper<BusiTenderNotice> busiTenderNoticeQueryWrapper = getBusiTenderNoticeQueryWrapper(busiTenderNotice);
        List<BusiTenderNotice> list = list(busiTenderNoticeQueryWrapper);
        if(busiTenderNotice.getParams().containsKey("returnProject")){
                list.forEach(item->{
                    item.setProject(iBusiTenderProjectService.getById(item.getProjectId()));
                });
        }
        return list;
    }

    private QueryWrapper<BusiTenderNotice> getBusiTenderNoticeQueryWrapper(BusiTenderNotice busiTenderNotice) {
        QueryWrapper<BusiTenderNotice> busiTenderNoticeQueryWrapper = new QueryWrapper<>();
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getProjectId()), "project_id", busiTenderNotice.getProjectId());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getNoticeCode()), "notice_code", busiTenderNotice.getNoticeCode());
        busiTenderNoticeQueryWrapper.like(ObjectUtil.isNotEmpty(busiTenderNotice.getNoticeName()), "notice_name", busiTenderNotice.getNoticeName());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getNoticeContent()), "notice_content", busiTenderNotice.getNoticeContent());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getNoticeType()), "notice_type", busiTenderNotice.getNoticeType());
        String beginNoticeStartTime = busiTenderNotice.getParams().get("beginNoticeStartTime") != null ? busiTenderNotice.getParams().get("beginNoticeStartTime") + "" : "";
        String endNoticeStartTime = busiTenderNotice.getParams().get("endNoticeStartTime") + "" != null ? busiTenderNotice.getParams().get("endNoticeStartTime") + "" : "";
        busiTenderNoticeQueryWrapper.between(ObjectUtil.isNotEmpty(beginNoticeStartTime) && ObjectUtil.isNotEmpty(endNoticeStartTime), "notice_start_time", beginNoticeStartTime, endNoticeStartTime);
        String beginNoticeEndTime = busiTenderNotice.getParams().get("beginNoticeEndTime") != null ? busiTenderNotice.getParams().get("beginNoticeEndTime") + "" : "";
        String endNoticeEndTime = busiTenderNotice.getParams().get("endNoticeEndTime") + "" != null ? busiTenderNotice.getParams().get("endNoticeEndTime") + "" : "";
        busiTenderNoticeQueryWrapper.between(ObjectUtil.isNotEmpty(beginNoticeEndTime) && ObjectUtil.isNotEmpty(endNoticeEndTime), "notice_end_time", beginNoticeEndTime, endNoticeEndTime);
        String beginBidOpeningTime = busiTenderNotice.getParams().get("beginBidOpeningTime") != null ? busiTenderNotice.getParams().get("beginBidOpeningTime") + "" : "";
        String endBidOpeningTime = busiTenderNotice.getParams().get("endBidOpeningTime") + "" != null ? busiTenderNotice.getParams().get("endBidOpeningTime") + "" : "";
        busiTenderNoticeQueryWrapper.between(ObjectUtil.isNotEmpty(beginBidOpeningTime) && ObjectUtil.isNotEmpty(endBidOpeningTime), "bid_opening_time", beginBidOpeningTime, endBidOpeningTime);
        String beginBidOpeningEndTime = busiTenderNotice.getParams().get("beginBidOpeningEndTime") != null ? busiTenderNotice.getParams().get("beginBidOpeningEndTime") + "" : "";
        String endBidOpeningEndTime = busiTenderNotice.getParams().get("endBidOpeningEndTime") + "" != null ? busiTenderNotice.getParams().get("endBidOpeningEndTime") + "" : "";
        busiTenderNoticeQueryWrapper.between(ObjectUtil.isNotEmpty(beginBidOpeningEndTime) && ObjectUtil.isNotEmpty(endBidOpeningEndTime), "bid_opening_end_time", beginBidOpeningEndTime, endBidOpeningEndTime);
        String beginBidEvaluationTime = busiTenderNotice.getParams().get("beginBidEvaluationTime") != null ? busiTenderNotice.getParams().get("beginBidEvaluationTime") + "" : "";
        String endBidEvaluationTime = busiTenderNotice.getParams().get("endBidEvaluationTime") + "" != null ? busiTenderNotice.getParams().get("endBidEvaluationTime") + "" : "";
        busiTenderNoticeQueryWrapper.between(ObjectUtil.isNotEmpty(beginBidEvaluationTime) && ObjectUtil.isNotEmpty(endBidEvaluationTime), "bid_evaluation_time", beginBidEvaluationTime, endBidEvaluationTime);
        String beginBidEvaluationEndTime = busiTenderNotice.getParams().get("beginBidEvaluationEndTime") != null ? busiTenderNotice.getParams().get("beginBidEvaluationEndTime") + "" : "";
        String endBidEvaluationEndTime = busiTenderNotice.getParams().get("endBidEvaluationEndTime") + "" != null ? busiTenderNotice.getParams().get("endBidEvaluationEndTime") + "" : "";
        busiTenderNoticeQueryWrapper.between(ObjectUtil.isNotEmpty(beginBidEvaluationEndTime) && ObjectUtil.isNotEmpty(endBidEvaluationEndTime), "bid_evaluation_end_time", beginBidEvaluationEndTime, endBidEvaluationEndTime);
        String beginProjectStartTime = busiTenderNotice.getParams().get("beginProjectStartTime") != null ? busiTenderNotice.getParams().get("beginProjectStartTime") + "" : "";
        String endProjectStartTime = busiTenderNotice.getParams().get("endProjectStartTime") + "" != null ? busiTenderNotice.getParams().get("endProjectStartTime") + "" : "";
        busiTenderNoticeQueryWrapper.between(ObjectUtil.isNotEmpty(beginProjectStartTime) && ObjectUtil.isNotEmpty(endProjectStartTime), "project_start_time", beginProjectStartTime, endProjectStartTime);
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getProjectDeadline()), "project_deadline", busiTenderNotice.getProjectDeadline());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getAllowCoalition()), "allow_coalition", busiTenderNotice.getAllowCoalition());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getToSme()), "to_sme", busiTenderNotice.getToSme());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getBidOpeningMode()), "bid_opening_mode", busiTenderNotice.getBidOpeningMode());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getBidEvaluationMode()), "bid_evaluation_mode", busiTenderNotice.getBidEvaluationMode());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getNoticeStats()), "notice_stats", busiTenderNotice.getNoticeStats());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getChangeNum()), "change_num", busiTenderNotice.getChangeNum());
        busiTenderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderNotice.getPriceUnit()), "price_unit", busiTenderNotice.getPriceUnit());
        busiTenderNoticeQueryWrapper.in(ObjectUtil.isNotEmpty(busiTenderNotice.getParams().get("inProjectIds")), "project_id", (List) busiTenderNotice.getParams().get("inProjectIds"));
        busiTenderNoticeQueryWrapper.le(ObjectUtil.isNotEmpty(busiTenderNotice.getParams().get("leNoticeEndTime")), "notice_end_time", busiTenderNotice.getParams().get("leNoticeEndTime"));
        //待开标
        //开标时间大于当前时间
        busiTenderNoticeQueryWrapper.ge(busiTenderNotice.getParams().containsKey("geBidOpeningTime"), "bid_opening_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        //获取某一节点前的所有公告信息
        busiTenderNoticeQueryWrapper.inSql(busiTenderNotice.getParams().containsKey("projectStatus"), "project_id","select project_id from busi_tender_project where project_status >= 0 and project_status < "+busiTenderNotice.getParams().get("projectStatus")+"");
        //开标时间排序
        if(busiTenderNotice.getParams().containsKey("orderByAsc")) {
            busiTenderNoticeQueryWrapper.orderByAsc(busiTenderNotice.getParams().containsKey("orderByAsc"), busiTenderNotice.getParams().get("orderByAsc") + "");
        }else{
            busiTenderNoticeQueryWrapper.orderByDesc("create_time");
        }
        String inviteBidder = (String) busiTenderNotice.getParams().get("inviteBidder");
        if(StringUtils.isNotEmpty(inviteBidder)){
            busiTenderNoticeQueryWrapper.and(w->{
                w.isNull("invite_bidder").or().eq("invite_bidder","").or().apply("FIND_IN_SET({0}, invite_bidder)", inviteBidder);
            });
        }
//        else{
//            busiTenderNoticeQueryWrapper.and(w->{
//              w.isNull("invite_bidder").or().eq("invite_bidder","");
//            });
//        }

//        if(busiTenderNotice.getParams().containsKey("inviteBidder")) {
//            //首页展示只展示null的
//            if ("1".equals(busiTenderNotice.getParams().get("inviteBidder").toString())){
//                busiTenderNoticeQueryWrapper.isNull("invite_bidder");
//            }else {
//                //如果是供应商搜索能看到null和invite_bidder字段包含自己的
//                if (StringUtils.isNotBlank(busiTenderNotice.getParams().get("inviteBidder").toString())) {
//                    busiTenderNoticeQueryWrapper.like("invite_bidder", busiTenderNotice.getParams().get("inviteBidder").toString()).or().isNull("invite_bidder");
//                }
//                //busiTenderNoticeQueryWrapper.like("invite_bidder", busiTenderNotice.getParams().get("inviteBidder").toString());
//            }
//        }
        /*if(busiTenderNotice.getParams().containsKey("inviteBidder")) {
            if (StringUtils.isNotBlank(busiTenderNotice.getParams().get("inviteBidder").toString())) {
                busiTenderNoticeQueryWrapper.like("invite_bidder", busiTenderNotice.getParams().get("inviteBidder").toString());
            }
        }else{
            busiTenderNoticeQueryWrapper.isNull("invite_bidder");
        }*/

//        LoginUser user = SecurityUtils.getLoginUser();
//        //List<Long> longs = new ArrayList<>(Arrays.asList(user.getUser().getRoleIds()));
//        List<SysRole> sysRoles = sysRoleService.selectRolesByUserId(user.getUserId());
//        user.getUser().setRoles(sysRoles);
        BaseUtil.checkUser(busiTenderNoticeQueryWrapper, busiTenderNotice);

        return busiTenderNoticeQueryWrapper;
    }

    @Transactional
    @Override
    public AjaxResult updateAnnouncementInfo(BusiTenderNotice busiTenderNotice) {

        return AjaxResult.success("直接发布");
    }
    @Transactional( rollbackFor = {Exception.class})
    @Override
    public AjaxResult changeAnnouncementInfo(BusiTenderNotice busiTenderNotice) throws Exception {
//        List<BusiTenderNotice> tenderNotices = busiTenderNoticeService.list(new QueryWrapper<BusiTenderNotice>().eq("project_id", busiTenderNotice.getProjectId()));
//        if (!tenderNotices.isEmpty()) {
//            List<Long> noticeIds = tenderNotices.stream()
//                    .map(BusiTenderNotice::getNoticeId)
//                    .collect(Collectors.toList());
//            boolean notice_id = busiVenueOccupyService.remove(new QueryWrapper<BusiVenueOccupy>().in("notice_id", noticeIds));
//        }

//        iBusiAttachmentService.remove(new QueryWrapper<BusiAttachment>().eq("busi_id",busiTenderNotice.getNoticeId()));
//        for (BusiAttachment attachment : busiTenderNotice.getAttachments()) {
//            attachment.setAttachmentId(null);
//        }

        //查询是否小于更公告时间规定公示时间
//        busiTenderNotice.setNoticeId(null);
//        busiTenderNotice.setChangeNum(busiTenderNotice.getChangeNum() + 1);
        //规定公示时间
        String blackStr = configService.selectConfigByKey("sys.publicity.time");
        // 获取开标时间
        Date bidOpeningTime = busiTenderNotice.getBidOpeningTime();
        // 获取当前时间
        Date currentTime = new Date();
        // 计算开标时间和当前时间的日期差
        long diffDays = (bidOpeningTime.getTime() - currentTime.getTime()) / (1000 * 60 * 60 * 24);
        // 判断日期差是否小于
        if (diffDays < Integer.parseInt(blackStr)) {
            //如小于规定时间则必须同步修改开评标预约时间，预约时间不得小于变更公告时间+规定公示时间。
            throw new Exception("开标时间不能小于变更公告时间规定公示时间");
        }
//        if (busiTenderNotice.getNoticeStats()==1){
//            //变更公告时,先将现有公告逻辑删除
//            QueryWrapper<BusiTenderNotice> removeWrapper = new QueryWrapper<>();
//            removeWrapper.eq("project_id", busiTenderNotice.getProjectId());
//            busiTenderNoticeService.remove(removeWrapper);
//        }
//        busiTenderNotice.setNoticeId(null);
     /*   List<BusiVenueOccupy> busiVenueOccupys = busiTenderNotice.getBusiVenueOccupys();
        for (BusiVenueOccupy busiVenueOccupy : busiVenueOccupys) {
            busiVenueOccupy.setOccupyId(null);
        }
        busiTenderNotice.setCreateTime(new Date());
        busiTenderNotice.setUpdateTime(new Date());*/
        saveTenderNoticeAttachment(busiTenderNotice);
        return AjaxResult.success("采购公告信息变更成功！");
    }

    @Transactional( rollbackFor = {Exception.class})
    @Override
    public AjaxResult saveTenderNoticeAttachment(BusiTenderNotice busiTenderNotice) throws Exception {
        //修改项目状态为20
        BusiTenderProject project = iBusiTenderProjectService.getById(busiTenderNotice.getProjectId());
        if (project!=null && busiTenderNotice.getNoticeStats()==1){
            project.setProjectStatus(20);
            iBusiTenderProjectService.updateById(project);
        }
//        if(busiTenderNotice.getVenueType()==1) {
//            List<BusiVenueOccupy> busiVenueOccupys = busiTenderNotice.getBusiVenueOccupys();
//            if(null!=busiVenueOccupys  && busiVenueOccupys.size()>0){
//                List<BusiVenueOccupy> filteredList = busiVenueOccupys.stream()
//                        .filter(busiVenueOccupy -> busiVenueOccupy.getVenueType() == 1)
//                        .collect(Collectors.toList());
//                busiTenderNotice.setBidOpeningTime(filteredList.get(0).getOccupyStartTime());
//            }
//        busiTenderNotice.setBidOpeningEndTime(filteredList.get(0).getOccupyEndTime());
//        }
        saveOrUpdate(busiTenderNotice);
        //暂存-1 保存0 发布1
        //判断是否为修改
        BusiTenderNotice oldTenderNotice = null;
        if(busiTenderNotice.getNoticeStats() == -1){
            BusiTenderNotice old = getById(busiTenderNotice.getPId());
            if (old!=null && old.getNoticeStats() == 1 ) {
                //删除采购公告文件
                iBusiAttachmentService.deleteByBusiId(busiTenderNotice.getNoticeId());
                //删除场地占用
                busiVenueOccupyService.deleteByNoticeId(busiTenderNotice.getNoticeId());
//                return AjaxResult.success("采购公告信息修改成功！",busiTenderNotice);
                busiTenderNoticeService.removeById(old.getNoticeId());
            }
        }
        //保存附件
        attachmentUtil.addNewAttachments(busiTenderNotice, "'0','5'");
        if (busiTenderNotice.getNoticeStats()==0){
            if (busiTenderNotice.getPId()!=null && busiTenderNotice.getNoticeType() == 2) {
                BusiTenderNotice oldNoticeQuery = new BusiTenderNotice();
                oldNoticeQuery.setNoticeId(busiTenderNotice.getPId());
                oldTenderNotice = getOneIgnoreDeleted(oldNoticeQuery);
                if(oldTenderNotice!=null){
                    removeById(busiTenderNotice.getPId());
                }
                BusiVenueOccupy query2 = new BusiVenueOccupy();
                query2.setNoticeId(oldTenderNotice.getNoticeId());
                List<BusiVenueOccupy> occupyList2 = busiVenueOccupyService.getListIgnoreDeleted(query2);
                for (BusiVenueOccupy occupy : occupyList2) {
                    if (occupy.getVenueType() == 1) {
                        oldTenderNotice.setBidOpening(occupy);
                    }else{
                        oldTenderNotice.setBidEvaluation(occupy);
                    }
                    busiVenueOccupyService.removeById(occupy.getOccupyId());
                }
            }
            if(busiTenderNotice.getVenueType()==1) {
                //保存场地占用记录
                for (BusiVenueOccupy busiVenueOccupy : busiTenderNotice.getBusiVenueOccupys()) {
                    //新增场地占用记录
                    busiVenueOccupy.setNoticeId(busiTenderNotice.getNoticeId());
                    //场地类型 (1开标 2评标)
                    if (busiVenueOccupy.getVenueType() == 1) {
                        //发布时查询开标时间是否被占用
                        QueryWrapper<BusiVenueOccupy> queryWrapper = new QueryWrapper<BusiVenueOccupy>();
                        //queryWrapper.eq("del_flag", 0);
                        //queryWrapper.eq("venue_type", busiVenueOccupy.getVenueType());
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        //开始时间
                        String formattedDate = dateFormat.format(busiVenueOccupy.getOccupyStartTime());
                        //结束时间
                        String formattedDateEnd = dateFormat.format(busiVenueOccupy.getOccupyEndTime());
//                        queryWrapper
//                                .and(i -> i.eq("venue_id", busiVenueOccupy.getVenueId())) // AND venue_id = ?
//                                .nested(i -> i
//                                        .le("occupy_start_time", formattedDate)
//                                        .ge("occupy_end_time", formattedDateEnd)
//                                )
//                                .or()
//                                .nested(i -> i
//                                        .between("occupy_start_time", formattedDate, formattedDateEnd)
//                                )
//                                .or()
//                                .nested(i -> i
//                                        .between("occupy_end_time", formattedDate, formattedDateEnd)
//                                )
//                              ;

                        queryWrapper.eq("del_flag", 0) // WHERE del_flag = 0
                                .and(i -> i.eq("venue_id", busiVenueOccupy.getVenueId())) // AND ( venue_id = 1108023367795717 )
                                .ne(ObjectUtil.isNotEmpty(busiTenderNotice.getNoticeId()), "notice_id",busiTenderNotice.getNoticeId())
                                .and(i -> i
                                        .eq("del_flag", 0) // AND del_flag = 0

                                        .eq("venue_type", busiVenueOccupy.getVenueType()) // AND venue_type = 1
                                        .nested(j -> j
                                                .apply("(occupy_start_time < {0} AND occupy_end_time > {1})", formattedDate, formattedDateEnd)
                                                .or()
                                                .apply("(occupy_start_time > {0} AND occupy_start_time < {1})", formattedDate, formattedDateEnd)
                                                .or()
                                                .apply("(occupy_end_time > {0} AND occupy_end_time < {1})", formattedDate, formattedDateEnd)
                                        )
                                );
                        List<BusiVenueOccupy> occupiedRecords = busiVenueOccupyService.list(queryWrapper);
                        boolean isOccupied = !occupiedRecords.isEmpty();
                        if (isOccupied) {
                            // 时间段被占用
                            throw new Exception("开标室占用时间冲突");
                        }
                        busiTenderNotice.setBidOpening(busiVenueOccupy);
                    } else if (busiVenueOccupy.getVenueType() == 2) {
                        QueryWrapper<BusiVenueOccupy> queryWrapper = new QueryWrapper<BusiVenueOccupy>();
                        queryWrapper.eq("del_flag", 0);
                        queryWrapper.eq("venue_type", busiVenueOccupy.getVenueType());
                        //queryWrapper.eq("bid_evaluation_period",busiTenderNotice.getBidEvaluationPeriod());
                        busiVenueOccupy.setOccupyStartTime(busiTenderNotice.getBidEvaluationTime());
                        List<Integer> statusList = new ArrayList<>();
                        if (busiVenueOccupy.getBidEvaluationPeriod() == 0) {
                            statusList.add(2);
                            statusList.add(0);
                        } else if (busiVenueOccupy.getBidEvaluationPeriod() == 1) {
                            statusList.add(2);
                            statusList.add(1);
                        } else if (busiVenueOccupy.getBidEvaluationPeriod() == 2) {
                            statusList.add(0);
                            statusList.add(1);
                            statusList.add(2);
                        }
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        String formattedDate = dateFormat.format(busiTenderNotice.getBidEvaluationTime());
                        queryWrapper.in("bid_evaluation_period", statusList);
                        queryWrapper.eq("occupy_start_time", formattedDate);
                        queryWrapper.ne(ObjectUtil.isNotEmpty(busiTenderNotice.getNoticeId()), "notice_id",busiTenderNotice.getNoticeId());
                        queryWrapper.eq("venue_id", busiVenueOccupy.getVenueId());
                        List<BusiVenueOccupy> one = busiVenueOccupyService.list(queryWrapper);
                        if (null != one && !one.isEmpty()) {
                            throw new Exception("评标室占用时间冲突");
                        }
                    }
                    if (busiVenueOccupy.getNoticeId() != null && busiTenderNotice.getPId() != null && busiVenueOccupy.getNoticeId().equals(busiTenderNotice.getPId())) {
                        busiVenueOccupy.setOccupyId(null);
                    }
                    boolean busiVenueOccupySave = busiVenueOccupyService.saveOrUpdate(busiVenueOccupy);
                    busiTenderNotice.setBidEvaluation(busiVenueOccupy);

                    if (!busiVenueOccupySave) {
                        throw new RuntimeException("场地占用失败！");
                    }
                }
            } else if (busiTenderNotice.getVenueType() == 2) {

                BusiVenueOccupy bidOpeningVenue = busiTenderNotice.getBusiVenueOccupys().get(0);
                if(busiTenderNotice.getPId()!=null && bidOpeningVenue.getNoticeId().equals(busiTenderNotice.getPId())){
                    bidOpeningVenue.setOccupyId(null);
                }
                bidOpeningVenue.setNoticeId(busiTenderNotice.getNoticeId());
                bidOpeningVenue.setVenueType(1);
                bidOpeningVenue.setVenueName(SecurityUtils.getLoginUser().getUser().getEnt().getEntName()+" 开标室");
                busiVenueOccupyService.saveOrUpdate(bidOpeningVenue);
                BusiVenueOccupy bidEvaluationVenue = busiTenderNotice.getBusiVenueOccupys().get(1);
                if(busiTenderNotice.getPId()!=null && bidEvaluationVenue.getNoticeId().equals(busiTenderNotice.getPId())){
                    bidEvaluationVenue.setOccupyId(null);
                }
                bidEvaluationVenue.setNoticeId(busiTenderNotice.getNoticeId());
                bidEvaluationVenue.setVenueType(2);
                bidEvaluationVenue.setRemark(bidOpeningVenue.getRemark());
                bidEvaluationVenue.setVenueName(SecurityUtils.getLoginUser().getUser().getEnt().getEntName()+" 评标室");
                busiVenueOccupyService.saveOrUpdate(bidEvaluationVenue);
            }
        }

        project.setTenderModeName(sysDictDataService.selectDictLabel("busi_tender_mode", project.getTenderMode()));
        project.setProjectTypeName(sysDictDataService.selectDictLabel("busi_project_type", project.getProjectType()));
        if(StringUtils.isNoneBlank(project.getTenderFundSource())) {
            project.setTenderFundSourceName(sysDictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
        }
        //判断如果发布并且是公开征集的 推送到政采协会
        if (busiTenderNotice.getNoticeStats() == 1 && (StringUtils.isBlank(busiTenderNotice.getInviteBidder()) || !busiTenderNotice.getInviteBidder().equals(""))) {
            //根据模板生成公告内容
            busiTenderNotice.setNoticeContent(getMainContent(project, busiTenderNotice, oldTenderNotice));
            //推送至政采协会
            if(project.getGetBidderMode()==1) {
                pushZbgg2Zcxh(project, busiTenderNotice);
            }
        }
        saveOrUpdate(busiTenderNotice);
        return   AjaxResult.success("采购公告信息保存成功！",busiTenderNotice);
    }

    private void pushZbgg2Zcxh(BusiTenderProject project, BusiTenderNotice busiTenderNotice){
        //推送至政采协会
        if (StringUtils.isNoneBlank(project.getProjectIndustry())) {
            String[] industrys = project.getProjectIndustry().split(",");
            String lastIndustry = industrys[industrys.length - 1];
            BaseTreeData tree = baseTreeDataService.getById(Long.valueOf(lastIndustry));
            project.setProjectIndustryName(tree.getName());
        }
        JSONObject sendData = new JSONObject();
        sendData.put("noticeContent", busiTenderNotice.getNoticeContent());
        sendData.put("noticeTitle", busiTenderNotice.getNoticeName());
        sendData.put("purchasingUnit", project.getTendererName());
        sendData.put("noticeSource", "限额以下");
        sendData.put("noticeType", "采购公告");
        sendData.put("publishingTime", busiTenderNotice.getNoticeStartTime().getTime());
        sendData.put("thirdId", busiTenderNotice.getNoticeId());
        sendData.put("tenderType", project.getTenderModeName());
        sendData.put("projectType", project.getProjectTypeName());

        if(null!=project.getProjectArea()){
            BaseTreeData area = baseTreeDataService.getById(project.getProjectArea());
            project.setProjectAreaName(area.getName());
            project.setProjectAreaCode(area.getCode());
            sendData.put("projectArea",area.getName());
            sendData.put("projectAreaCode",area.getCode());
        }
        sendData.put("projectIndustry", project.getProjectIndustryName());
        sendData.put("noticeUrl", "https://xeyx.hbszfcgxh.com:10443/tender/notice/noticeview/"+ project.getProjectId() +"?handleType=supplierMain");
        try {
            System.out.println("请求参数："+sendData);
            JSONObject jsonObject = zcxhUtil.send2Zcxh(sendData, ZcxhEnum.NOTICE.getUrl());
            System.out.println("请求结果："+jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public AjaxResult push2Zcxh(Long projectId){//推送至政采协会
        BusiTenderNotice busiTenderNotice = getTenderNoticeByProjectId(projectId);
        BusiTenderProject project = iBusiTenderProjectService.getById(busiTenderNotice.getProjectId());
        JSONObject sendData = new JSONObject();
        sendData.put("noticeContent", busiTenderNotice.getNoticeContent());
        sendData.put("noticeTitle", busiTenderNotice.getNoticeName());
        sendData.put("purchasingUnit", project.getTendererName());
        sendData.put("noticeSource", "限额以下");
        sendData.put("noticeType", "采购公告");
        sendData.put("publishingTime", busiTenderNotice.getNoticeStartTime().getTime());
        sendData.put("thirdId", busiTenderNotice.getNoticeId());
        sendData.put("noticeUrl", "https://xeyx.hbszfcgxh.com:10443");
        try {
            JSONObject jo = zcxhUtil.send2Zcxh(sendData, ZcxhEnum.NOTICE.getUrl());
            return AjaxResult.success(jo);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }


    @Transactional
    @Override
    public AjaxResult getTenderNoticeInfo(Long noticeId) {
        BusiTenderNotice byId = busiTenderNoticeService.getById(noticeId);
        if(byId!=null) {
            //查询场地占用信息
            List<BusiVenueOccupy> busiVenueOccupy = busiVenueOccupyService.list(new QueryWrapper<BusiVenueOccupy>().eq("notice_id", byId.getNoticeId()));
            if (busiVenueOccupy == null) {
                busiVenueOccupy = new ArrayList<>();
            }
            byId.setBusiVenueOccupys(busiVenueOccupy);
            byId.setAttachments(iBusiAttachmentService.getByBusiId(byId.getNoticeId()));
            byId.setFirstNoticeName(byId.getNoticeName());
            if (byId.getNoticeType() == 2) {
                BusiTenderNotice q = new BusiTenderNotice();
                q.setProjectId(byId.getProjectId());
                q.setNoticeType(1);
                BusiTenderNotice notice = baseMapper.getOneIgnoreDeleted(q);
                byId.setFirstNoticeName(notice.getNoticeName());
            }
        }else {
            byId = new BusiTenderNotice();
            byId.setPriceUnit("1");
            byId.setBidOpeningMode("2");
            byId.setBidEvaluationMode("2");
            byId.setSharedFirstRule(1);
            byId.setSharedFirstRuleRemark("系统随机选取");
        }
        byId.setAttachmentMap(attachmentUtil.getAttachmentMap(noticeId, ProcessEnum.TENDER_NOTICE.getAttachmentCode()));
        byId.setAttachmentNameMap(attachmentUtil.getAttachmentNameMap(noticeId, ProcessEnum.TENDER_NOTICE.getAttachmentCode()));
        return AjaxResult.success(byId);
    }


    @Transactional
    @Override
    public AjaxResult getChangeNoticeInfo(Long noticeId, Integer type){

        BusiTenderNotice notice = null;
        AjaxResult res = getTenderNoticeInfo(noticeId);
        if (res.get(AjaxResult.CODE_TAG) != null && (Integer) res.get(AjaxResult.CODE_TAG) == 200) {
            notice = (BusiTenderNotice)res.get("data");
        }else{
            return AjaxResult.error("查询出错");
        }
        if (type == 1) {
            BusiTenderNotice q = new BusiTenderNotice();
            q.setProjectId(notice.getProjectId());
            q.setNoticeType(1);
            BusiTenderNotice qnotice = baseMapper.getOneIgnoreDeleted(q);
            notice.setFirstNoticeName(qnotice.getNoticeName());
            notice.initChangeInfo();
        }
        return AjaxResult.success(notice);
    }

    @Transactional
    @Override
    public AjaxResult removeTenderNotice(List<BusiTenderNotice> busiTenderNotices) {
        List<Long> busiTenderNoticeIdList=busiTenderNotices.stream()
                .map(BusiTenderNotice::getNoticeId)
                .collect(Collectors.toList());
        //删除场地占用
        List<BusiVenueOccupy> noticeIdBusiVenueOccupy = busiVenueOccupyService.list(new QueryWrapper<BusiVenueOccupy>().in("notice_id", busiTenderNoticeIdList));
        if (!noticeIdBusiVenueOccupy.isEmpty()){
            List<Long> busiVenueOccupyIdList=noticeIdBusiVenueOccupy.stream()
                    .map(BusiVenueOccupy::getOccupyId)
                    .collect(Collectors.toList());
            boolean b = busiVenueOccupyService.removeByIds(busiVenueOccupyIdList);
            if (!b) {
                return AjaxResult.error("删除场地占用信息失败！");
            }
        }

        //删除附件信息
        List<BusiAttachment> busiAttachments = iBusiAttachmentService.list(new QueryWrapper<BusiAttachment>().in("busi_id",busiTenderNoticeIdList).eq("del_flag",0));
        if (!busiAttachments.isEmpty()){
            List<Long> projectIdList = busiAttachments.stream()
                    .map(BusiAttachment::getAttachmentId)
                    .collect(Collectors.toList());
            boolean busiAttachmentBol = iBusiAttachmentService.removeByIds(projectIdList);
            if (!busiAttachmentBol) {
                return AjaxResult.error("删除附件信息失败！");
            }
        }

        return AjaxResult.success(busiTenderNoticeService.removeByIds(busiTenderNoticeIdList));
    }

    @Override
    public AjaxResult duringBidOpeninglist(BusiTenderNotice busiTenderNotice,LoginUser loginUser) {
        // 获取当前登录用户的所有角色信息
        List<SysRole> roles = loginUser.getUser().getRoles();
        // 将角色列表转换为角色键（roleKey）的字符串列表，用于后续角色判断
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());

        // 判断当前用户是否为供应商角色
        if (roleList.contains("supplier")){

            // 查询当前供应商的所有有效报名信息（已注册的项目）
//            List<BusiBidderInfo> bidderInfos = busiBidderInfoService.list(
//                new QueryWrapper<BusiBidderInfo>()
//                    .eq("bidder_id", loginUser.getEntId())
//                    .eq("del_flag", 0)); // 只查询未删除的有效报名记录
            List<BusiBiddingRecord> biddingRecords = busiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>().eq("bidder_id", loginUser.getEntId()).eq("del_flag", 0));
            List<Long> biddingRecordProjectIDS = biddingRecords.stream().map(BusiBiddingRecord::getProjectId).collect(Collectors.toList());
            // 从报名信息中提取项目ID列表
//            List<Long> biddingRecordProjectIDS = bidderInfos.stream().map(BusiBidderInfo::getProjectId).collect(Collectors.toList());
            // 检查是否有有效报名记录
            if (!biddingRecordProjectIDS.isEmpty()){
                // 根据项目ID列表查询项目信息（代理机构id: agency_id, 采购人id: tenderer_id）
                List<BusiTenderProject> projectList = iBusiTenderProjectService.list(new QueryWrapper<BusiTenderProject>().in("project_id", biddingRecordProjectIDS));
                // 从项目列表中提取项目ID
                List<Long> idList = projectList.stream().map(BusiTenderProject::getProjectId).collect(Collectors.toList());
                // 将项目列表转换为Map，以项目ID为键，项目对象为值，便于后续快速查找
                Map<Long, BusiTenderProject> map = projectList.stream()
                        .collect(Collectors.toMap(
                                BusiTenderProject::getProjectId,
                                result -> result,
                                (existing, replacement) -> existing));

                // 创建公告列表用于存储查询结果
                List<BusiTenderNotice> list=new ArrayList<BusiTenderNotice>();
                // 创建查询条件包装器
                QueryWrapper<BusiTenderNotice> queryWrapper = new QueryWrapper<>();
                // 创建一个SimpleDateFormat对象，定义日期格式为 yyyy-MM-dd
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                // 获取开标时间的格式化字符串
                String formattedBidOpeningTime = dateFormat.format(busiTenderNotice.getBidOpeningTime());
                // 获取开标结束时间的格式化字符串
                String formattedBidOpeningEndTime = dateFormat.format( busiTenderNotice.getBidOpeningEndTime());
                // 输出格式化后的日期时间到控制台，用于调试
                System.out.println("Bid Opening Time: " + formattedBidOpeningTime);
                System.out.println("Bid Opening End Time: " + formattedBidOpeningEndTime);
                // 设置查询条件：开标时间大于等于指定日期的开始时间（00:00:00）
                queryWrapper.ge("bid_opening_time", formattedBidOpeningTime+" 00:00:00"); // 开标时间 >= 今天开始时间
                // 设置查询条件：开标时间小于等于指定日期的结束时间（23:59:59）
                queryWrapper.le("bid_opening_time", formattedBidOpeningEndTime+" 23:59:59");   // 开标时间 <= 今天结束时间
                // 设置查询条件：公告状态为1（已发布）
                queryWrapper.eq("notice_stats",1);
                // 设置查询条件：项目ID在指定列表中
                queryWrapper.in("project_id",idList);
               // queryWrapper.in("notice_stats",idList); // 注释掉的代码，可能是之前的错误写法
                // 设置查询条件：删除标志为0（未删除）
                queryWrapper.eq("del_flag",0);
                // 执行查询，获取所有符合条件的待开标项目公告
                list = busiTenderNoticeService.list(queryWrapper); // 查询出所有待开标项目

                // 为每个公告设置对应的项目信息
                list.forEach(item->{
                    item.setProject(map.get(item.getProjectId()));
                });
                // 返回成功结果，包含公告列表
                return AjaxResult.success(list);
            }else {
                // 如果没有投标记录，返回错误信息
                return AjaxResult.error("今天无待开标信息");
            }
        }
        // 判断当前用户是否为代理机构或采购人角色
        else if (roleList.contains("agency")||roleList.contains("purchaser")){
            // 查询当前用户相关的项目信息（作为代理机构或采购人）
            List<BusiTenderProject> projectList = iBusiTenderProjectService.list(new QueryWrapper<BusiTenderProject>()
                    .and(wrapper -> wrapper.eq("agency_id", loginUser.getEntId()) // 添加等于代理机构ID的条件
                            .or() // 或者
                            .eq("tenderer_id", loginUser.getEntId()))); // 等于采购人ID的条件
            // 从项目列表中提取项目ID
            List<Long> projectIds = projectList.stream()
                    .map(BusiTenderProject::getProjectId)
                    .collect(Collectors.toList());
            // 查询已开标的公告列表
            List<BusiTenderNotice> list=new ArrayList<BusiTenderNotice>();
            // 创建查询条件包装器
            QueryWrapper<BusiTenderNotice> queryWrapper = new QueryWrapper<>();
            // 创建一个SimpleDateFormat对象，定义日期格式为 yyyy-MM-dd
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            // 获取开标时间的格式化字符串
            String formattedBidOpeningTime = dateFormat.format(busiTenderNotice.getBidOpeningTime());
            // 获取开标结束时间的格式化字符串
            String formattedBidOpeningEndTime = dateFormat.format( busiTenderNotice.getBidOpeningEndTime());
            // 输出格式化后的日期时间到控制台，用于调试
            System.out.println("Bid Opening Time: " + formattedBidOpeningTime);
            System.out.println("Bid Opening End Time: " + formattedBidOpeningEndTime);
            // 设置查询条件：开标时间大于等于指定日期的开始时间
            queryWrapper.ge("bid_opening_time", formattedBidOpeningTime+" 00:00:00"); // 开标时间 >= 今天开始时间
            // 设置查询条件：开标时间小于等于指定日期的结束时间
            queryWrapper.le("bid_opening_time", formattedBidOpeningEndTime+" 23:59:59");   // 开标时间 <= 今天结束时间
            // 设置查询条件：公告状态为1（已发布）
            queryWrapper.eq("notice_stats",1);
            //queryWrapper.eq("notice_stats",1); // 重复的条件，已注释
            // 设置查询条件：项目ID在指定列表中
            queryWrapper.in("project_id",projectIds);
            // 设置查询条件：删除标志为0（未删除）
            queryWrapper.eq("del_flag",0);
            // 执行查询，获取符合条件的公告列表
            list = busiTenderNoticeService.list(queryWrapper);

            // 检查查询结果是否为空
            if (!list.isEmpty()){

                // 将项目列表转换为Map，以项目ID为键，项目对象为值
                Map<Long, BusiTenderProject> map = projectList.stream()
                        .collect(Collectors.toMap(
                                BusiTenderProject::getProjectId,
                                result -> result,
                                (existing, replacement) -> existing));
                // 为每个公告设置对应的项目信息
                list.forEach(item->{
                    item.setProject(map.get(item.getProjectId()));
                });
                // 返回成功结果，包含公告列表
                return AjaxResult.success(list);
            }else {
                // 如果没有查询到结果，返回错误信息
                return AjaxResult.error("今天无待开标信息");
            }
        }
        // 如果用户角色不匹配任何条件，返回null
        return null;
    }

    @Override
    public AjaxResult duringBidOpeningInfo(BusiTenderNotice busiTenderNotice) {
        List<BusiTenderNotice> list=new ArrayList<BusiTenderNotice>();
        QueryWrapper<BusiTenderNotice> queryWrapper = new QueryWrapper<>();
        if ( busiTenderNotice.getProjectId() != null) {
            queryWrapper.eq("project_id",busiTenderNotice.getProjectId());
        }
//        busiTenderNotice.getBidOpeningTime();
//        busiTenderNotice.getBidOpeningEndTime();
        // 创建一个SimpleDateFormat对象，定义目标格式 HH:mm:ss
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        try {
            // 获取开标时间和结束时间
            String formattedBidOpeningTime = dateFormat.format(busiTenderNotice.getBidOpeningTime());
            String formattedBidOpeningEndTime = dateFormat.format( busiTenderNotice.getBidOpeningEndTime());
            // 输出格式化后的日期时间
            System.out.println("Bid Opening Time: " + formattedBidOpeningTime);
            System.out.println("Bid Opening End Time: " + formattedBidOpeningEndTime);
            queryWrapper.ge("bid_opening_time", formattedBidOpeningTime+" 00:00:00"); // 开标时间 >= 今天开始时间
            queryWrapper.le("bid_opening_time", formattedBidOpeningEndTime+" 23:59:59");   // 开标时间 <= 今天结束时间
            queryWrapper.eq("notice_stats",1);
            queryWrapper.eq("del_flag",0);
            list = busiTenderNoticeService.list(queryWrapper);
            // 使用Stream API提取projectId集合
            List<Long> projectIds = list.stream()
                    .map(BusiTenderNotice::getProjectId)
                    .collect(Collectors.toList());
            if (!list.isEmpty()){
                // inProjectIds不为空且不为空列表
                ProjectInfoAndIdsVo projectsVo = iBusiTenderProjectService.getProjectsByProjectIds(projectIds);
                //查询项目信息
                Map<Long, BusiTenderProject> map = projectsVo.getProjects().stream()
                        .collect(Collectors.toMap(
                                BusiTenderProject::getProjectId,
                                result -> result,
                                (existing, replacement) -> existing)); // 在键冲突时保留现有的元素
                list.forEach(item->{
                    item.setProject(map.get(item.getProjectId()));
                });
                return AjaxResult.success(list.get(0));
            }else {
                return AjaxResult.error("今天无待开标信息");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success(list.get(0));
    }



    @Override
    public BusiTenderNotice selectByProject(Long projectId) {
        QueryWrapper<BusiTenderNotice> busiTenderNoticeQueryWrapper = new QueryWrapper<>();
        busiTenderNoticeQueryWrapper.eq("project_id", projectId);
        // 添加排序，优先获取最新的公告（按创建时间倒序）
        busiTenderNoticeQueryWrapper.orderByDesc("create_time");
        // 使用 list() 方法获取列表，然后取第一条记录，避免 TooManyResultsException
        List<BusiTenderNotice> noticeList = list(busiTenderNoticeQueryWrapper);
        BusiTenderNotice byId = noticeList.isEmpty() ? null : noticeList.get(0);
        if(byId!=null) {
            //查询场地占用信息
            List<BusiVenueOccupy> busiVenueOccupy = busiVenueOccupyService.list(new QueryWrapper<BusiVenueOccupy>().eq("notice_id", byId.getNoticeId()));
            if (busiVenueOccupy == null) {
                busiVenueOccupy = new ArrayList<>();
            }
            byId.setBusiVenueOccupys(busiVenueOccupy);
            byId.setAttachments(iBusiAttachmentService.getByBusiId(byId.getNoticeId()));
            byId.setFirstNoticeName(byId.getNoticeName());
            if (byId.getNoticeType() == 2) {
                BusiTenderNotice q = new BusiTenderNotice();
                q.setProjectId(byId.getProjectId());
                q.setNoticeType(1);
                BusiTenderNotice notice = baseMapper.getOneIgnoreDeleted(q);
                byId.setFirstNoticeName(notice.getNoticeName());
            }
        }else {
            byId = new BusiTenderNotice();
            byId.setPriceUnit("1");
            byId.setBidOpeningMode("2");
            byId.setBidEvaluationMode("2");
            byId.setAttachmentMap(new HashMap<>());
        }
        byId.setAttachmentMap(attachmentUtil.getAttachmentMap(byId.getNoticeId(), ProcessEnum.TENDER_NOTICE.getAttachmentCode()));
        return byId;
    }


    @Override
    public List<BusiTenderNotice> selectForBidOpening()
    {
        Long agencyId = null, tendererId = null;
        if(SecurityUtils.getAuthentication()!=null && SecurityUtils.getLoginUser()!=null) {
            Long entId = SecurityUtils.getLoginUser().getEntId();
            List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
            for (SysRole role : roles) {
                if (role.getRoleKey().equals("purchaser")) {
                    tendererId = entId;
                } else if (role.getRoleKey().equals("agency")) {
                    agencyId = entId;
                }
            }
        }

        return baseMapper.selectForBidOpening(tendererId, agencyId);
    }

    @Override
    public NoticeInfoAndIdsVo getTenderNotisByProjectId(List<Long> projectIds) {
            List<BusiTenderNotice> busiTenderNotices = baseMapper.selectList(new QueryWrapper<BusiTenderNotice>().in("project_id", projectIds));
            NoticeInfoAndIdsVo noticeInfoAndIdsVo = new NoticeInfoAndIdsVo();
            noticeInfoAndIdsVo.setNotices(busiTenderNotices);
            noticeInfoAndIdsVo.setNoticeIds(projectIds);
            //查询项目信息
            Map<Long, BusiTenderNotice> map = busiTenderNotices.stream()
                    .collect(Collectors.toMap(
                            BusiTenderNotice::getProjectId,
                            result -> result,
                            (existing, replacement) -> existing)); // 在键冲突时保留现有的元素
            noticeInfoAndIdsVo.setNoticesMap(map);
            return noticeInfoAndIdsVo;
    }

    @Override
    public AjaxResult getNoticeStatistics(LoginUser loginUser) {
        BigDecimal totalAmount=BigDecimal.ZERO; // 累计订单额
        Integer winCount=0; // 中标数量
        Integer totalCount=0; // 累计投标数量

        List<BusiBidderInfo> bidderInfos = busiBidderInfoService.list(
                new QueryWrapper<BusiBidderInfo>().eq("bidder_id", loginUser.getEntId()).eq("is_win",1));
        if (!bidderInfos.isEmpty()){
            winCount=bidderInfos.size();
            totalAmount = bidderInfos.stream().map( BusiBidderInfo::getBidderAmount ).reduce(BigDecimal.ZERO, BigDecimal::add); //BigDecimal类型

        }
        List<BusiBiddingRecord> biddingRecords = busiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>().eq("bidder_id", loginUser.getEntId()));
        if (!biddingRecords.isEmpty()){
            totalCount=biddingRecords.size();
        }
        Map<String,Object> map=new HashMap<>();
        map.put("totalAmount",totalAmount);
        map.put("winCount",totalCount);
        map.put("totalCount",winCount);
        return AjaxResult.success(map);
    }

    @Override
    public AjaxResult getNoticeStatisticsByType(LoginUser loginUser,Integer type) {
        // 已开标30,40,50,60
        //上传响应文件就算已投标
        List<BusiBiddingRecord> bidderId = busiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>().eq("bidder_id", loginUser.getEntId()));
        //获取已投标的所有项目id
        List<Long> projectIds = bidderId.stream()
                .map(BusiBiddingRecord::getProjectId)
                .collect(Collectors.toList());
        if (type==1){
            List<Integer> status=new ArrayList<>();
            status.add(30);
            status.add(40);
            status.add(50);
            status.add(60);
            //获取已投标的项目
            List<BusiTenderProject> projectList = iBusiTenderProjectService.list(new QueryWrapper<BusiTenderProject>()
                    .in("project_status", status).in("project_id",projectIds));
            //获取已投标项目且已开标项目的id
            List<Long> openNotice = projectList.stream()
                    .map(BusiTenderProject::getProjectId)
                    .collect(Collectors.toList());
            //获取响应的采购公告返回
            List<BusiTenderNotice> busiTenderNotices = busiTenderNoticeService.list(new QueryWrapper<BusiTenderNotice>().in("project_id", openNotice));
            return AjaxResult.success(busiTenderNotices);
        }
        //未开标10,20
        if (type==2){
            List<Integer> status=new ArrayList<>();
            status.add(10);
            status.add(20);
            //获取未开标的项目
            List<BusiTenderProject> projectList = iBusiTenderProjectService.list(new QueryWrapper<BusiTenderProject>()
                    .in("project_status", status).in("project_id",projectIds));
            //获取已投标项目且未开标
            List<Long> openNotice = projectList.stream()
                    .map(BusiTenderProject::getProjectId)
                    .collect(Collectors.toList());
            //获取响应的采购公告返回
            List<BusiTenderNotice> busiTenderNotices = busiTenderNoticeService.list(new QueryWrapper<BusiTenderNotice>().in("project_id", openNotice));
            return AjaxResult.success(busiTenderNotices);
        }
        //已中标
        if (type==3){
            //获取中标信息
            List<BusiBidderInfo> bidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("is_win", 1).eq("bidder_id",loginUser.getEntId()));
            //获取中标信息查询
            List<Long> successId = bidderInfos.stream() .map(BusiBidderInfo::getProjectId).collect(Collectors.toList());
            List<BusiTenderNotice> busiTenderNotices = busiTenderNoticeService.list(new QueryWrapper<BusiTenderNotice>().in("project_id", successId));
            return AjaxResult.success(busiTenderNotices);
        }
        return AjaxResult.success("获取我已投标的项目信息");
    }

    @Override
    public BusiTenderVo getTenderNotice(Long tenderNoticeId, boolean isMain){
        BusiTenderVo vo = new BusiTenderVo();
        BusiTenderNotice query1 = new BusiTenderNotice();
        query1.setNoticeId(tenderNoticeId);
        BusiTenderNotice tenderNotice = baseMapper.getOneIgnoreDeleted(query1);
        if(tenderNotice!=null) {
            BusiTenderProject tenderProject = iBusiTenderProjectService.getById(tenderNotice.getProjectId());
            if(tenderProject.getGetBidderMode()==2) {
                String[] strings = tenderNotice.getInviteBidder().split(",");
                List<BaseEntInfo> baseEntInfos = baseEntInfoService.getByIds(Arrays.stream(strings)
                        .map(Long::parseLong)
                        .collect(Collectors.toList()));
                String joinedEntNames = baseEntInfos.stream()
                        .map(BaseEntInfo::getEntName)
                        .collect(Collectors.joining("、"));
                tenderNotice.setInviteBidderName(joinedEntNames);
            }
            vo.setTenderNotice(tenderNotice);
            vo.setTenderProject(tenderProject);
            BusiVenueOccupy occupyQuery = new BusiVenueOccupy();
            occupyQuery.setNoticeId(tenderNoticeId);
            List<BusiVenueOccupy> venueOccupies = busiVenueOccupyService.getListIgnoreDeleted(occupyQuery);
            for (BusiVenueOccupy occupy : venueOccupies) {
                if (occupy.getVenueType() == 1) {
                    vo.getTenderNotice().setBidOpening(occupy);
                }else{
                    vo.getTenderNotice().setBidEvaluation(occupy);
                }
            }
            if (tenderNotice.getNoticeType() == 2) {
                BusiTenderNotice oldNoticeQuery = new BusiTenderNotice();
                oldNoticeQuery.setProjectId(tenderNotice.getProjectId());
                oldNoticeQuery.setChangeNum(tenderNotice.getChangeNum()-1);
                BusiTenderNotice oldTenderNotice = getOneIgnoreDeleted(oldNoticeQuery);
                BusiVenueOccupy query2 = new BusiVenueOccupy();
                query2.setNoticeId(oldTenderNotice.getNoticeId());
                List<BusiVenueOccupy> occupyList2 = busiVenueOccupyService.getListIgnoreDeleted(query2);
                for (BusiVenueOccupy occupy : occupyList2) {
                    if (occupy.getVenueType() == 1) {
                        oldTenderNotice.setBidOpening(occupy);
                    }else{
                        oldTenderNotice.setBidEvaluation(occupy);
                    }
                }
                vo.setOldTenderNotice(oldTenderNotice);
            }
            if(!isMain) {
                tenderNotice.setAttachmentMap(attachmentUtil.getAttachmentMap(tenderNoticeId, ProcessEnum.TENDER_NOTICE.getAttachmentCode()));
                tenderNotice.setAttachmentNameMap(attachmentUtil.getAttachmentNameMap(tenderNoticeId, ProcessEnum.TENDER_NOTICE.getAttachmentCode()));
                BusiBiddingRecord bbrq = new BusiBiddingRecord();
                bbrq.setProjectId(tenderNotice.getProjectId());
                bbrq.getParams().put("isScope", "1");
                List<BusiBiddingRecord> busiBiddingRecordList = busiBiddingRecordService.selectList(bbrq);
                vo.setBiddingRecordNum(busiBiddingRecordList.size());
            }
        }
        return vo;
    }

    @Override
    public byte[] getTenderNoticeFileZip(Long tenderNoticeId) throws Exception{
        List<BusiAttachment> noticeAttachmentList = iBusiAttachmentService.getByBusiId(tenderNoticeId);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(baos);
        for (BusiAttachment attachment : noticeAttachmentList) {
            File file = getAttachment(attachment);
            if (file!=null && file.isFile()) {
                FileInputStream fis = new FileInputStream(file);
                ZipEntry zipEntry = new ZipEntry(file.getName());
                zos.putNextEntry(zipEntry);

                byte[] bytes = new byte[1024];
                int length;
                while ((length = fis.read(bytes)) >= 0) {
                    zos.write(bytes, 0, length);
                }

                fis.close();
                zos.flush();
                zos.closeEntry();
            }
        }
        IOUtils.closeQuietly(zos);

        return baos.toByteArray();
    }

    @Override
    public BusiTenderNotice getTenderNoticeByProjectId(Long projectId) {
        List<BusiTenderNotice> busiTenderNoticeList = list(new QueryWrapper<BusiTenderNotice>().eq("project_id", projectId).orderByDesc("create_time"));
        if(busiTenderNoticeList!=null && !busiTenderNoticeList.isEmpty()){
            BusiTenderNotice busiTenderNotice = busiTenderNoticeList.get(0);
            if(busiTenderNotice!=null) {
                busiTenderNotice.setAttachments(iBusiAttachmentService.getByBusiId(busiTenderNotice.getNoticeId()));
            }
            return busiTenderNotice;
        }
        return null;
    }

    private File getAttachment(BusiAttachment attachment){
        File file = new File(AttachmentUtil.urlToReal(attachment.getFilePath()));
        if (file.exists()) {
            return file;
        }
        return null;
    }

    @Resource
    private PdfUtil pdfUtil;
    @Override
    public String getMainContent(BusiTenderProject tenderProject, BusiTenderNotice tenderNotice, BusiTenderNotice oldTenderNotice){
        Map<String, Object> map = new HashMap<>();
        map.put("tenderNotice", tenderNotice);
        map.put("tenderProject", tenderProject);
        map.put("oldTenderNotice", oldTenderNotice);


        map.put("fileItems", attachmentUtil.getAttachmentList(tenderNotice.getNoticeId(), ProcessEnum.TENDER_NOTICE.getAttachmentCode()));
        String ftlPath = "portal_templates/采购公告.ftl";
        if(tenderNotice.getNoticeType()==2){
            ftlPath = "portal_templates/变更公告.ftl";
        }
        try {
            return pdfUtil.toHtml(map, ftlPath);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public BusiTenderNotice getOneIgnoreDeleted(BusiTenderNotice busiTenderNotice){
        return baseMapper.getOneIgnoreDeleted(busiTenderNotice);
    }

    @Override
    public AjaxResult getNoticeTypes(Long projectId){
        BusiTenderNotice query = new BusiTenderNotice();
        query.setProjectId(projectId);
        List<BusiTenderNotice> noticeList = baseMapper.getListIgnoreDeleted(query);
        return AjaxResult.success(noticeList);
    }
}
