package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.template.TemplateException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.base.service.IBaseTreeDataService;
import com.ruoyi.busi.controller.FtlToPdfConverter;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.domain.vo.BidOpeningResultVo;
import com.ruoyi.busi.domain.vo.BusiTenderVo;
import com.ruoyi.busi.domain.vo.NoticeInfoAndIdsVo;
import com.ruoyi.busi.domain.vo.ProjectInfoAndIdsVo;
import com.ruoyi.busi.enums.ProcessEnum;
import com.ruoyi.busi.enums.ProjectCodeEnum;
import com.ruoyi.busi.mapper.BusiTenderProjectMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.scoring.domain.ScoringMethodInfo;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.service.IScoringMethodInfoService;
import com.ruoyi.scoring.service.IScoringMethodItemService;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.utils.AttachmentUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购项目信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BusiTenderProjectServiceImpl extends ServiceImpl<BusiTenderProjectMapper, BusiTenderProject> implements IBusiTenderProjectService {
    @Autowired
    private ISysConfigService configService;
    @Autowired
    IBusiTenderIntentionService tenderIntentionService;
    @Autowired
    IBusiAttachmentService iBusiAttachmentService;
    @Autowired
    IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    IBusiTenderNoticeService iBusiTenderNoticeService;
    @Autowired
    private IBusiTenderDocumentsDownloadService busiTenderDocumentsDownloadService;
    @Autowired
    IBusiBidderInfoService iBusiBidderInfoService;
    @Autowired
    private IBusiWinningBidderNoticeService busiWinningBidderNoticeService;
    @Autowired
    private IBusiWinningBidderAdviceNoteService busiWinningBidderAdviceNoteService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private IScoringMethodInfoService scoringMethodInfoService;
    @Autowired
    private IScoringMethodUinfoService scoringMethodUinfoService;
    @Autowired
    private IBaseTreeDataService baseTreeDataService;
    @Autowired
    private AttachmentUtil attachmentUtil;
    @Autowired
    private IScoringMethodItemService scoringMethodItemService;
    @Autowired
    private IScoringMethodUitemService scoringMethodUitemService;

    @Autowired
    private IBusiExtractExpertApplyService busiExtractExpertApplyService;
    /**
     * 查询采购项目信息列表
     *
     * @param busiTenderProject 采购项目信息
     * @return 采购项目信息
     */
    @Override
    @DataScope(entAlias = "tenderer_id,agency_id")
    public List<BusiTenderProject> selectList(BusiTenderProject busiTenderProject) {
        QueryWrapper<BusiTenderProject> busiTenderProjectQueryWrapper = getBusiTenderProjectQueryWrapper(busiTenderProject);
        busiTenderProjectQueryWrapper.orderByDesc("create_time");
        List<BusiTenderProject> list = list(busiTenderProjectQueryWrapper);
        if (busiTenderProject.getParams().containsKey("attachments")) {
            List<Long> projectIds = list.stream().map(item -> {
                return item.getProjectId();
            }).collect(Collectors.toList());
            List<BusiAttachment> attachments = iBusiAttachmentService.list(new QueryWrapper<BusiAttachment>().in("project_id", projectIds));
            Map<Long, List<BusiAttachment>> attachmentMap = attachments.stream()
                    .collect(Collectors.groupingBy(BusiAttachment::getAttachmentId));
            list.forEach(item -> {
                item.setAttachments(attachmentMap.get(item.getProjectId()));
            });
        }
        return list;
    }

    @Override
    public List<BusiTenderProject> getListByExtractionType(BusiTenderProject busiTenderProject) {
        List<BusiExtractExpertApply> busiExtractExpertApplies = busiExtractExpertApplyService.list(new QueryWrapper<BusiExtractExpertApply>().eq("extraction_type", busiTenderProject.getExtractionType()));

        // 提取project_id集合
        Set<Long> appliesProjectIds = busiExtractExpertApplies.stream()
                .map(BusiExtractExpertApply::getProjectId)
                .collect(Collectors.toSet());

        List<BusiTenderProject> projectList = selectList(busiTenderProject);

        // 找出projectList中projectId不在appliesProjectIds中的项目
        List<BusiTenderProject> extraProjects = projectList.stream()
                .filter(project -> !appliesProjectIds.contains(project.getProjectId()))
                .collect(Collectors.toList());

        return extraProjects;
    }

    private QueryWrapper<BusiTenderProject> getBusiTenderProjectQueryWrapper(BusiTenderProject busiTenderProject) {
        QueryWrapper<BusiTenderProject> busiTenderProjectQueryWrapper = new QueryWrapper<>();
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getProjectCode()), "project_code", busiTenderProject.getProjectCode());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getProjectIntentionId()), "project_intention_id", busiTenderProject.getProjectIntentionId());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getIsEmergencyProject()), "is_emergency_project", busiTenderProject.getIsEmergencyProject());
        busiTenderProjectQueryWrapper.like(ObjectUtil.isNotEmpty(busiTenderProject.getProjectName()), "project_name", busiTenderProject.getProjectName());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getProjectContent()), "project_content", busiTenderProject.getProjectContent());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getBidderQualification()), "bidder_qualification", busiTenderProject.getBidderQualification());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getProjectArea()), "project_area", busiTenderProject.getProjectArea());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getProjectIndustry()), "project_industry", busiTenderProject.getProjectIndustry());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getTendererId()), "tenderer_id", busiTenderProject.getTendererId());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getProjectStatus()), "project_status", busiTenderProject.getProjectStatus());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getTendererCode()), "tenderer_code", busiTenderProject.getTendererCode());
        busiTenderProjectQueryWrapper.like(ObjectUtil.isNotEmpty(busiTenderProject.getTendererName()), "tenderer_name", busiTenderProject.getTendererName());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getTendererContactPerson()), "tenderer_contact_person", busiTenderProject.getTendererContactPerson());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getTendererPhone()), "tenderer_phone", busiTenderProject.getTendererPhone());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getTenderMode()), "tender_mode", busiTenderProject.getTenderMode());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getAgencyFlag()), "agency_flag", busiTenderProject.getAgencyFlag());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getAgencyId()), "agency_id", busiTenderProject.getAgencyId());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getAgencyCode()), "agency_code", busiTenderProject.getAgencyCode());
        busiTenderProjectQueryWrapper.like(ObjectUtil.isNotEmpty(busiTenderProject.getAgencyName()), "agency_name", busiTenderProject.getAgencyName());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getAgencyContactPerson()), "agency_contact_person", busiTenderProject.getAgencyContactPerson());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getAgencyPhone()), "agency_phone", busiTenderProject.getAgencyPhone());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getBudgetAmount()), "budget_amount", busiTenderProject.getBudgetAmount());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getControlPrice()), "control_price", busiTenderProject.getControlPrice());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getTenderFundSource()), "tender_fund_source", busiTenderProject.getTenderFundSource());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getTenderSelfFund()), "tender_self_fund", busiTenderProject.getTenderSelfFund());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getTenderFinancialFund()), "tender_financial_fund", busiTenderProject.getTenderFinancialFund());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getProjectDuration()), "project_duration", busiTenderProject.getProjectDuration());
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getDelFlag()), "del_flag", busiTenderProject.getDelFlag());
        String beginCreateTime = busiTenderProject.getParams().get("beginCreateTime") != null ? busiTenderProject.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = busiTenderProject.getParams().get("endCreateTime") + "" != null ? busiTenderProject.getParams().get("endCreateTime") + "" : "";
        busiTenderProjectQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getCreateBy()), "create_by", busiTenderProject.getCreateBy());
        String beginUpdateTime = busiTenderProject.getParams().get("beginUpdateTime") != null ? busiTenderProject.getParams().get("beginUpdateTime") + "" : "";
        String endUpdateTime = busiTenderProject.getParams().get("endUpdateTime") + "" != null ? busiTenderProject.getParams().get("endUpdateTime") + "" : "";
        busiTenderProjectQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime, endUpdateTime);
        busiTenderProjectQueryWrapper.eq(ObjectUtil.isNotEmpty(busiTenderProject.getUpdateBy()), "update_by", busiTenderProject.getUpdateBy());
        busiTenderProjectQueryWrapper.apply(
                ObjectUtil.isNotEmpty(busiTenderProject.getParams().get("dataScope"))
                && (!busiTenderProject.getParams().containsKey("isScope") || Boolean.parseBoolean(busiTenderProject.getParams().get("isScope").toString())),
                busiTenderProject.getParams().get("dataScope") + ""
        );
            busiTenderProjectQueryWrapper.and(ObjectUtil.isNotEmpty(busiTenderProject.getParams().get("key")),w->{
                w.like("project_name",busiTenderProject.getParams().get("key")).or().like("project_code",busiTenderProject.getParams().get("key"));
            });
            busiTenderProjectQueryWrapper.in(ObjectUtil.isNotEmpty(busiTenderProject.getParams().get("projectIds"))
            ,"project_id",(List)busiTenderProject.getParams().get("projectIds"));
        if(ObjectUtil.isNotEmpty(busiTenderProject.getParams().get("projectStatusList"))){
            List projectStatusList = new ArrayList();
            try {
                projectStatusList = (List)busiTenderProject.getParams().get("projectStatusList");
            }catch (Exception e) {
                String[] projectStatusLists = (busiTenderProject.getParams().get("projectStatusList") + "").split(",");
                projectStatusList = Arrays.asList(projectStatusLists);
            }
            busiTenderProjectQueryWrapper.in(
                    "project_status",
                    projectStatusList
            );
        }
        if(ObjectUtil.isEmpty(busiTenderProject.getParams().get("allProject"))){
            busiTenderProjectQueryWrapper.ge("project_status",-2);
        }
        return busiTenderProjectQueryWrapper;
    }

    /**
     * 新增采购项目并携带附件数据
     *
     * @param busiTenderProject
     * @return
     */
    @Transactional
    @Override
    public AjaxResult saveHaveAttachment(BusiTenderProject busiTenderProject) {
        if(busiTenderProject.getBusiState()!=0){
            busiTenderProject.setProjectStatus(10);
        }else{
            busiTenderProject.setProjectStatus(0);
        }
        if ((busiTenderProject.getProjectId() == null ||
                StringUtils.isBlank(busiTenderProject.getProjectCode())) &&
            StringUtils.isNoneBlank(busiTenderProject.getTenderModeName())) {
            busiTenderProject.setProjectCode(getCodeNum(busiTenderProject.getTenderModeName()));
        }
        boolean save = saveOrUpdate(busiTenderProject);
        //保存附件
        attachmentUtil.addNewAttachments(busiTenderProject);
        if (busiTenderProject.getTenderMode().equals("3")){
            //询价的评分方法
            ScoringMethodInfo one = scoringMethodInfoService.getOne(new QueryWrapper<ScoringMethodInfo>().eq("tender_mode", 3).eq("project_type", 2));
            ScoringMethodUinfo scoringMethodUinfo=scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                    .eq("project_id", busiTenderProject.getProjectId())
                    .eq("scoring_method_id", one.getScoringMethodId()));
            if(scoringMethodUinfo == null) {
                scoringMethodUinfo = new ScoringMethodUinfo();
                scoringMethodUinfo.setProjectId(busiTenderProject.getProjectId());
                scoringMethodUinfo.setScoringMethodId(one.getScoringMethodId());
                if (busiTenderProject.getAgencyId() != null) {
                    scoringMethodUinfo.setEntId(busiTenderProject.getAgencyId());
                } else {
                    scoringMethodUinfo.setEntId(busiTenderProject.getTendererId());
                }
            }
            scoringMethodUinfoService.saveOrUpdate(scoringMethodUinfo);
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId()));
            if(scoringMethodUitems==null || scoringMethodUitems.isEmpty()) {
                List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                        .eq("scoring_method_id", one.getScoringMethodId()));
                List<Long> scoringMethodItemIds = scoringMethodItems.stream()
                        .map(ScoringMethodItem::getScoringMethodItemId) // 假设getScoringMethodItemId是获取ID的方法
                        .collect(Collectors.toList());
                //查询因素信息
                scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                        .in("scoring_method_item_id", scoringMethodItemIds)
                        .eq("ent_method_id", 0)
                        .eq("ent_id", 0)
                );
                for (ScoringMethodUitem scoringMethodUitem : scoringMethodUitems) {
                    scoringMethodUitem.setEntMethodItemId(null);//主键为空
                    scoringMethodUitem.setEntMethodId(scoringMethodUinfo.getEntMethodId());
                    scoringMethodUitem.setEntId(scoringMethodUinfo.getEntId());
                }
                scoringMethodUitemService.saveOrUpdateBatch(scoringMethodUitems);
            }

        }


        return  AjaxResult.success(busiTenderProject);
    }

    private String getCodeNum(String codeName){
        ProjectCodeEnum cc = ProjectCodeEnum.getCodeByName(codeName);
        String code = "XJ";
        if (cc != null) {
        code = cc.getCode();
    }
        String codePrefix = code + "-" + Calendar.getInstance().get(Calendar.YEAR);
        Integer nowCodeNum = baseMapper.getMax(codePrefix);
        return codePrefix + "-" + String.format("%04d", nowCodeNum+1);
    }

    @Override
    public BusiTenderProject getByIdHasAttachment(Long projectId) {
        BusiTenderProject byId = getById(projectId);
        byId.setAttachments(iBusiAttachmentService.getByBusiId(projectId));
        byId.setNotice(iBusiTenderNoticeService.getTenderNoticeByProjectId(projectId));
        return byId;
    }

    @Override
    public BusiTenderVo getAllById(Long projectId) {
        BusiTenderVo vo = new BusiTenderVo();
        BusiTenderProject byId = getById(projectId);
        if(byId!=null) {
            BusiTenderIntention tenderIntention = tenderIntentionService.getById(byId.getProjectIntentionId());
            vo.setTenderIntention(tenderIntention);

            if(StringUtils.isNoneBlank(byId.getTenderMode())) {
                byId.setTenderModeName(dictDataService.selectDictLabel("busi_tender_mode", byId.getTenderMode()));
            }
            byId.setProjectTypeName(dictDataService.selectDictLabel("busi_project_type", byId.getProjectType()));
            if (StringUtils.isNoneBlank(byId.getProjectIndustry())) {
                String[] industrys = byId.getProjectIndustry().split(",");
                String lastIndustry = industrys[industrys.length - 1];
                BaseTreeData tree = baseTreeDataService.getById(Long.valueOf(lastIndustry));
                byId.setProjectIndustryName(tree.getName());
            }
//            byId.setProjectIndustryName(dictDataService.selectDictLabel("busi_tender_mode", byId.getTenderMode()));
            if(StringUtils.isNoneBlank(byId.getTenderFundSource())) {
                byId.setTenderFundSourceName(dictDataService.selectDictLabel("tender_project_fundsource", byId.getTenderFundSource()));
            }
            if(StringUtils.isNoneBlank(byId.getProjectArea())) {
                byId.setProjectAreaName(baseTreeDataService.getById(Long.valueOf(byId.getProjectArea())).getName());
            }
        }else{
            byId = new BusiTenderProject();
            byId.setBidderQualification("无");
        }
        byId.setAttachmentMap(attachmentUtil.getAttachmentMap(projectId, ProcessEnum.TENDER_PROJECT.getAttachmentCode()));
        vo.setTenderProject(byId);
        return vo;
    }

    @Override
    public AjaxResult updateByIdHaveAttachment(BusiTenderProject busiTenderProject) {
        saveOrUpdate(busiTenderProject);
        List<BusiAttachment> newAttachments = busiTenderProject.getAttachments().stream().map(item -> {
            item.setBusiId(busiTenderProject.getProjectId());
            return item;
        }).collect(Collectors.toList());
        iBusiAttachmentService.remove(new QueryWrapper<BusiAttachment>().eq("busi_id", busiTenderProject.getProjectId()));
        iBusiAttachmentService.saveOrUpdateBatch(newAttachments);
        return AjaxResult.success();
    }

    @Override
    public ProjectInfoAndIdsVo getProjectsByLoginInfo(boolean isScope) {
        ProjectInfoAndIdsVo vo = new ProjectInfoAndIdsVo();
        BusiTenderProject project = new BusiTenderProject();
        if(isScope){
            project.getParams().put("dataScope", "(tenderer_id = "+ SecurityUtils.getLoginUser().getEntId() +"  OR agency_id = "+SecurityUtils.getLoginUser().getEntId()+" )");
            project.getParams().put("isScope", "true");
        }
        List<BusiTenderProject> projects = selectList(project);
        vo.setProjects(projects);
        List<Long> projectIds = projects.stream().map(item -> item.getProjectId()).collect(Collectors.toList());
        vo.setProjectIds(projectIds);
        //查询项目信息
        Map<Long, BusiTenderProject> map = projects.stream()
                .collect(Collectors.toMap(
                        BusiTenderProject::getProjectId,
                        result -> result,
                        (existing, replacement) -> existing)); // 在键冲突时保留现有的元素
        vo.setProjectsMap(map);
        return vo;
    }

    @Override
    public ProjectInfoAndIdsVo getProjectsByProjectIds(List<Long> projectIds) {
        ProjectInfoAndIdsVo vo = new ProjectInfoAndIdsVo();
        if (!projectIds.isEmpty()){
            List<BusiTenderProject> projects = list(new QueryWrapper<BusiTenderProject>()
                    .in("project_id", projectIds));
            vo.setProjects(projects);
            vo.setProjectIds(projectIds);
            //查询项目信息
            Map<Long, BusiTenderProject> map = projects.stream()
                    .collect(Collectors.toMap(
                            BusiTenderProject::getProjectId,
                            result -> result,
                            (existing, replacement) -> existing)); // 在键冲突时保留现有的元素
            vo.setProjectsMap(map);
        }
        return vo;
    }

    @Override
    public AjaxResult supplierLookProdect(LoginUser loginUser) {
        List<BusiTenderDocumentsDownload> documentsDownloads = busiTenderDocumentsDownloadService.list(new QueryWrapper<BusiTenderDocumentsDownload>().eq("bidder_id", loginUser.getEntId()));
        List<BusiTenderProject> list = new ArrayList<>();
        if(documentsDownloads!=null && !documentsDownloads.isEmpty()) {
            Set<Long> collect = documentsDownloads.stream()
                    .map(BusiTenderDocumentsDownload::getProjectId)
                    .collect(Collectors.toSet());
            QueryWrapper<BusiTenderProject> queryWrapper = new QueryWrapper<BusiTenderProject>();
            queryWrapper.in("project_id", collect);
            queryWrapper.notInSql("project_id", "select project_id from busi_bidding_record where del_flag=0 and bidder_id="+loginUser.getEntId());
            list = iBusiTenderProjectService.list(queryWrapper);

            //查询公告-开标实际，过滤掉旧项目
            List<Long> projectIds= list.stream()
                    .map(BusiTenderProject::getProjectId)
                    .collect(Collectors.toList());
            if (projectIds.size()>0){
                List<BusiTenderNotice> tenderNotices = iBusiTenderNoticeService.list(
                        new QueryWrapper<BusiTenderNotice>()
                                .in("project_id", projectIds)
                                .gt("bid_opening_time", new Date()));
                // 提取项目ID集合
                List<Long> newProjectIds;
                if (tenderNotices != null && !tenderNotices.isEmpty()) {
                    newProjectIds = tenderNotices.stream()
                            .map(BusiTenderNotice::getProjectId)
                            .collect(Collectors.toList());
                } else {
                    newProjectIds = new ArrayList<>();
                }
                // 保留原始列表，创建新的过滤后的列表
                List<BusiTenderProject> filteredList = list.stream()
                        .filter(project -> newProjectIds.contains(project.getProjectId()))
                        .collect(Collectors.toList());
                return AjaxResult.success(filteredList);
            }
        }
        return AjaxResult.success();

    }

    @Override
    public AjaxResult supplierViewProdect(LoginUser loginUser) {
        List<BusiTenderDocumentsDownload> documentsDownloads = busiTenderDocumentsDownloadService.list(
                new QueryWrapper<BusiTenderDocumentsDownload>().eq("bidder_id", loginUser.getEntId())
                        .inSql("project_id", "select project_id from busi_tender_project where project_status < 40")
        );
        if(documentsDownloads!=null && !documentsDownloads.isEmpty()) {
            List<Long> projectIds = documentsDownloads.stream().map(item -> item.getProjectId()).collect(Collectors.toList());
            ProjectInfoAndIdsVo projectsByProjectIds = iBusiTenderProjectService.getProjectsByProjectIds(projectIds);
            Map<Long, BusiTenderProject> projectsMap = projectsByProjectIds.getProjectsMap();

            NoticeInfoAndIdsVo tenderNotisByProjectId = iBusiTenderNoticeService.getTenderNotisByProjectId(projectIds);
            Map<Long, BusiTenderNotice> noticeInfoAndIdsVoMap = tenderNotisByProjectId.getNoticesMap();

            documentsDownloads.forEach(item->{
                item.setProject(projectsMap.get(item.getProjectId()));
                item.setNotice(noticeInfoAndIdsVoMap.get(item.getProjectId()));
            });
        }
        return AjaxResult.success(documentsDownloads);
    }

    @Override
    public AjaxResult supplierisWinProdect(LoginUser loginUser) {
        List<SysRole> roles = loginUser.getUser().getRoles();
        for (SysRole role : roles) {
            if (role.getRoleKey().equals("supplier")){
                //供应商查自己中标的项目
                QueryWrapper<BusiWinningBidderNotice> queryWrapper=new QueryWrapper<BusiWinningBidderNotice>();
                queryWrapper.eq("del_flag", 0);
                queryWrapper.eq("bidder_id", loginUser.getEntId());
                queryWrapper.eq("notice_type",1);
                //获取中标信息
                List<BusiWinningBidderNotice> busiWinningBidderNotices = busiWinningBidderNoticeService.list(queryWrapper);
                if (!busiWinningBidderNotices.isEmpty()){
                    //取中标信息的prijectids
                    List<Long> projectIds = busiWinningBidderNotices.stream()
                            .map(BusiWinningBidderNotice::getProjectId)
                            .collect(Collectors.toList());
                    //查询项目信息
                    List<BusiTenderProject> tenderProjects = iBusiTenderProjectService.list(new QueryWrapper<BusiTenderProject>()
                            .in("project_id",projectIds).eq("project_status",70));

                    Map<Long, BusiWinningBidderNotice> bidderInfoMap = busiWinningBidderNotices.stream()
                            .collect(Collectors.toMap(BusiWinningBidderNotice::getProjectId, bidderNotice -> bidderNotice));

                    tenderProjects.forEach(tenderProject -> {
                        BusiWinningBidderNotice bidderNotice = bidderInfoMap.get(tenderProject.getProjectId());
                        if (bidderNotice != null) {
                            tenderProject.setBidderNotice(bidderNotice);
                        }
                    });

                    return AjaxResult.success(tenderProjects);
                }else {
                    return AjaxResult.error("没有查到该用户已的中标信息");
                }


            }
            if (role.getRoleKey().equals("purchaser")||role.getRoleKey().equals("agency")){
                //通过项目id查询已中标的项目
                QueryWrapper<BusiTenderProject> projectQueryWrapper=new QueryWrapper<BusiTenderProject>();
                projectQueryWrapper.eq("del_flag", 0);
                projectQueryWrapper.eq("project_status",70);
                projectQueryWrapper.eq("tenderer_id", loginUser.getEntId()).or().eq("agency_id", loginUser.getEntId());
                List<BusiTenderProject> tenderProjects = iBusiTenderProjectService.list(projectQueryWrapper);
                if (!tenderProjects.isEmpty()){
                    List<Long> projectIds = tenderProjects.stream()
                            .map(BusiTenderProject::getProjectId)
                            .collect(Collectors.toList());

                    QueryWrapper<BusiWinningBidderNotice> queryWrapper=new QueryWrapper<BusiWinningBidderNotice>();
                    //queryWrapper.eq("is_win", 1);
                    queryWrapper.eq("del_flag", 0);
                    queryWrapper.in("project_id", projectIds);
                    queryWrapper.eq("notice_type",1);
                    //获取中标信息
                    List<BusiWinningBidderNotice> busiWinningBidderNotices = busiWinningBidderNoticeService.list(queryWrapper);

                    Map<Long, BusiWinningBidderNotice> bidderNoticeMap = busiWinningBidderNotices.stream()
                            .collect(Collectors.toMap(BusiWinningBidderNotice::getProjectId, bidderNotice -> bidderNotice));

                    tenderProjects.forEach(tenderProject -> {
                        BusiWinningBidderNotice bidderNotice = bidderNoticeMap.get(tenderProject.getProjectId());
                        if (bidderNotice != null) {
                            tenderProject.setBidderNotice(bidderNotice);
                        }
                    });
                }
                return AjaxResult.success(tenderProjects);
            }
            if (role.getRoleKey().equals("admin")){
                QueryWrapper<BusiWinningBidderNotice> queryWrapper=new QueryWrapper<BusiWinningBidderNotice>();
                //queryWrapper.eq("is_win", 1);
                queryWrapper.eq("del_flag", 0);
                queryWrapper.eq("notice_type",1);

                //获取中标信息
                List<BusiWinningBidderNotice> busiWinningBidderNotices = busiWinningBidderNoticeService.list(queryWrapper);
                if (busiWinningBidderNotices.isEmpty()){
                    AjaxResult.error("没有查询到中标信息");
                }

                List<Long> projectIds = busiWinningBidderNotices.stream()
                        .map(BusiWinningBidderNotice::getProjectId)
                        .collect(Collectors.toList());


                QueryWrapper<BusiTenderProject> projectQueryWrapper=new QueryWrapper<BusiTenderProject>();
                projectQueryWrapper.eq("del_flag", 0).eq("project_status",70);
                projectQueryWrapper.in("project_id",projectIds);
                List<BusiTenderProject> tenderProjects = iBusiTenderProjectService.list(projectQueryWrapper);
                if (!tenderProjects.isEmpty()){
                    Map<Long, BusiWinningBidderNotice> bidderInfoMap = busiWinningBidderNotices.stream()
                            .collect(Collectors.toMap(BusiWinningBidderNotice::getProjectId, bidderInfo -> bidderInfo));

                    tenderProjects.forEach(tenderProject -> {
                        BusiWinningBidderNotice bidderNotice = bidderInfoMap.get(tenderProject.getProjectId());
                        if (bidderNotice != null) {
                            tenderProject.setBidderNotice(bidderNotice);
                        }
                    });
                }
                return AjaxResult.success(tenderProjects);
            }


        }

        return null;
    }

    @Override
    public List<BusiTenderProject> selectBidOpeningProject(BusiTenderProject busiTenderProject) {
        return baseMapper.selectBidOpeningProject(busiTenderProject);
    }
    @Override
    public void freeMarker(Long  projectId) throws IOException, TemplateException {
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        BidOpeningResultVo bidOpeningResultVo=new BidOpeningResultVo();
        try {
            FtlToPdfConverter converter = new FtlToPdfConverter();
            Map<String, Object> dataModel = new HashMap<>(); // 创建数据模型
            QueryWrapper<BusiTenderNotice> queryWrapper=new QueryWrapper<BusiTenderNotice>();
            queryWrapper.eq("project_id",tenderProject.getProjectId());
            queryWrapper.eq("notice_stats",1);
            queryWrapper.eq("del_flag",0);
            List<BusiTenderNotice> tenderNotices = iBusiTenderNoticeService.list();
            dataModel.put("xmmc",tenderProject.getProjectName());
            dataModel.put("cgr",tenderProject.getTendererName());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dataModel.put("kbsj",dateFormat.format(tenderNotices.get(0).getBidOpeningTime()));
            bidOpeningResultVo.setBidderInfos(new ArrayList<BusiBidderInfo>());

            List<BusiBidderInfo> list= iBusiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id",projectId).eq("del_flag",0));

           /* BusiBidderInfo byId = iBusiBidderInfoService.getById("1144387454125061");
            List<BusiBidderInfo> list=new ArrayList<>();
            list.add(byId);*/
            dataModel.put("bidderInfos",list);
            converter.convertFtlToPdf("/opening_record/openRecord.ftl", dataModel, "D:/ruoyi/opening_record/openRecord.pdf");
            System.out.println("PDF文件生成成功！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Override
    public BusiTenderProject getSubById(Long projectId){
        BusiTenderProject project = getById(projectId);
        project.setTenderModeName(dictDataService.selectDictLabel("busi_tender_mode", project.getTenderMode()));
        project.setProjectTypeName(dictDataService.selectDictLabel("busi_project_type", project.getProjectType()));

        if(StringUtils.isNoneBlank(project.getProjectArea())) {
            BaseTreeData tree = baseTreeDataService.getById(Long.valueOf(project.getProjectArea()));
            project.setProjectAreaName(tree.getName());
            project.setProjectAreaCode(tree.getCode());
        }
        if(StringUtils.isNoneBlank(project.getTenderFundSource())) {
            project.setTenderFundSourceName(
                    dictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
        }
        if (StringUtils.isNoneBlank(project.getProjectIndustry())) {
            String[] industries = project.getProjectIndustry().split(",");
            String lastIndustry = industries[industries.length - 1];
            BaseTreeData tree = baseTreeDataService.getById(Long.valueOf(lastIndustry));
            project.setProjectIndustryName(tree.getName());
        }
        return project;
    }

    @Override
    public AjaxResult codePage(Long projectId) {

        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);

        BusiWinningBidderNotice bidderNoticeServiceOne = busiWinningBidderNoticeService.getOne(new QueryWrapper<BusiWinningBidderNotice>().eq("project_id", projectId));

        BusiWinningBidderAdviceNote adviceNoteServiceOne = busiWinningBidderAdviceNoteService.getOne(new QueryWrapper<BusiWinningBidderAdviceNote>().eq("project_id", projectId));

        List<BusiAttachment> busiAttachments = iBusiAttachmentService.selectByBusiId(adviceNoteServiceOne.getNoteId());

        Map<String,Object> map=new HashMap<>();
        map.put("tenderProject",tenderProject);
        map.put("bidderNoticeServiceOne",bidderNoticeServiceOne);
        map.put("adviceNoteServiceOne",adviceNoteServiceOne);

        System.out.println(AttachmentUtil.urlToReal(busiAttachments.get(0).getFilePath()));
        String fileUrl = configService.selectConfigByKey("fileUrl");
        map.put("downUrl",fileUrl+busiAttachments.get(0).getFilePath());
        return AjaxResult.success(map);
    }


}
