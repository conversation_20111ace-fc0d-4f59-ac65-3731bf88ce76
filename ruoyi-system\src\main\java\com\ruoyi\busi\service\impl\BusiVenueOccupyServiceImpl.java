package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.busi.domain.BusiVenueOccupy;
import com.ruoyi.busi.mapper.BusiVenueOccupyMapper;
import com.ruoyi.busi.service.IBusiVenueOccupyService;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 场地占用Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BusiVenueOccupyServiceImpl extends ServiceImpl<BusiVenueOccupyMapper, BusiVenueOccupy> implements IBusiVenueOccupyService {
    /**
     * 查询场地占用列表
     *
     * @param busiVenueOccupy 场地占用
     * @return 场地占用
     */
    @Override
    public List<BusiVenueOccupy> selectList(BusiVenueOccupy busiVenueOccupy) {
        QueryWrapper<BusiVenueOccupy> busiVenueOccupyQueryWrapper = new QueryWrapper<>();
        busiVenueOccupyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiVenueOccupy.getNoticeId()), "notice_id", busiVenueOccupy.getNoticeId());
        busiVenueOccupyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiVenueOccupy.getVenueId()), "venue_id", busiVenueOccupy.getVenueId());
        busiVenueOccupyQueryWrapper.like(ObjectUtil.isNotEmpty(busiVenueOccupy.getVenueName()), "venue_name", busiVenueOccupy.getVenueName());
        busiVenueOccupyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiVenueOccupy.getVenueType()), "venue_type", busiVenueOccupy.getVenueType());
        String beginOccupyStartTime = busiVenueOccupy.getParams().get("beginOccupyStartTime") != null ? busiVenueOccupy.getParams().get("beginOccupyStartTime") + "" : "";
        String endOccupyStartTime = busiVenueOccupy.getParams().get("endOccupyStartTime") + "" != null ? busiVenueOccupy.getParams().get("endOccupyStartTime") + "" : "";
        busiVenueOccupyQueryWrapper.between(ObjectUtil.isNotEmpty(beginOccupyStartTime) && ObjectUtil.isNotEmpty(endOccupyStartTime), "occupy_start_time", beginOccupyStartTime, endOccupyStartTime);
        String beginOccupyEndTime = busiVenueOccupy.getParams().get("beginOccupyEndTime") != null ? busiVenueOccupy.getParams().get("beginOccupyEndTime") + "" : "";
        String endOccupyEndTime = busiVenueOccupy.getParams().get("endOccupyEndTime") + "" != null ? busiVenueOccupy.getParams().get("endOccupyEndTime") + "" : "";
        busiVenueOccupyQueryWrapper.between(ObjectUtil.isNotEmpty(beginOccupyEndTime) && ObjectUtil.isNotEmpty(endOccupyEndTime), "occupy_end_time", beginOccupyEndTime, endOccupyEndTime);
        busiVenueOccupyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiVenueOccupy.getPeopleNumber()), "people_number", busiVenueOccupy.getPeopleNumber());
        busiVenueOccupyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiVenueOccupy.getRemark()), "remark", busiVenueOccupy.getRemark());
        busiVenueOccupyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiVenueOccupy.getDelFlag()), "del_flag", busiVenueOccupy.getDelFlag());
        String beginCreateTime = busiVenueOccupy.getParams().get("beginCreateTime") != null ? busiVenueOccupy.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = busiVenueOccupy.getParams().get("endCreateTime") + "" != null ? busiVenueOccupy.getParams().get("endCreateTime") + "" : "";
        busiVenueOccupyQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        busiVenueOccupyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiVenueOccupy.getCreateBy()), "create_by", busiVenueOccupy.getCreateBy());
        String beginUpdateTime = busiVenueOccupy.getParams().get("beginUpdateTime") != null ? busiVenueOccupy.getParams().get("beginUpdateTime") + "" : "";
        String endUpdateTime = busiVenueOccupy.getParams().get("endUpdateTime") + "" != null ? busiVenueOccupy.getParams().get("endUpdateTime") + "" : "";
        busiVenueOccupyQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime, endUpdateTime);
        busiVenueOccupyQueryWrapper.eq(ObjectUtil.isNotEmpty(busiVenueOccupy.getBidEvaluationPeriod()), "bid_evaluation_period", busiVenueOccupy.getBidEvaluationPeriod());
        busiVenueOccupyQueryWrapper.orderByDesc("create_time");
        return list(busiVenueOccupyQueryWrapper);
    }

    @Override
    public List<BusiVenueOccupy> getListIgnoreDeleted(BusiVenueOccupy busiVenueOccupy){
        return baseMapper.getListIgnoreDeleted(busiVenueOccupy);
    }

    @Override
    public boolean deleteByNoticeId(Long noticeId) {
        try {
            QueryWrapper<BusiVenueOccupy> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.eq("notice_id", noticeId);
            return remove(deleteWrapper);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
