package com.ruoyi.procurement.vo;

import java.util.ArrayList;
import java.util.List;

/**
 * 磋商工程的关键字对应关系
 */
public enum ProcurementScoreItemKeywordEnum {
    /*
    工程类评审因素
     */
    //资格性评审
    GC_ZG_FHESET(1, 0, "符合《中华人民共和国政府采购法》第二十二条规定", "申请人资格要求："),
    GC_ZG_TDZGYQ(1, 0, "特定资格要求", "二、申请人的资格要求"),
    GC_ZG_XYCX(1, 0, "信用查询", "4.信用查询："),
    //符合型评审
    GC_FH_XYRMC(1, 0, "响应人名称", "•符合性"),
    GC_FH_QZGZ(1, 0, "签字盖章", "签字和盖章要求"),
    GC_FH_YBJGCLQD(1, 0, "已标价工程量清单", "（一）工程量清单及图纸"),
    GC_FH_HTLXQX(1, 0, "合同履行期限", "7.合同履行期限："),
    GC_FH_ZBQ(1, 0, "质保期", "6.质保期："),
    GC_FH_TBBJ(1, 0, "投标报价", "4.预算总金额："),
    GC_FH_QTYQ(1, 0, "其他要求", "•符合性"),
    //技术标评审
    GC_JS_SGFA(1, 0, "施工方案及技术措施、质量管理体系与措施、安全管理体系与措施、环境保护管理体系与措施及拟投入资源配备计划",
            "•技术部"),
    GC_JS_GCJD(1, 0, "工程进度计划与措施、施工进度或施工网络图、施工总平面布置图", "•技术部"),
    GC_JS_JNJP(1, 0, "节能减排（绿色施工、工艺创新）在本工程的具体应用措施", "•技术部"),
    GC_JS_XGY(1, 0, "新工艺（新技术、新设备、新材料）的采用程度", "•技术部"),
    GC_JS_FXGLCS(1, 0, "风险管理措施", "•技术部"),
    //商务标评审
    GC_SW_NPXMGLRY(1, 0, "拟派项目管理人员", "•商务部"),
    GC_SW_XMJSFZR(1, 0, "项目技术负责人", "•商务部"),
    GC_SW_TXRZ(1, 0, "体系认证", "•商务部"),
    GC_SW_LSYJ(1, 0, "以往业绩", "•商务部"),

    /*
    服务类评审因素
     */
    //资格性评审
    FW_ZG_FHESET(1, 1, "符合《中华人民共和国政府采购法》第二十二条规定", "申请人资格要求："),
    FW_ZG_TDZGYQ(1, 1, "特定资格要求", "二、申请人的资格要求"),
    FW_ZG_XYCX(1, 1, "信用查询", "4.信用查询："),
    //符合型评审
    FW_FH_XYRMC(1, 1, "响应人名称", "•符合性"),
    FW_FH_XYWJGS(1, 1, "响应文件格式", "•符合性"),
    FW_FH_QZGZ(1, 1, "签字盖章", "签字和盖章要求"),
    FW_FH_FWQX(1, 1, "服务期限", "7.服务期限："),
    FW_FH_TBBJ(1, 1, "投标报价", "4.预算总金额："),
    FW_FH_QTYQ(1, 1, "其他要求", "•符合性"),
    //技术标评审
    FW_JS_FWFA(1, 1, "服务方案","(1)"),
    //商务标评审
    FW_SW_XMZRY(1, 1, "项目组人员", "(2)"),
    FW_SW_LSYJ(1, 1, "类似业绩", "(2)"),
    FW_SW_TXRZ(1, 1, "体系认证", "(2)"),
    FW_SW_YQSB(1, 1, "仪器设备", "(2)"),
    /*
    货物类评审因素
     */
    //资格性评审
    HW_ZG_FHESET(1, 2, "符合《中华人民共和国政府采购法》第二十二条规定", "、资格条件承诺函"),
    HW_ZG_TDZGYQ(1, 2, "特定资格要求", "、申请人的资格要求："),
    HW_ZG_XYCX(1, 2, "信用查询", "、申请人的资格要求："),
    //符合型评审
    HW_FH_XYRMC(1, 2, "响应人名称", "与营业执照一致，相关证件须在有效期内"),
    HW_FH_XYWJGS(1, 2, "响应文件格式", "符合第六章“响应文件内容及格式”的要求"),
    HW_FH_QZGZ(1, 2, "签字盖章", "签字和盖章要求"),
    HW_FH_GHQ(1, 2, "供货期", "6.供货期："),
    HW_FH_TBBJ(1, 2, "投标报价", "4.预算总金额："),
    HW_FH_QTYQ(1, 2, "其他要求", "采购文件规定的其他响应无效情形"),
    //技术标评审
    HW_JS_KLHJSCS(1, 2, "可量化技术参数", "*技术部分"),
    //商务标评审
    HW_SW_JNHB(1, 2, "节能环保", "*商务部分"),
    HW_SW_SHFW(1, 2, "售后服务", "*商务部分"),
    HW_SW_TXRZ(1, 2, "体系认证", "*商务部分"),
    HW_SW_ZSCQ(1, 2, "知识产权", "*商务部分"),
    HW_SW_LSYJ(1, 2, "类似业绩", "*商务部分"),
    HW_SW_ZBQ(1, 2, "质保期加分项", "*商务部分"),
    /*
    询价货物类评审因素
     */
    //资格性评审
    XJ_HW_ZG_FHESET(3, 2, "符合《中华人民共和国政府采购法》第二十二条规定", "二、申请人的资格要求"),
    XJ_HW_ZG_TDZGYQ(3, 2, "特定资格要求", "3.本项目的特定资格要求:"),
    XJ_HW_ZG_XYCX(3, 2, "信用查询", "4.信用查询："),
    XJ_HW_ZG_BSLHT(3, 2, "不是联合体", "5.本项目"),
    //符合型评审
    XJ_HW_FH_XYRMC(3, 2, "响应人名称", "与营业执照一致"),
    XJ_HW_FH_XYNR(3, 2, "响应内容", "第三章  采购需求"),
    XJ_HW_FH_CGXQ(3, 2, "采购需求", "第三章  采购需求"),
    XJ_HW_FH_GHQX(3, 2, "供货期限", "6.供货期限"),
    XJ_HW_FH_TBBJ(3, 2, "投标报价", "4.预算金额"),;
    /*
    采购方式
     */
    private Integer tenderMode;
    /*
    项目类别
     */
    private Integer projectType;
    private String scoreItemName;
    private String keyword;

    ProcurementScoreItemKeywordEnum(Integer tenderMode, Integer projectType, String scoreItemName, String keyword) {
        this.tenderMode = tenderMode;
        this.projectType = projectType;
        this.scoreItemName = scoreItemName;
        this.keyword = keyword;
    }

    public Integer getTenderMode() {
        return tenderMode;
    }
    public Integer getProjectType() {
        return projectType;
    }

    public String getKeyword() {
        return keyword;
    }

    public String getScoreItemName() {
        return scoreItemName;
    }

    public static List<ProcurementScoreItemKeywordEnum> getKeywordList(String tenderMode, String projectType){
        return getKeywordList(Integer.valueOf(tenderMode), Integer.valueOf(projectType));
    }
    public static List<ProcurementScoreItemKeywordEnum> getKeywordList(Integer tenderMode, Integer projectType){
        ProcurementScoreItemKeywordEnum[] enums = ProcurementScoreItemKeywordEnum.values();
        List<ProcurementScoreItemKeywordEnum> keywords = new ArrayList<>();
        for (ProcurementScoreItemKeywordEnum e : enums) {
            if (e.getTenderMode().equals(tenderMode) && e.getProjectType().equals(projectType)) {
                keywords.add(e);
            }
        }
        return keywords;
    }
}
