{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\agentRoom.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\agentRoom.vue", "mtime": 1753950779107}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\babel.config.js", "mtime": 1750995480034}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "names": ["_ready", "_interopRequireDefault", "require", "_publicity", "_decryption", "_bidAnnouncement", "_end", "_index", "_info", "_user", "_opening", "_default", "exports", "default", "components", "Ready", "publicity", "decryption", "bidAnnouncement", "end", "data", "node", "userInfo", "projectInfo", "baseUrl", "process", "env", "VUE_APP_BASE_API", "message", "text_content", "ws", "anonymous", "recordContent", "sendId", "content", "sendTime", "Sender", "isLink", "syncedHeight", "currentTime", "timeInterval", "watch", "handler", "_this2", "setTimeout", "element", "document", "getElementById", "console", "log", "offsetHeight", "deep", "methods", "init", "_this3", "promise1", "bidInfo", "bidOpeningTime", "getTodayStartWithDate", "bidOpeningEndTime", "getTodayEndWithDate", "projectId", "$route", "query", "then", "response", "code", "$modal", "msgwarning", "msg", "promise2", "getUserProfile", "Promise", "all", "result", "join", "handleStatus", "$store", "getters", "agentBidOpenStatus", "updateStatus", "$refs", "head", "getBidStatus", "formatBidOpeningTime", "time", "formatDateOption", "formatBidOpeningTimeTwo", "socketUrl", "replace", "url", "concat", "VUE_APP_WEBSOCKET_API", "entId", "wsurl", "WebSocket", "self", "ws_heartCheck", "timeout", "timeoutObj", "serverTimeoutObj", "start", "_this4", "send", "close", "reset", "clearTimeout", "stop", "onopen", "event", "onmessage", "initdataList", "initChat", "onclose", "exit", "scrollToBottom", "alert", "operateSend", "_this5", "chatHistory", "_this6", "$nextTick", "container", "messagesContainer", "scrollTop", "scrollHeight", "updateTime", "_this7", "clearInterval", "_this", "getSystemTime", "ct", "Date", "setInterval", "setSeconds", "getSeconds", "created", "mounted", "updated", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/bidOpeningHall/agentRoom.vue"], "sourcesContent": ["<template>\r\n\t<!-- 开标室主页面 -->\r\n\t<div>\r\n\t\t<!-- 头部组件，负责流程状态展示和切换 -->\r\n\t\t<BidHeadtwo ref=\"head\" @updateStatus=\"handleStatus\"></BidHeadtwo>\r\n\t\t<div class=\"bidOpeningHall\">\r\n\t\t\t<!-- 主体区域，包含流程节点内容 -->\r\n\t\t\t<el-card id=\"main\" class=\"box-card\">\r\n\t\t\t\t<!-- 平台当前时间与聊天室连接状态 -->\r\n\t\t\t\t<div style=\"height: 10px;\">\r\n\t\t\t\t\t<div style=\"padding: 5px 0 0 20px; float: left;\">平台当前时间：<span >{{ currentTime }}</span></div>\r\n\t\t\t\t\t<div style=\"padding: 5px 20px 0 0; float: right;\">\r\n\t\t\t\t\t\t连接状态：<span :style=\"`color:${isLink ? 'green' : 'red'}`\">{{\r\n\t\t\t\t\t\t\tisLink ? \"已连接\" : \"已断连，请刷新重连\"\r\n\t\t\t\t\t\t}}</span>\r\n\t\t\t\t\t</div></div>\r\n\t\t\t\t<!-- 根据流程节点动态渲染不同子组件 -->\r\n\t\t\t\t<ready ref=\"ready\" v-if=\"node == 'ready' && projectInfo\" :projectInfo=\"projectInfo\" @sendMessage=\"operateSend\"></ready>\r\n\t\t\t\t<publicity ref=\"publicity\" v-if=\"node == 'publicity'\" :projectInfo=\"projectInfo\" @sendMessage=\"operateSend\"></publicity>\r\n\t\t\t\t<decryption ref=\"decryption\" v-if=\"node == 'decryption' && projectInfo\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" @sendMessage=\"operateSend\"></decryption>\r\n\t\t\t\t<bidAnnouncement ref=\"bidAnnouncement\" v-if=\"node == 'bidAnnouncement'\" @sendMessage=\"operateSend\"></bidAnnouncement>\r\n\t\t\t\t<end ref=\"end\" v-if=\"node == 'end'\" @sendMessage=\"operateSend\"></end>\r\n\t\t\t</el-card>\r\n\t\t\t<!-- 聊天室侧边栏 -->\r\n\t\t\t<el-card class=\"box-card\" style=\"width: 15%;\">\r\n\t\t\t\t<div class=\"im\">\r\n\t\t\t\t\t<div class=\"im-title\">{{ userInfo.nickName }}</div>\r\n\t\t\t\t\t<!-- 聊天内容区域 -->\r\n\t\t\t\t\t<div ref=\"messagesContainer\" class=\"im-content\" :style=\"{height: syncedHeight }\">\r\n\t\t\t\t\t\t<div v-for=\"(itemc, indexc) in recordContent\" :key=\"indexc\">\r\n\t\t\t\t\t\t\t<div class=\"sysMessage\" v-if=\"itemc.type == 0\">\r\n\t\t\t\t\t\t\t\t<!-- 系统消息（可扩展） -->\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div v-else>\r\n\t\t\t\t\t\t\t\t<!-- 他人消息 -->\r\n\t\t\t\t\t\t\t\t<div class=\"word\" v-if=\"itemc.sendId !== userInfo.entId\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"message_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{anonymous? \"*******\":itemc.sendName }}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"info-content\">{{ itemc.content }}</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"message_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<!-- 自己消息 -->\r\n\t\t\t\t\t\t\t\t<div class=\"word-my\" v-else>\r\n\t\t\t\t\t\t\t\t\t<div class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"info-content\">{{ itemc.content }}</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"Sender_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<!-- 聊天输入与发送 -->\r\n\t\t\t\t\t<div class=\"im-operation\">\r\n\t\t\t\t\t\t<div style=\"margin-right:5px\">\r\n\t\t\t\t\t\t\t<el-input v-model=\"message\" placeholder=\"输入内容\"></el-input>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-button style=\"height: 36px;background: #176ADB;color:#fff\" @click=\"send\">发送</el-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</el-card>\r\n\t\t</div>\r\n\t\t<!-- 页脚 -->\r\n\t\t<Foot></Foot>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n// 引入各流程节点组件\r\nimport Ready from \"./agentComponent/ready.vue\";\r\nimport publicity from \"./agentComponent/publicity.vue\";\r\nimport decryption from \"./agentComponent/decryption.vue\";\r\nimport bidAnnouncement from \"./agentComponent/bidAnnouncement.vue\";\r\nimport end from \"./agentComponent/end.vue\";\r\n\r\n// 工具方法与API接口\r\nimport {\r\n\tformatDateOption,\r\n\tgetTodayStartWithDate,\r\n\tgetTodayEndWithDate,\r\n} from \"@/utils/index\";\r\nimport { bidInfo, chatHistory } from \"@/api/onlineBidOpening/info\";\r\nimport { getUserProfile } from \"@/api/system/user\";\r\nimport { getSystemTime } from \"@/api/bid/opening\";\r\n\r\nexport default {\r\n\tcomponents: { Ready, publicity, decryption, bidAnnouncement, end },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 当前流程节点\r\n\t\t\tnode: \"ready\",\r\n\t\t\t// 当前用户信息\r\n\t\t\tuserInfo: {},\r\n\t\t\t// 项目信息\r\n\t\t\tprojectInfo: null,\r\n\t\t\t// 接口基础地址\r\n\t\t\tbaseUrl: process.env.VUE_APP_BASE_API,\r\n\t\t\t// 聊天输入内容\r\n\t\t\tmessage: \"\",\r\n\t\t\t// 文本内容（用于日志等）\r\n\t\t\ttext_content: \"\",\r\n\t\t\t// websocket 实例\r\n\t\t\tws: null,\r\n\t\t\t// 是否匿名（根据流程节点控制）\r\n\t\t\tanonymous: true,\r\n\t\t\t// 聊天记录内容\r\n\t\t\trecordContent: [\r\n\t\t\t\t// 示例数据，实际会被接口数据覆盖\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 1,\r\n\t\t\t\t\tcontent: \"Nice to meet you.\",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:00:00\",\r\n\t\t\t\t\tSender: \"张三\",\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 2,\r\n\t\t\t\t\tcontent: \"Nice to meet you.too\",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:01:00\",\r\n\t\t\t\t\tSender: \"李四\",\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 2,\r\n\t\t\t\t\tcontent: \"How are you? \",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:02:00\",\r\n\t\t\t\t\tSender: \"李四\",\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 1,\r\n\t\t\t\t\tcontent: \"I'am fine,Thank you.\",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:03:00\",\r\n\t\t\t\t\tSender: \"张三\",\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t\t// websocket连接状态\r\n\t\t\tisLink: false,\r\n\t\t\t// 聊天内容区高度（与主卡片同步）\r\n\t\t\tsyncedHeight: '450px', // 初始高度\r\n\t\t\t// 平台当前时间\r\n\t\t\tcurrentTime:null,\r\n\t\t\t// 时间更新定时器\r\n\t\t\ttimeInterval: null\r\n\t\t};\r\n\t},\r\n\twatch: {\r\n\t\t// 监听流程节点变化，动态调整聊天区高度\r\n\t\tnode: {\r\n\t\t\thandler() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tvar element = document.getElementById('main');\r\n\t\t\t\t\tconsole.log('element.clientHeight', element.offsetHeight);\r\n\t\t\t\t\tthis.syncedHeight = element.offsetHeight - 120 + 'px'\r\n\t\t\t\t}, 10);\r\n\r\n\t\t\t},\r\n\t\t\tdeep: true\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 初始化，获取项目信息和用户信息\r\n\t\tinit() {\r\n\t\t\t// 获取开标项目信息\r\n\t\t\tconst promise1 = bidInfo({\r\n\t\t\t\tbidOpeningTime: getTodayStartWithDate(),\r\n\t\t\t\tbidOpeningEndTime: getTodayEndWithDate(),\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t}).then((response) => {\r\n\t\t\t\tif (response.code == 200) {\r\n\t\t\t\t\tthis.projectInfo = response.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$modal.msgwarning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// 获取用户信息\r\n\t\t\tconst promise2 = getUserProfile().then((response) => {\r\n\t\t\t\tthis.userInfo = response.data;\r\n\t\t\t});\r\n\r\n\t\t\t// 两个接口都完成后，建立websocket连接\r\n\t\t\tPromise.all([promise1, promise2]).then((result) => {\r\n\t\t\t\tthis.join();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 处理流程节点切换\r\n\t\thandleStatus(data) {\r\n\t\t\t// 根据流程状态判断是否匿名\r\n\t\t\tthis.anonymous = this.$store.getters.agentBidOpenStatus >= 2 ? false : true;\r\n\t\t\tswitch (data) {\r\n\t\t\t\tcase \"签到\":\r\n\t\t\t\t\tthis.node = \"signIn\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"开标准备\":\r\n\t\t\t\t\tthis.node = \"ready\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"投标人公示\":\r\n\t\t\t\t\tthis.node = \"publicity\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"标书解密\":\r\n\t\t\t\t\tthis.node = \"decryption\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"唱标\":\r\n\t\t\t\t\tthis.node = \"bidAnnouncement\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"开标结束\":\r\n\t\t\t\t\tthis.node = \"end\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 节点更新通知，刷新头部状态\r\n\t\tupdateStatus() {\r\n\t\t\tthis.$refs.head.getBidStatus();\r\n\t\t},\r\n\t\t// 格式化开标时间显示 年-月-日\r\n\t\tformatBidOpeningTime(time) {\r\n\t\t\treturn formatDateOption(time, \"date\");\r\n\t\t},\r\n\t\t// 格式化开标时间显示 时-分-秒\r\n\t\tformatBidOpeningTimeTwo(time) {\r\n\t\t\treturn formatDateOption(time, \"time\");\r\n\t\t},\r\n\r\n\t\t// 建立websocket连接，处理心跳、消息、断线重连等\r\n\t\tjoin() {\r\n\t\t\tlet socketUrl = this.baseUrl.replace(\"http\", \"ws\");\r\n\t\t\tconsole.log(\"socketUrl\", socketUrl);\r\n\t\t\t// 拼接websocket地址\r\n\t\t\tthis.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n\t\t\tconst wsurl = this.url;\r\n\t\t\tthis.ws = new WebSocket(wsurl);\r\n\t\t\tconst self = this;\r\n\t\t\t// 心跳检测函数，防止连接超时断开\r\n\t\t\tconst ws_heartCheck = {\r\n\t\t\t\ttimeout: 5000, // 5秒\r\n\t\t\t\ttimeoutObj: null,\r\n\t\t\t\tserverTimeoutObj: null,\r\n\t\t\t\tstart: function () {\r\n\t\t\t\t\tthis.timeoutObj = setTimeout(() => {\r\n\t\t\t\t\t\t// 发送心跳包\r\n\t\t\t\t\t\tself.ws.send(\"ping\");\r\n\t\t\t\t\t\tthis.serverTimeoutObj = setTimeout(() => {\r\n\t\t\t\t\t\t\tself.ws.close(); // 超时未响应则断开\r\n\t\t\t\t\t\t}, this.timeout);\r\n\t\t\t\t\t}, this.timeout);\r\n\t\t\t\t},\r\n\t\t\t\treset: function () {\r\n\t\t\t\t\tclearTimeout(this.timeoutObj); // 重置心跳\r\n\t\t\t\t\tclearTimeout(this.serverTimeoutObj);\r\n\t\t\t\t\tthis.start();\r\n\t\t\t\t},\r\n\t\t\t\tstop: function () {\r\n\t\t\t\t\tclearTimeout(this.timeoutObj);\r\n\t\t\t\t\tclearTimeout(this.serverTimeoutObj);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// 连接打开\r\n\t\t\tthis.ws.onopen = function (event) {\r\n\t\t\t\tws_heartCheck.start();\r\n\t\t\t\tself.text_content = self.text_content + \"已经打开开标室连接!\" + \"\\n\";\r\n\t\t\t\tself.isLink = true;\r\n\t\t\t\tconsole.log(self.text_content);\r\n\t\t\t};\r\n\t\t\t// 收到消息\r\n\t\t\tthis.ws.onmessage = function (event) {\r\n\t\t\t\tconsole.log(event.data);\r\n\t\t\t\tif (event.data == \"ping\") {\r\n\t\t\t\t\tws_heartCheck.reset(); // 心跳包\r\n\t\t\t\t} else if (event.data == \"连接成功\") {\r\n\t\t\t\t} else if (event.data == \"signIn\") {\r\n\t\t\t\t\tself.$refs.publicity.initdataList();\r\n\t\t\t\t} else if (event.data == \"supDecrytion\") {\r\n\t\t\t\t\tself.$refs.decryption.initdataList();\r\n\t\t\t\t} else if (\r\n\t\t\t\t\tevent.data == \"ready\" ||\r\n\t\t\t\t\tevent.data == \"bidPublicity\" ||\r\n\t\t\t\t\tevent.data == \"decryption\" ||\r\n\t\t\t\t\tevent.data == \"nextStep\" ||\r\n\t\t\t\t\tevent.data == \"bidAnnouncement\" ||\r\n\t\t\t\t\tevent.data == \"end\" ||\r\n\t\t\t\t\tevent.data == \"flowLabel\"\r\n\t\t\t\t) {\r\n\t\t\t\t\tself.updateStatus();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tself.initChat();\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// 连接关闭，自动重连\r\n\t\t\tthis.ws.onclose = function (event) {\r\n\t\t\t\tself.text_content = self.text_content + \"已经关闭开标室连接!\" + \"\\n\";\r\n\t\t\t\tself.isLink = false;\r\n\t\t\t\tclearTimeout(ws_heartCheck.timeoutObj);\r\n\t\t\t\tclearTimeout(ws_heartCheck.serverTimeoutObj);\r\n\t\t\t\t//断开后自动重连\r\n\t\t\t\tws_heartCheck.stop();\r\n\t\t\t\tself.join();\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 主动断开websocket连接\r\n\t\texit() {\r\n\t\t\tif (this.ws) {\r\n\t\t\t\tthis.ws.close();\r\n\t\t\t\tthis.ws = null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 发送消息（自己输入）\r\n\t\tsend() {\r\n\t\t\tif (this.ws) {\r\n\t\t\t\tthis.ws.send(this.message);\r\n\t\t\t\tthis.message = \"\";\r\n\t\t\t\tthis.scrollToBottom();\r\n\t\t\t} else {\r\n\t\t\t\talert(\"未连接到开标室服务器\");\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 发送消息（子组件调用）\r\n\t\toperateSend(message) {\r\n\t\t\tif (this.ws) {\r\n\t\t\t\tthis.ws.send(message);\r\n\t\t\t} else {\r\n\t\t\t\talert(\"未连接到开标室服务器\");\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 初始化聊天记录\r\n\t\tinitChat() {\r\n\t\t\tchatHistory(this.$route.query.projectId).then((response) => {\r\n\t\t\t\tif (response.code == 200) {\r\n\t\t\t\t\tthis.recordContent = response.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.recordContent = [];\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 聊天内容滚动到底部\r\n\t\tscrollToBottom() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst container = this.$refs.messagesContainer;\r\n\t\t\t\tcontainer.scrollTop = container.scrollHeight;\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 获取并定时更新时间\r\n\t\tupdateTime() {\r\n\t\t\t// 清理之前的定时器\r\n\t\t\tif (this.timeInterval) {\r\n\t\t\t\tclearInterval(this.timeInterval);\r\n\t\t\t}\r\n\r\n\t\t\tvar _this = this;\r\n\t\t\tgetSystemTime().then((result) => {\r\n\t\t\t\tif(result.code==200){\r\n\t\t\t\t\tvar ct = new Date(result.data);\r\n\t\t\t\t\tthis.timeInterval = setInterval(function(){\r\n\t\t\t\t\t\tct.setSeconds(ct.getSeconds() + 1);\r\n\t\t\t\t\t\t_this.currentTime = formatDateOption(ct, \"cdatetime\");\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t_this.updateStatus();\r\n\t\t\t\t\t\t_this.initChat();\r\n\r\n\t\t\t\t\t}, 1000); // 每秒更新时间\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\t// 生命周期钩子\r\n\tcreated() { },\r\n\tmounted() {\r\n\t\tthis.init();\r\n\t\tthis.initChat();\r\n\r\n\t\tthis.updateTime();\r\n\t},\r\n\tupdated() {\r\n\t\tthis.scrollToBottom();\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.timeInterval) {\r\n\t\t\tclearInterval(this.timeInterval);\r\n\t\t}\r\n\t\t// 断开WebSocket连接\r\n\t\tthis.exit();\r\n\t},\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// 覆盖el-card body内边距\r\n::v-deep .el-card__body {\r\n\tpadding: 0;\r\n}\r\n</style>\r\n\r\n<style scoped lang=\"scss\">\r\n// 聊天消息激活态\r\n.active {\r\n\tbackground-color: rgba(149, 250, 190, 1);\r\n}\r\n// 开标室主布局\r\n.bidOpeningHall {\r\n\tposition: relative;\r\n\tbackground-color: #f5f5f5;\r\n\tdisplay: flex;\r\n\tflex-wrap: nowrap;\r\n\tjustify-content: center;\r\n\talign-content: flex-start;\r\n\talign-items: flex-start;\r\n}\r\n// 主体卡片样式\r\n.box-card {\r\n\tmin-height: 600px;\r\n\twidth: 50%;\r\n\tmargin: 15px 5px;\r\n}\r\n// 聊天室整体样式\r\n.im {\r\n\t.im-title {\r\n\t\twidth: 100%;\r\n\t\theight: 50px;\r\n\t\tbackground: #176adb;\r\n\r\n\t\tfont-weight: 500;\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #ffffff;\r\n\t\tletter-spacing: 0;\r\n\r\n\t\tline-height: 50px;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.im-content {\r\n\t\tmargin: 10px;\r\n\t\tbackground: #f5f5f5;\r\n\t\theight: 450px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\t.im-operation {\r\n\t\tdisplay: flex;\r\n\t\tmargin: 0 10px;\r\n\t\tmargin-bottom: 10px;\r\n\t\toverflow: auto;\r\n\t}\r\n}\r\n// 聊天内容区样式\r\n.im-content {\r\n\t// 他人消息气泡\r\n\t.word {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 20px;\r\n\r\n\t\timg {\r\n\t\t\twidth: 40px;\r\n\t\t\theight: 40px;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t\t.info {\r\n\t\t\twidth: 47%;\r\n\t\t\tmargin-left: 10px;\r\n\t\t\t.Sender_time {\r\n\t\t\t\tpadding-right: 12px;\r\n\t\t\t\tpadding-top: 5px;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\theight: 20px;\r\n\t\t\t}\r\n\t\t\t.message_time {\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\theight: 20px;\r\n\t\t\t\tline-height: 20px;\r\n\t\t\t\tmargin-top: -5px;\r\n\t\t\t\tmargin-top: 5px;\r\n\t\t\t}\r\n\t\t\t.info-content {\r\n\t\t\t\tword-break: break-all;\r\n\t\t\t\t// max-width: 45%;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tpadding: 10px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-top: 8px;\r\n\t\t\t\tbackground: #dbdbdb;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t}\r\n\t\t\t//小三角形\r\n\t\t\t.info-content::before {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: -8px;\r\n\t\t\t\ttop: 8px;\r\n\t\t\t\tcontent: \"\";\r\n\t\t\t\tborder-right: 10px solid #dbdbdb;\r\n\t\t\t\tborder-top: 8px solid transparent;\r\n\t\t\t\tborder-bottom: 8px solid transparent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// 自己消息气泡\r\n\t.word-my {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t\tmargin-bottom: 20px;\r\n\t\timg {\r\n\t\t\twidth: 40px;\r\n\t\t\theight: 40px;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t\t.info {\r\n\t\t\twidth: 90%;\r\n\t\t\t// margin-left: 10px;\r\n\t\t\ttext-align: right;\r\n\t\t\t// position: relative;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: flex-end;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tflex-direction: column;\r\n\t\t\t.info-content {\r\n\t\t\t\tword-break: break-all;\r\n\t\t\t\tmax-width: 45%;\r\n\t\t\t\tpadding: 10px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\t// float: right;\r\n\t\t\t\tmargin-right: 10px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-top: 8px;\r\n\t\t\t\tbackground: #a3c3f6;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t}\r\n\t\t\t.Sender_time {\r\n\t\t\t\tpadding-right: 12px;\r\n\t\t\t\tpadding-top: 5px;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\theight: 20px;\r\n\t\t\t}\r\n\t\t\t//小三角形\r\n\t\t\t.info-content::after {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: -8px;\r\n\t\t\t\ttop: 8px;\r\n\t\t\t\tcontent: \"\";\r\n\t\t\t\tborder-left: 10px solid #a3c3f6;\r\n\t\t\t\tborder-top: 8px solid transparent;\r\n\t\t\t\tborder-bottom: 8px solid transparent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA2EA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,gBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,IAAA,GAAAL,sBAAA,CAAAC,OAAA;AAGA,IAAAK,MAAA,GAAAL,OAAA;AAKA,IAAAM,KAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,QAAA,GAAAR,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAfA;AAOA;AAAA,IAAAS,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAUA;EACAC,UAAA;IAAAC,KAAA,EAAAA,cAAA;IAAAC,SAAA,EAAAA,kBAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,eAAA,EAAAA,wBAAA;IAAAC,GAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,OAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACAC,OAAA;MACA;MACAC,YAAA;MACA;MACAC,EAAA;MACA;MACAC,SAAA;MACA;MACAC,aAAA;MACA;MACA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;MACA,GACA;QACAH,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;MACA,GACA;QACAH,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;MACA,GACA;QACAH,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;MACA,EACA;MACA;MACAC,MAAA;MACA;MACAC,YAAA;MAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;IACA;EACA;EACAC,KAAA;IACA;IACApB,IAAA;MACAqB,OAAA,WAAAA,QAAA;QAAA,IAAAC,MAAA;QACAC,UAAA;UACA,IAAAC,OAAA,GAAAC,QAAA,CAAAC,cAAA;UACAC,OAAA,CAAAC,GAAA,yBAAAJ,OAAA,CAAAK,YAAA;UACAP,MAAA,CAAAL,YAAA,GAAAO,OAAA,CAAAK,YAAA;QACA;MAEA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,QAAA,OAAAC,aAAA;QACAC,cAAA,MAAAC,4BAAA;QACAC,iBAAA,MAAAC,0BAAA;QACAC,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF;MACA,GAAAG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAZ,MAAA,CAAA/B,WAAA,GAAA0C,QAAA,CAAA7C,IAAA;QACA;UACAkC,MAAA,CAAAa,MAAA,CAAAC,UAAA,CAAAH,QAAA,CAAAI,GAAA;QACA;MACA;MACA;MACA,IAAAC,QAAA,OAAAC,oBAAA,IAAAP,IAAA,WAAAC,QAAA;QACAX,MAAA,CAAAhC,QAAA,GAAA2C,QAAA,CAAA7C,IAAA;MACA;;MAEA;MACAoD,OAAA,CAAAC,GAAA,EAAAlB,QAAA,EAAAe,QAAA,GAAAN,IAAA,WAAAU,MAAA;QACApB,MAAA,CAAAqB,IAAA;MACA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAxD,IAAA;MACA;MACA,KAAAW,SAAA,QAAA8C,MAAA,CAAAC,OAAA,CAAAC,kBAAA;MACA,QAAA3D,IAAA;QACA;UACA,KAAAC,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;MACA;IACA;IACA;IACA2D,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,YAAA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAAC,IAAA;MACA,WAAAC,uBAAA,EAAAD,IAAA;IACA;IACA;IACAE,uBAAA,WAAAA,wBAAAF,IAAA;MACA,WAAAC,uBAAA,EAAAD,IAAA;IACA;IAEA;IACAV,IAAA,WAAAA,KAAA;MACA,IAAAa,SAAA,QAAAhE,OAAA,CAAAiE,OAAA;MACAzC,OAAA,CAAAC,GAAA,cAAAuC,SAAA;MACA;MACA,KAAAE,GAAA,MAAAC,MAAA,CAAAlE,OAAA,CAAAC,GAAA,CAAAkE,qBAAA,yBAAAD,MAAA,MAAArE,QAAA,CAAAuE,KAAA,OAAAF,MAAA,MAAA7B,MAAA,CAAAC,KAAA,CAAAF,SAAA;MACA,IAAAiC,KAAA,QAAAJ,GAAA;MACA,KAAA5D,EAAA,OAAAiE,SAAA,CAAAD,KAAA;MACA,IAAAE,IAAA;MACA;MACA,IAAAC,aAAA;QACAC,OAAA;QAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,IAAAC,MAAA;UACA,KAAAH,UAAA,GAAAvD,UAAA;YACA;YACAoD,IAAA,CAAAlE,EAAA,CAAAyE,IAAA;YACAD,MAAA,CAAAF,gBAAA,GAAAxD,UAAA;cACAoD,IAAA,CAAAlE,EAAA,CAAA0E,KAAA;YACA,GAAAF,MAAA,CAAAJ,OAAA;UACA,QAAAA,OAAA;QACA;QACAO,KAAA,WAAAA,MAAA;UACAC,YAAA,MAAAP,UAAA;UACAO,YAAA,MAAAN,gBAAA;UACA,KAAAC,KAAA;QACA;QACAM,IAAA,WAAAA,KAAA;UACAD,YAAA,MAAAP,UAAA;UACAO,YAAA,MAAAN,gBAAA;QACA;MACA;MACA;MACA,KAAAtE,EAAA,CAAA8E,MAAA,aAAAC,KAAA;QACAZ,aAAA,CAAAI,KAAA;QACAL,IAAA,CAAAnE,YAAA,GAAAmE,IAAA,CAAAnE,YAAA;QACAmE,IAAA,CAAA3D,MAAA;QACAW,OAAA,CAAAC,GAAA,CAAA+C,IAAA,CAAAnE,YAAA;MACA;MACA;MACA,KAAAC,EAAA,CAAAgF,SAAA,aAAAD,KAAA;QACA7D,OAAA,CAAAC,GAAA,CAAA4D,KAAA,CAAAzF,IAAA;QACA,IAAAyF,KAAA,CAAAzF,IAAA;UACA6E,aAAA,CAAAQ,KAAA;QACA,WAAAI,KAAA,CAAAzF,IAAA,aACA,WAAAyF,KAAA,CAAAzF,IAAA;UACA4E,IAAA,CAAAf,KAAA,CAAAjE,SAAA,CAAA+F,YAAA;QACA,WAAAF,KAAA,CAAAzF,IAAA;UACA4E,IAAA,CAAAf,KAAA,CAAAhE,UAAA,CAAA8F,YAAA;QACA,WACAF,KAAA,CAAAzF,IAAA,eACAyF,KAAA,CAAAzF,IAAA,sBACAyF,KAAA,CAAAzF,IAAA,oBACAyF,KAAA,CAAAzF,IAAA,kBACAyF,KAAA,CAAAzF,IAAA,yBACAyF,KAAA,CAAAzF,IAAA,aACAyF,KAAA,CAAAzF,IAAA,iBACA;UACA4E,IAAA,CAAAhB,YAAA;QACA;UACAgB,IAAA,CAAAgB,QAAA;QACA;MACA;MACA;MACA,KAAAlF,EAAA,CAAAmF,OAAA,aAAAJ,KAAA;QACAb,IAAA,CAAAnE,YAAA,GAAAmE,IAAA,CAAAnE,YAAA;QACAmE,IAAA,CAAA3D,MAAA;QACAqE,YAAA,CAAAT,aAAA,CAAAE,UAAA;QACAO,YAAA,CAAAT,aAAA,CAAAG,gBAAA;QACA;QACAH,aAAA,CAAAU,IAAA;QACAX,IAAA,CAAArB,IAAA;MACA;IACA;IACA;IACAuC,IAAA,WAAAA,KAAA;MACA,SAAApF,EAAA;QACA,KAAAA,EAAA,CAAA0E,KAAA;QACA,KAAA1E,EAAA;MACA;IACA;IACA;IACAyE,IAAA,WAAAA,KAAA;MACA,SAAAzE,EAAA;QACA,KAAAA,EAAA,CAAAyE,IAAA,MAAA3E,OAAA;QACA,KAAAA,OAAA;QACA,KAAAuF,cAAA;MACA;QACAC,KAAA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAAzF,OAAA;MACA,SAAAE,EAAA;QACA,KAAAA,EAAA,CAAAyE,IAAA,CAAA3E,OAAA;MACA;QACAwF,KAAA;MACA;IACA;IACA;IACAJ,QAAA,WAAAA,SAAA;MAAA,IAAAM,MAAA;MACA,IAAAC,iBAAA,OAAAzD,MAAA,CAAAC,KAAA,CAAAF,SAAA,EAAAG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAoD,MAAA,CAAAtF,aAAA,GAAAiC,QAAA,CAAA7C,IAAA;QACA;UACAkG,MAAA,CAAAtF,aAAA;QACA;MACA;IACA;IACA;IACAmF,cAAA,WAAAA,eAAA;MAAA,IAAAK,MAAA;MACA,KAAAC,SAAA;QACA,IAAAC,SAAA,GAAAF,MAAA,CAAAvC,KAAA,CAAA0C,iBAAA;QACAD,SAAA,CAAAE,SAAA,GAAAF,SAAA,CAAAG,YAAA;MACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAAvF,YAAA;QACAwF,aAAA,MAAAxF,YAAA;MACA;MAEA,IAAAyF,KAAA;MACA,IAAAC,sBAAA,IAAAlE,IAAA,WAAAU,MAAA;QACA,IAAAA,MAAA,CAAAR,IAAA;UACA,IAAAiE,EAAA,OAAAC,IAAA,CAAA1D,MAAA,CAAAtD,IAAA;UACA2G,MAAA,CAAAvF,YAAA,GAAA6F,WAAA;YACAF,EAAA,CAAAG,UAAA,CAAAH,EAAA,CAAAI,UAAA;YACAN,KAAA,CAAA1F,WAAA,OAAA+C,uBAAA,EAAA6C,EAAA;YAEAF,KAAA,CAAAjD,YAAA;YACAiD,KAAA,CAAAjB,QAAA;UAEA;QACA;MACA;IACA;EACA;EACA;EACAwB,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAApF,IAAA;IACA,KAAA2D,QAAA;IAEA,KAAAc,UAAA;EACA;EACAY,OAAA,WAAAA,QAAA;IACA,KAAAvB,cAAA;EACA;EACAwB,aAAA,WAAAA,cAAA;IACA;IACA,SAAAnG,YAAA;MACAwF,aAAA,MAAAxF,YAAA;IACA;IACA;IACA,KAAA0E,IAAA;EACA;AACA", "ignoreList": []}]}