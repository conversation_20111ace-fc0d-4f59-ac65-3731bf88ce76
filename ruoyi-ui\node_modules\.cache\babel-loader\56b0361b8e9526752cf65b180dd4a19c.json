{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue", "mtime": 1753952497308}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\babel.config.js", "mtime": 1750995480034}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi95dW56aG9uZ2hlL3hleXhqeXB0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnJlZHVjZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZmluZC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5yZWR1Y2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucHJvbWlzZS5hbGwtc2V0dGxlZC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcudHJpbS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyIpOwp2YXIgX29iamVjdFNwcmVhZDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3l1bnpob25naGUveGV5eGp5cHQvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMi5qcyIpKTsKdmFyIF9zbGljZWRUb0FycmF5MiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDoveXVuemhvbmdoZS94ZXl4anlwdC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9zbGljZWRUb0FycmF5LmpzIikpOwp2YXIgX3JlZ2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDoveXVuemhvbmdoZS94ZXl4anlwdC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9yZWdlbmVyYXRvci5qcyIpKTsKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDoveXVuemhvbmdoZS94ZXl4anlwdC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yLmpzIikpOwp2YXIgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi95dW56aG9uZ2hlL3hleXhqeXB0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIuanMiKSk7CnZhciBfcmV2aWV3ID0gcmVxdWlyZSgiQC9hcGkvZXhwZXJ0L3JldmlldyIpOwp2YXIgX2RldGFpbCA9IHJlcXVpcmUoIkAvYXBpL2V2YWx1YXRpb24vZGV0YWlsLyIpOwp2YXIgX2V4cGVydFN0YXR1cyA9IHJlcXVpcmUoIkAvYXBpL2V2YWx1YXRpb24vZXhwZXJ0U3RhdHVzIik7CnZhciBfZW50SW5mbyA9IHJlcXVpcmUoIkAvYXBpL2RvY1Jlc3BvbnNlL2VudEluZm8iKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCi8vIOW4uOmHj+WumuS5iQp2YXIgUEFTUyA9ICcxJzsgLy8g6YCa6L+HCnZhciBGQUlMID0gJzAnOyAvLyDkuI3pgJrov4cKdmFyIENIRUNLX1BBU1MgPSAn57O757uf5Yid6aqM6YCa6L+HJzsgLy8g57O757uf5Yid6aqM6YCa6L+H5paH5pysCnZhciBDSEVDS19GQUlMID0gJ+ezu+e7n+WInemqjOacqumAmui/hyc7IC8vIOezu+e7n+WInemqjOacqumAmui/h+aWh+acrAoKLy8g5a+85YWl5LiT5a626K+E5a6h55u45YWzQVBJCi8vIOiOt+WPluivhOWIhuivpuaDhUFQSQovLyDnvJbovpHkuJPlrrbor4TliIbnirbmgIFBUEkKLy8g6I635Y+W5ZON5bqU5paH5Lu26K+E5a6h5Zug5a2Q5Yaz562WQVBJCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc3VwcGxpZXJPcHRpb25zOiBbXSwKICAgICAgLy8g5L6b5bqU5ZWG5LiL5ouJ6YCJ6aG55YiX6KGoCiAgICAgIHNjb3JpbmdNZXRob2Q6IG51bGwsCiAgICAgIC8vIOW9k+WJjeivhOWIhuaWueazleWvueixoQogICAgICBzZWxlY3RlZEZhY3Rvck5vZGU6IHt9LAogICAgICAvLyDlvZPliY3pgInkuK3nmoTor4TliIblm6DlrZDoioLngrkKICAgICAgc2VsZWN0ZWRTdXBwbGllck5hbWU6ICcnLAogICAgICAvLyDlvZPliY3pgInkuK3nmoTkvpvlupTllYblkI3np7AKICAgICAgc2VsZWN0ZWRTdXBwbGllcjoge30sCiAgICAgIC8vIOW9k+WJjemAieS4reeahOS+m+W6lOWVhuWvueixoQogICAgICBleHBlcnRJbmZvOiB7fSwKICAgICAgLy8g5b2T5YmN5LiT5a625L+h5oGvCiAgICAgIHJhdGluZ1N0YXRlTWFwOiB7fSwKICAgICAgLy8g6K+E5YiG6aG554q25oCB5pig5bCE77yIa2V55Li66K+E5YiG6aG5SUTvvIx2YWx1ZeS4untzdGF0ZSwgcmVhc29ufe+8iQogICAgICBwcm9qZWN0RmlsZXM6IHt9LAogICAgICAvLyDpobnnm67nm7jlhbPmlofku7blr7nosaEKICAgICAgaXNTaG93UmVzcG9uc2U6IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrlk43lupTmlofku7YKICAgICAgaXNTaG93UHJvY3VyZW1lbnQ6IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrph4fotK3mlofku7YKICAgICAgaXNEb3VibGVWaWV3OiBmYWxzZSwKICAgICAgLy8g5piv5ZCm5Y+M5paH5Lu25a+55q+U5qih5byPCiAgICAgIGZhY3RvckRldGFpbExpc3Q6IFtdLAogICAgICAvLyDor4TliIblm6DlrZDor6bnu4bliJfooagKICAgICAgZW50RG9jUmVzcG9uc2VQYWdlOiBudWxsLAogICAgICAvLyDkvIHkuJrlk43lupTmlofku7bpobXnoIHkv6Hmga8KICAgICAgZmFjdG9yc1BhZ2VNYXA6IG51bGwsCiAgICAgIC8vIOS+m+W6lOWVhuWboOWtkOmhteeggeaYoOWwhAogICAgICBzdXBwbGllckZhY3RvclBhZ2U6IG51bGwsCiAgICAgIC8vIOW9k+WJjeS+m+W6lOWVhuWboOWtkOmhteeggQogICAgICByZXNwb25zZVBkZlVybDogbnVsbCwKICAgICAgLy8g5ZON5bqU5paH5Lu2UERG5Zyw5Z2ACiAgICAgIHByb2N1cmVtZW50UGRmVXJsOiBudWxsLAogICAgICAvLyDph4fotK3mlofku7ZQREblnLDlnYAKCiAgICAgIC8vIOaMiemSrueKtuaAgeeuoeeQhgogICAgICBhY3RpdmVCdXR0b246ICdyZXNwb25zZScsCiAgICAgIC8vIOW9k+WJjea/gOa0u+eahOaMiemSru+8midyZXNwb25zZSfjgIEncHJvY3VyZW1lbnQn44CBJ2NvbnRyYXN0JwoKICAgICAgZW50RG9jUHJvY3VyZW1lbnRQYWdlOiB7fSwKICAgICAgLy8g6YeH6LSt5paH5Lu26aG156CB5L+h5oGvCiAgICAgIHBhZ2VQcm9jdXJlbWVudDogW10sCiAgICAgIC8vIOmHh+i0reaWh+S7tueahOivhOWIhumhuQogICAgICBhdHRhY2htZW50c0xpc3Q6IFtdLAogICAgICAvLyDmlofku7bliJfooagKCiAgICAgIC8vIFBERua4suafk+eKtuaAgeeuoeeQhgogICAgICByZXNwb25zZVBkZlJlbmRlcmVkOiBmYWxzZSwKICAgICAgLy8g5ZON5bqU5paH5Lu2UERG5piv5ZCm5riy5p+T5a6M5oiQCiAgICAgIHByb2N1cmVtZW50UGRmUmVuZGVyZWQ6IGZhbHNlLAogICAgICAvLyDph4fotK3mlofku7ZQREbmmK/lkKbmuLLmn5PlrozmiJAKCiAgICAgIGhlbHBJbWdMaXN0OiBbIi9ldmFsdXRpb24vaGVscC5qcGciXSwKICAgICAgLy8g5pON5L2c5biu5Yqp5Zu+54mH5YiX6KGoCgogICAgICAvLyBQREbmuLLmn5PnirbmgIHmjqfliLYKICAgICAgaXNQZGZSZW5kZXJpbmc6IGZhbHNlLAogICAgICAvLyBQREbmmK/lkKbmraPlnKjmuLLmn5MKICAgICAgLy8g6K+E5YiG6aG55ZCN56ew5LiO5ZCO56uv5a2X5q615pig5bCECiAgICAgIGZhY3RvckNvZGVNYXA6IHsKICAgICAgICAi54m55a6a6LWE5qC86KaB5rGCIjogInpnenMiLAogICAgICAgICLlk43lupTlhoXlrrkiOiAianNwbGIiLAogICAgICAgICLph4fotK3pnIDmsYIiOiAianNwbGIiLAogICAgICAgICLkvpvotKfmnJ/pmZAiOiAiZ2hxeCIsCiAgICAgICAgIuaKleagh+aKpeS7tyI6ICJ0YmJqIgogICAgICB9LAogICAgICBjaGVja1Jlc3VsdDoge30sCiAgICAgIC8vIOezu+e7n+WInemqjOe7k+aenOWvueixoQogICAgICAvLyDns7vnu5/liJ3pqoznu5PmnpzlkI3np7DmmKDlsIQKICAgICAgY2hlY2tSZXN1bHROYW1lTWFwOiB7CiAgICAgICAgIuespuWQiOOAiuS4reWNjuS6uuawkeWFseWSjOWbveaUv+W6nOmHh+i0reazleOAi+esrOS6jOWNgeS6jOadoeinhOWumiI6IENIRUNLX1BBU1MsCiAgICAgICAgIueJueWumui1hOagvOimgeaxgiI6IENIRUNLX1BBU1MsCiAgICAgICAgIuS/oeeUqOafpeivoiI6IENIRUNLX1BBU1MsCiAgICAgICAgIuWTjeW6lOS6uuWQjeensCI6IENIRUNLX1BBU1MsCiAgICAgICAgIuWTjeW6lOWGheWuuSI6IENIRUNLX1BBU1MsCiAgICAgICAgIumHh+i0remcgOaxgiI6IENIRUNLX1BBU1MsCiAgICAgICAgIuS+m+i0p+acn+mZkCI6IENIRUNLX1BBU1MsCiAgICAgICAgIuaKleagh+aKpeS7tyI6IENIRUNLX1BBU1MKICAgICAgfSwKICAgICAgLy8g5pys5Zyw57yT5a2Y5pWw5o2uCiAgICAgIGxvY2FsRXhwZXJ0SW5mbzogbnVsbCwKICAgICAgLy8g5pys5Zyw5LiT5a625L+h5oGvCiAgICAgIGxvY2FsRW50RG9jUmVzcG9uc2VQYWdlOiBudWxsLAogICAgICAvLyDmnKzlnLDlk43lupTmlofku7bpobXnoIEKICAgICAgbG9jYWxGYWN0b3JzUGFnZU1hcDogbnVsbCwKICAgICAgLy8g5pys5Zyw5Zug5a2Q6aG156CB5pig5bCECgogICAgICAvLyDmgqzlgZznirbmgIHnrqHnkIYKICAgICAgaG92ZXJlZEZhY3Rvck5vZGU6IG51bGwsCiAgICAgIC8vIOaCrOWBnOaXtueahOivhOWIhumhuQogICAgICB0b29sdGlwVGltZXI6IG51bGwgLy8g5oKs5rWu5qGG5pi+56S65a6a5pe25ZmoCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgLyoqDQogICAgICog5qCh6aqM5omA5pyJ6K+E5YiG6aG55piv5ZCm5aGr5YaZ5a6M5pW0DQogICAgICogQHJldHVybnMge2Jvb2xlYW59IOaYr+WQpuWFqOmDqOWhq+WGmQ0KICAgICAqLwogICAgdmFsaWRhdGVSYXRpbmdzOiBmdW5jdGlvbiB2YWxpZGF0ZVJhdGluZ3MoKSB7CiAgICAgIHZhciBfaXRlcmF0b3IgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKHRoaXMuc2NvcmluZ01ldGhvZC51aXRlbXMpLAogICAgICAgIF9zdGVwOwogICAgICB0cnkgewogICAgICAgIGZvciAoX2l0ZXJhdG9yLnMoKTsgIShfc3RlcCA9IF9pdGVyYXRvci5uKCkpLmRvbmU7KSB7CiAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwLnZhbHVlOwogICAgICAgICAgLy8g6YGN5Y6G5omA5pyJ6K+E5YiG6aG5CiAgICAgICAgICB2YXIgc3RhdGUgPSB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5zdGF0ZTsgLy8g6I635Y+W6K+E5YiG54q25oCBCiAgICAgICAgICB2YXIgcmVhc29uID0gdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtLmVudE1ldGhvZEl0ZW1JZF0ucmVhc29uOyAvLyDojrflj5bor4TliIbljp/lm6AKICAgICAgICAgIC8vIOivhOWIhue7k+aenOacquWhq+WGmQogICAgICAgICAgaWYgKHN0YXRlID09PSBudWxsIHx8IHN0YXRlID09PSAnJykgewogICAgICAgICAgICAvLyB0aGlzLiRtZXNzYWdlLndhcm5pbmcoYOivt+Whq+WGmeivhOWIhumhue+8miR7aXRlbS5pdGVtTmFtZX0g55qE6K+E5YiG57uT5p6cYCk7IC8vIOaPkOekuuacquWhq+WGmQogICAgICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgICAgIH0KICAgICAgICAgIC8vIOS4jemAmui/h+S9huacquWhq+WGmeWOn+WboCAtIOWwhuivhOWuoemhueiuvue9ruS4uuepuu+8jOeEtuWQjue7p+e7reaJp+ihjOWQjue7rea1geeoiwogICAgICAgICAgaWYgKHN0YXRlID09PSBGQUlMICYmICghcmVhc29uIHx8IHJlYXNvbi50cmltKCkgPT09ICcnKSkgewogICAgICAgICAgICAvLyDlsIbmraTor4TlrqHpobnorr7nva7kuLrnqbrvvIjmnKror4TlrqHnirbmgIHvvIkKICAgICAgICAgICAgdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtLmVudE1ldGhvZEl0ZW1JZF0uc3RhdGUgPSBudWxsOwogICAgICAgICAgICB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5yZWFzb24gPSAiIjsKICAgICAgICAgICAgY29uc29sZS5sb2coIiIuY29uY2F0KGl0ZW0uaXRlbU5hbWUsICJcdThCQzRcdTVCQTFcdTRFMERcdTkwMUFcdThGQzdcdTRGNDZcdTY3MkFcdTU4NkJcdTUxOTlcdTU5MDdcdTZDRThcdUZGMENcdTVERjJcdTVDMDZcdThCRTVcdThCQzRcdTVCQTFcdTk4NzlcdThCQkVcdTdGNkVcdTRFM0FcdTdBN0EiKSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3IuZShlcnIpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIF9pdGVyYXRvci5mKCk7CiAgICAgIH0KICAgICAgcmV0dXJuIHRydWU7IC8vIOWFqOmDqOWhq+WGmei/lOWbnnRydWUKICAgIH0sCiAgICAvKioNCiAgICAgKiDojrflj5bns7vnu5/liJ3pqoznu5PmnpzvvIjpgJrov4cv5pyq6YCa6L+H77yJDQogICAgICogQHBhcmFtIHtzdHJpbmd9IGZhY3Rvck5hbWUg6K+E5YiG6aG55ZCN56ewDQogICAgICogQHJldHVybnMge3N0cmluZ30gMS3pgJrov4cgMC3mnKrpgJrov4cNCiAgICAgKi8KICAgIGdldENoZWNrUmVzdWx0U3RhdGU6IGZ1bmN0aW9uIGdldENoZWNrUmVzdWx0U3RhdGUoZmFjdG9yTmFtZSkgewogICAgICBpZiAoIXRoaXMuY2hlY2tSZXN1bHQgfHwgT2JqZWN0LmtleXModGhpcy5jaGVja1Jlc3VsdCkubGVuZ3RoID09PSAwKSByZXR1cm4gJyc7IC8vIOayoeacieWInemqjOe7k+aenOebtOaOpei/lOWbnuepugogICAgICB2YXIgY29kZSA9IHRoaXMuZmFjdG9yQ29kZU1hcFtmYWN0b3JOYW1lXTsgLy8g6I635Y+W6K+E5YiG6aG55a+55bqU55qE5ZCO56uv5a2X5q61CiAgICAgIHZhciBjaGVjayA9IFBBU1M7IC8vIOm7mOiupOmAmui/hwogICAgICBpZiAoY29kZSkgewogICAgICAgIGNoZWNrID0gdGhpcy5jaGVja1Jlc3VsdFtjb2RlXTsgLy8g6I635Y+W5Yid6aqM57uT5p6cCiAgICAgICAgLy8g5oqV5qCH5oql5Lu354m55q6K5aSE55CGCiAgICAgICAgaWYgKGZhY3Rvck5hbWUgPT09ICLmipXmoIfmiqXku7ciICYmIGNoZWNrID09PSBQQVNTKSB7CiAgICAgICAgICBjaGVjayA9IHRoaXMuY2hlY2tSZXN1bHRbJ214YmpiJ107IC8vIOaYjue7huaKpeS7t+ihqAogICAgICAgIH0KICAgICAgfQogICAgICAvLyDorr7nva7liJ3pqoznu5PmnpzlkI3np7AKICAgICAgaWYgKGNoZWNrID09PSBGQUlMKSB7CiAgICAgICAgdGhpcy5jaGVja1Jlc3VsdE5hbWVNYXBbZmFjdG9yTmFtZV0gPSBDSEVDS19GQUlMOyAvLyDmnKrpgJrov4cKICAgICAgfSBlbHNlIHsKICAgICAgICBjaGVjayA9IFBBU1M7CiAgICAgICAgdGhpcy5jaGVja1Jlc3VsdE5hbWVNYXBbZmFjdG9yTmFtZV0gPSBDSEVDS19QQVNTOyAvLyDpgJrov4cKICAgICAgfQogICAgICByZXR1cm4gY2hlY2s7IC8vIOi/lOWbnuWInemqjOe7k+aenAogICAgfSwKICAgIC8qKg0KICAgICAqIOmHjee9ruaJgOacieivhOWIhumhueeahOeKtuaAgQ0KICAgICAqLwogICAgcmVzZXRSYXRpbmdTdGF0ZU1hcDogZnVuY3Rpb24gcmVzZXRSYXRpbmdTdGF0ZU1hcCgpIHsKICAgICAgaWYgKCF0aGlzLnNjb3JpbmdNZXRob2QpIHJldHVybjsgLy8g5rKh5pyJ6K+E5YiG5pa55rOV55u05o6l6L+U5ZueCiAgICAgIGZvciAodmFyIF9pID0gMCwgX09iamVjdCRrZXlzID0gT2JqZWN0LmtleXModGhpcy5yYXRpbmdTdGF0ZU1hcCk7IF9pIDwgX09iamVjdCRrZXlzLmxlbmd0aDsgX2krKykgewogICAgICAgIHZhciBrZXkgPSBfT2JqZWN0JGtleXNbX2ldOwogICAgICAgIC8vIOmBjeWOhuaJgOacieivhOWIhumhuQogICAgICAgIHRoaXMucmF0aW5nU3RhdGVNYXBba2V5XS5zdGF0ZSA9IG51bGw7IC8vIOmHjee9rueKtuaAgQogICAgICAgIHRoaXMucmF0aW5nU3RhdGVNYXBba2V5XS5yZWFzb24gPSAnJzsgLy8g6YeN572u5Y6f5ZugCiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKiDkuLTml7bkv53lrZjor4TliIbnu5PmnpzliLDlkI7nq68NCiAgICAgKiDmoKHpqozpgJrov4flkI7miY3kvJrkv53lrZgNCiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5L+d5a2Y5piv5ZCm5oiQ5YqfDQogICAgICovCiAgICBzYXZlVGVtcFJhdGluZzogZnVuY3Rpb24gc2F2ZVRlbXBSYXRpbmcoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLm0oZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICB2YXIgZGF0YSwgcmVzcG9uc2UsIF90OwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoX3RoaXMudmFsaWRhdGVSYXRpbmdzKCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5hKDIsIGZhbHNlKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIC8vIOagoemqjOS4jemAmui/h+S4jeS/neWtmO+8jOi/lOWbnmZhbHNlCiAgICAgICAgICAgICAgLy8g5p6E6YCg5o+Q5Lqk5pWw5o2uCiAgICAgICAgICAgICAgZGF0YSA9IF90aGlzLnNjb3JpbmdNZXRob2QudWl0ZW1zLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgdmFyIGl0ZW1JZCA9IGl0ZW0uZW50TWV0aG9kSXRlbUlkOyAvLyDojrflj5bor4TliIbpoblJRAogICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgc2NvcmluZ01ldGhvZFVpdGVtSWQ6IGl0ZW1JZCwKICAgICAgICAgICAgICAgICAgLy8g6K+E5YiG6aG5SUQKICAgICAgICAgICAgICAgICAgZXhwZXJ0UmVzdWx0SWQ6IF90aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsCiAgICAgICAgICAgICAgICAgIC8vIOS4k+Wutue7k+aenElECiAgICAgICAgICAgICAgICAgIGVudElkOiBfdGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkLAogICAgICAgICAgICAgICAgICAvLyDkvpvlupTllYZJRAogICAgICAgICAgICAgICAgICBldmFsdWF0aW9uUmVzdWx0OiBfdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtSWRdLnN0YXRlLAogICAgICAgICAgICAgICAgICAvLyDor4TliIbnu5PmnpwKICAgICAgICAgICAgICAgICAgZXZhbHVhdGlvblJlbWFyazogX3RoaXMucmF0aW5nU3RhdGVNYXBbaXRlbUlkXS5yZWFzb24gfHwgJycgLy8g6K+E5YiG5Y6f5ZugCiAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIH0pLmZpbHRlcihmdW5jdGlvbiAoZCkgewogICAgICAgICAgICAgICAgcmV0dXJuIGQuZXZhbHVhdGlvblJlc3VsdCAhPT0gbnVsbCAmJiBkLmV2YWx1YXRpb25SZXN1bHQgIT09ICcnOwogICAgICAgICAgICAgIH0pOyAvLyDov4fmu6TmnKrloavlhpnnmoTpobkKICAgICAgICAgICAgICBpZiAoIShkYXRhLmxlbmd0aCA+IDApKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dC5uID0gNzsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dC5wID0gMjsKICAgICAgICAgICAgICBfY29udGV4dC5uID0gMzsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9yZXZpZXcuc2NvcmluZ0ZhY3RvcnMpKGRhdGEpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dC52OwogICAgICAgICAgICAgIGlmICghKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSA0OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/neWtmOaIkOWKnyIpOyAvLyDkv53lrZjmiJDlip/mj5DnpLoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuYSgyLCB0cnVlKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLndhcm5pbmcocmVzcG9uc2UubXNnKTsgLy8g5L+d5a2Y5aSx6LSl5o+Q56S6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmEoMiwgZmFsc2UpOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDc7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICBfY29udGV4dC5wID0gNjsKICAgICAgICAgICAgICBfdCA9IF9jb250ZXh0LnY7CiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IoIuS/neWtmOWksei0pSIpOyAvLyDlvILluLjmj5DnpLoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuYSgyLCBmYWxzZSk7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuYSgyLCB0cnVlKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzIsIDZdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKg0KICAgICAqIOS+m+W6lOWVhuWIh+aNouS6i+S7tu+8jOWIh+aNouaXtuiHquWKqOS/neWtmOS4iuS4gOS4quS+m+W6lOWVhuivhOWIhu+8jOW5tuW5tuWPkeiOt+WPluaWsOS+m+W6lOWVhueahOivhOWIhuivpuaDheWSjOezu+e7n+WInemqjA0KICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzdXBwbGllck5hbWUg5L6b5bqU5ZWG5ZCN56ewDQogICAgICovCiAgICBoYW5kbGVTdXBwbGllckNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU3VwcGxpZXJDaGFuZ2Uoc3VwcGxpZXJOYW1lKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHZhciBfeWllbGQkUHJvbWlzZSRhbGxTZXQsIF95aWVsZCRQcm9taXNlJGFsbFNldDIsIGRldGFpbFJlc3VsdCwgY2hlY2tSZXN1bHQsIGRldGFpbFJlcywgX3RoaXMyJGZhY3RvckRldGFpbExpLCBmYWN0b3IsIF9pdGVyYXRvcjIsIF9zdGVwMiwgaXRlbSwgY2hlY2tSZXMsIF90MjsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS53KGZ1bmN0aW9uIChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoIShPYmplY3Qua2V5cyhfdGhpczIuc2VsZWN0ZWRTdXBwbGllcikubGVuZ3RoICE9PSAwKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQyLm4gPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0Mi5uID0gMTsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMyLnNhdmVUZW1wUmF0aW5nKCk7CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgICAvLyDmn6Xmib7lvZPliY3pgInkuK3nmoTkvpvlupTllYblr7nosaEKICAgICAgICAgICAgICBfdGhpczIuc2VsZWN0ZWRTdXBwbGllciA9IF90aGlzMi5zdXBwbGllck9wdGlvbnMuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0uYmlkZGVyTmFtZSA9PT0gc3VwcGxpZXJOYW1lOwogICAgICAgICAgICAgIH0pOyAvLyDmn6Xmib7kvpvlupTllYYKICAgICAgICAgICAgICAvLyDojrflj5blvZPliY3kvpvlupTllYblm6DlrZDpobXnoIEKICAgICAgICAgICAgICBfdGhpczIuc3VwcGxpZXJGYWN0b3JQYWdlID0gX3RoaXMyLmZhY3RvcnNQYWdlTWFwW190aGlzMi5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkXTsgLy8g6I635Y+W6aG156CBCiAgICAgICAgICAgICAgLy8g5bm25Y+R6I635Y+W6K+E5YiG6K+m5oOF5ZKM57O757uf5Yid6aqMCiAgICAgICAgICAgICAgLy8g5L2/55SoIFByb21pc2UuYWxsU2V0dGxlZCDorqnkuKTkuKror7fmsYLni6znq4vmiafooYzvvIzkupLkuI3lvbHlk40KICAgICAgICAgICAgICBfY29udGV4dDIucCA9IDI7CiAgICAgICAgICAgICAgX2NvbnRleHQyLm4gPSAzOwogICAgICAgICAgICAgIHJldHVybiBQcm9taXNlLmFsbFNldHRsZWQoWygwLCBfZGV0YWlsLmdldERldGFpbEJ5UHN4eCkoewogICAgICAgICAgICAgICAgZXhwZXJ0UmVzdWx0SWQ6IF90aGlzMi5leHBlcnRJbmZvLnJlc3VsdElkLAogICAgICAgICAgICAgICAgLy8g5LiT5a6257uT5p6cSUQKICAgICAgICAgICAgICAgIHByb2plY3RJZDogX3RoaXMyLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsCiAgICAgICAgICAgICAgICAvLyDpobnnm65JRAogICAgICAgICAgICAgICAgc2NvcmluZ01ldGhvZEl0ZW1JZDogX3RoaXMyLiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkIC8vIOivhOWIhuaWueazlemhuUlECiAgICAgICAgICAgICAgfSksICgwLCBfZW50SW5mby5yZXNEb2NSZXZpZXdGYWN0b3JzRGVjaXNpb24pKHsKICAgICAgICAgICAgICAgIHByb2plY3RJZDogX3RoaXMyLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsCiAgICAgICAgICAgICAgICAvLyDpobnnm65JRAogICAgICAgICAgICAgICAgZW50SWQ6IF90aGlzMi5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkIC8vIOS+m+W6lOWVhklECiAgICAgICAgICAgICAgfSldKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIF95aWVsZCRQcm9taXNlJGFsbFNldCA9IF9jb250ZXh0Mi52OwogICAgICAgICAgICAgIF95aWVsZCRQcm9taXNlJGFsbFNldDIgPSAoMCwgX3NsaWNlZFRvQXJyYXkyLmRlZmF1bHQpKF95aWVsZCRQcm9taXNlJGFsbFNldCwgMik7CiAgICAgICAgICAgICAgZGV0YWlsUmVzdWx0ID0gX3lpZWxkJFByb21pc2UkYWxsU2V0MlswXTsKICAgICAgICAgICAgICBjaGVja1Jlc3VsdCA9IF95aWVsZCRQcm9taXNlJGFsbFNldDJbMV07CiAgICAgICAgICAgICAgLy8g5aSE55CG6K+E5YiG6K+m5oOF6K+35rGC57uT5p6cCiAgICAgICAgICAgICAgaWYgKGRldGFpbFJlc3VsdC5zdGF0dXMgPT09ICdmdWxmaWxsZWQnKSB7CiAgICAgICAgICAgICAgICBkZXRhaWxSZXMgPSBkZXRhaWxSZXN1bHQudmFsdWU7CiAgICAgICAgICAgICAgICBpZiAoZGV0YWlsUmVzLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICAgICAgICBfdGhpczIuZmFjdG9yRGV0YWlsTGlzdCA9IGRldGFpbFJlcy5kYXRhOyAvLyDor4TliIbor6bmg4XliJfooagKICAgICAgICAgICAgICAgICAgZmFjdG9yID0gKF90aGlzMiRmYWN0b3JEZXRhaWxMaSA9IF90aGlzMi5mYWN0b3JEZXRhaWxMaXN0LmZpbmQoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gaXRlbS5iaWRkZXJOYW1lID09PSBzdXBwbGllck5hbWU7CiAgICAgICAgICAgICAgICAgIH0pKSA9PT0gbnVsbCB8fCBfdGhpczIkZmFjdG9yRGV0YWlsTGkgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzMiRmYWN0b3JEZXRhaWxMaS5ldmFsRXhwZXJ0RXZhbHVhdGlvbkRldGFpbHM7IC8vIOW9k+WJjeS+m+W6lOWVhuivhOWIhuivpuaDhQogICAgICAgICAgICAgICAgICBfdGhpczIucmVzZXRSYXRpbmdTdGF0ZU1hcCgpOyAvLyDph43nva7or4TliIbnirbmgIEKICAgICAgICAgICAgICAgICAgaWYgKGZhY3RvcikgewogICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKGZhY3Rvcik7CiAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yMi5zKCk7ICEoX3N0ZXAyID0gX2l0ZXJhdG9yMi5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0gPSBfc3RlcDIudmFsdWU7CiAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzMi5yYXRpbmdTdGF0ZU1hcFtpdGVtLnNjb3JpbmdNZXRob2RVaXRlbUlkXS5yZWFzb24gPSBpdGVtLmV2YWx1YXRpb25SZW1hcms7IC8vIOiuvue9ruivhOWIhuWOn+WboAogICAgICAgICAgICAgICAgICAgICAgICBfdGhpczIucmF0aW5nU3RhdGVNYXBbaXRlbS5zY29yaW5nTWV0aG9kVWl0ZW1JZF0uc3RhdGUgPSBpdGVtLmV2YWx1YXRpb25SZXN1bHQ7IC8vIOiuvue9ruivhOWIhue7k+aenAogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMi5lKGVycik7CiAgICAgICAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIuZigpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLndhcm5pbmcoZGV0YWlsUmVzLm1zZyk7IC8vIOivhOWIhuivpuaDheiOt+WPluWksei0pQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bor4TliIbor6bmg4XlpLHotKU6IiwgZGV0YWlsUmVzdWx0LnJlYXNvbik7CiAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluivhOWIhuivpuaDheWksei0pSIpOyAvLyDor4TliIbor6bmg4Xor7fmsYLlvILluLgKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIC8vIOWkhOeQhuezu+e7n+WInemqjOivt+axgue7k+aenAogICAgICAgICAgICAgIGlmIChjaGVja1Jlc3VsdC5zdGF0dXMgPT09ICdmdWxmaWxsZWQnKSB7CiAgICAgICAgICAgICAgICBjaGVja1JlcyA9IGNoZWNrUmVzdWx0LnZhbHVlOwogICAgICAgICAgICAgICAgaWYgKGNoZWNrUmVzLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICAgICAgICBfdGhpczIuY2hlY2tSZXN1bHQgPSBjaGVja1Jlcy5kYXRhOyAvLyDorr7nva7liJ3pqoznu5PmnpwKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluezu+e7n+WInemqjOe7k+aenOWksei0pToiLCBjaGVja1Jlcy5tc2cpOwogICAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2Uud2FybmluZygi6I635Y+W57O757uf5Yid6aqM57uT5p6c5aSx6LSlIik7IC8vIOezu+e7n+WInemqjOiOt+WPluWksei0pQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLns7vnu5/liJ3pqozor7fmsYLlpLHotKU6IiwgY2hlY2tSZXN1bHQucmVhc29uKTsKICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcigi57O757uf5Yid6aqM6K+35rGC5aSx6LSlIik7IC8vIOezu+e7n+WInemqjOivt+axguW8guW4uAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDIubiA9IDU7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBfY29udGV4dDIucCA9IDQ7CiAgICAgICAgICAgICAgX3QyID0gX2NvbnRleHQyLnY7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi6K+35rGC5aSE55CG5byC5bi4OiIsIF90Mik7CiAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLmVycm9yKCLojrflj5bkvpvlupTllYbor6bmg4XlpLHotKUiKTsgLy8g5byC5bi45o+Q56S6CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICAvLyDpu5jorqTmmL7npLrlk43lupTmlofku7YKICAgICAgICAgICAgICBfdGhpczIuc2hvd1Jlc3BvbnNlRmlsZSgpOwogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5hKDIpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUyLCBudWxsLCBbWzIsIDRdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKg0KICAgICAqIOaYvuekuuWTjeW6lOaWh+S7tlBERg0KICAgICAqLwogICAgc2hvd1Jlc3BvbnNlRmlsZTogZnVuY3Rpb24gc2hvd1Jlc3BvbnNlRmlsZSgpIHsKICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkU3VwcGxpZXIgfHwgT2JqZWN0LmtleXModGhpcy5zZWxlY3RlZFN1cHBsaWVyKS5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeS+m+W6lOWVhiIpOyAvLyDmnKrpgInkvpvlupTllYbmj5DnpLoKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5hY3RpdmVCdXR0b24gPSAncmVzcG9uc2UnOyAvLyDorr7nva7lvZPliY3mv4DmtLvmjInpkq4KICAgICAgdGhpcy5pc0RvdWJsZVZpZXcgPSBmYWxzZTsgLy8g5Y2V5paH5Lu25qih5byPCiAgICAgIHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgPSBmYWxzZTsgLy8g5LiN5pi+56S66YeH6LSt5paH5Lu2CiAgICAgIHRoaXMuaXNTaG93UmVzcG9uc2UgPSB0cnVlOyAvLyDmmL7npLrlk43lupTmlofku7YKICAgICAgdGhpcy5yZXNwb25zZVBkZlVybCA9IHRoaXMucHJvamVjdEZpbGVzLmZpbGVbdGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkXTsgLy8g6K6+572u5ZON5bqU5paH5Lu2UERG5Zyw5Z2ACiAgICAgIHRoaXMuaXNQZGZSZW5kZXJpbmcgPSB0cnVlOyAvLyDlvIDlp4vmuLLmn5NQREYKICAgIH0sCiAgICAvKioNCiAgICAgKiDmlofku7blr7nmr5TvvIjlj4zmlofku7bmqKHlvI/vvIkNCiAgICAgKi8KICAgIHNob3dGaWxlQ29udHJhc3Q6IGZ1bmN0aW9uIHNob3dGaWxlQ29udHJhc3QoKSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZFN1cHBsaWVyIHx8IE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0ZWRTdXBwbGllcikubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nkvpvlupTllYYiKTsgLy8g5pyq6YCJ5L6b5bqU5ZWG5o+Q56S6CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuYWN0aXZlQnV0dG9uID0gJ2NvbnRyYXN0JzsgLy8g6K6+572u5b2T5YmN5r+A5rS75oyJ6ZKuCiAgICAgIHRoaXMuaXNEb3VibGVWaWV3ID0gdHJ1ZTsgLy8g5Y+M5paH5Lu25qih5byPCiAgICAgIHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgPSB0cnVlOyAvLyDmmL7npLrph4fotK3mlofku7YKICAgICAgdGhpcy5pc1Nob3dSZXNwb25zZSA9IHRydWU7IC8vIOaYvuekuuWTjeW6lOaWh+S7tgogICAgICB0aGlzLnJlc3BvbnNlUGRmVXJsID0gdGhpcy5wcm9qZWN0RmlsZXMuZmlsZVt0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWRdOyAvLyDorr7nva7lk43lupTmlofku7ZQREblnLDlnYAKICAgICAgdGhpcy5pc1BkZlJlbmRlcmluZyA9IHRydWU7IC8vIOW8gOWni+a4suafk1BERgogICAgfSwKICAgIC8qKg0KICAgICAqIOeCueWHu+ivhOWIhumhueWQjeensO+8jOi3s+i9rOWIsOWvueW6lFBERumhteeggQ0KICAgICAqIEBwYXJhbSB7T2JqZWN0fSBmYWN0b3JJdGVtIOW9k+WJjeivhOWIhuWboOWtkOmhuQ0KICAgICAqLwogICAgaGFuZGxlU2hvd0ZhY3RvckluZm86IGZ1bmN0aW9uIGhhbmRsZVNob3dGYWN0b3JJbmZvKGZhY3Rvckl0ZW0pIHsKICAgICAgLy8g5qOA5p+lUERG5piv5ZCm5riy5p+T5a6M5oiQCiAgICAgIGlmICghdGhpcy5jYW5KdW1wVG9QYWdlKCkpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIlBERumhtemdouato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeivlSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLnNlbGVjdGVkRmFjdG9yTm9kZSA9IGZhY3Rvckl0ZW07IC8vIOiuvue9ruW9k+WJjemAieS4reWboOWtkAoKICAgICAgLy8g5aaC5p6c5Y+q5pi+56S66YeH6LSt5paH5Lu277yM5L2/55So6YeH6LSt5paH5Lu26aG156CB5L+h5oGvCiAgICAgIGlmICh0aGlzLmlzU2hvd1Byb2N1cmVtZW50ICYmICF0aGlzLmlzU2hvd1Jlc3BvbnNlKSB7CiAgICAgICAgaWYgKCF0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6YeH6LSt5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIGlmIChmYWN0b3JJdGVtLmp1bXBUb1BhZ2UpIHsKICAgICAgICAgIHRoaXMuJHJlZnMucHJvY3VyZW1lbnQuc2tpcFBhZ2UoZmFjdG9ySXRlbS5qdW1wVG9QYWdlKTsgLy8g6YeH6LSt5paH5Lu26Lez6aG1CiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZSAmJiB0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtmYWN0b3JJdGVtLml0ZW1OYW1lXSkgewogICAgICAgICAgdGhpcy4kcmVmcy5wcm9jdXJlbWVudC5za2lwUGFnZSh0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtmYWN0b3JJdGVtLml0ZW1OYW1lXSk7IC8vIOmHh+i0reaWh+S7tui3s+mhtQogICAgICAgIH0KICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOWmguaenOaYvuekuuWTjeW6lOaWh+S7tuaIluWvueavlOaooeW8j++8jOmcgOimgemAieaLqeS+m+W6lOWVhgogICAgICBpZiAoIXRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlIHx8IE9iamVjdC5rZXlzKHRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlKS5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieaLqeS+m+W6lOWVhiIpOyAvLyDmnKrpgInkvpvlupTllYbmj5DnpLoKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOi3s+i9rOWIsOWTjeW6lOaWh+S7tuWvueW6lOmhteeggQogICAgICBpZiAodGhpcy5pc1Nob3dSZXNwb25zZSAmJiB0aGlzLiRyZWZzLnJlc3BvbnNlKSB7CiAgICAgICAgaWYgKCF0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5ZON5bqU5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIHRoaXMuJHJlZnMucmVzcG9uc2Uuc2tpcFBhZ2UodGhpcy5zdXBwbGllckZhY3RvclBhZ2VbdGhpcy5zZWxlY3RlZEZhY3Rvck5vZGUuaXRlbU5hbWVdKTsgLy8g5ZON5bqU5paH5Lu26Lez6aG1CiAgICAgIH0KCiAgICAgIC8vIOi3s+i9rOWIsOmHh+i0reaWh+S7tuWvueW6lOmhteeggQogICAgICBpZiAodGhpcy5pc1Nob3dQcm9jdXJlbWVudCAmJiB0aGlzLiRyZWZzLnByb2N1cmVtZW50KSB7CiAgICAgICAgaWYgKCF0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6YeH6LSt5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQoKICAgICAgICAvLyDlnKjlr7nmr5TmqKHlvI/kuIvvvIzph4fotK3mlofku7blupTor6Xot7PovazliLDph4fotK3mlofku7bnmoTlr7nlupTpobXnoIHvvIzogIzkuI3mmK/kvpvlupTllYbnmoTpobXnoIEKICAgICAgICBpZiAodGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2UgJiYgdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbZmFjdG9ySXRlbS5pdGVtTmFtZV0pIHsKICAgICAgICAgIHRoaXMuJHJlZnMucHJvY3VyZW1lbnQuc2tpcFBhZ2UodGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbZmFjdG9ySXRlbS5pdGVtTmFtZV0pOyAvLyDph4fotK3mlofku7bot7PpobUKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5Zyo5a+55q+U5qih5byP5LiL77yM5aaC5p6c5rKh5pyJ6YeH6LSt5paH5Lu26aG156CB5L+h5oGv77yM5YiZ5Y+q6Lez6L2s5ZON5bqU5paH5Lu255qE6aG156CB77yM5LiN6Lez6L2s6YeH6LSt5paH5Lu2CiAgICAgICAgICAvLyDov5nmoLflj6/ku6Xpgb/lhY3ph4fotK3mlofku7blkozlk43lupTmlofku7bmmL7npLrkuI3lkIznmoTlhoXlrrnpgKDmiJDmt7fmt4YKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKiDmo4Dmn6XmmK/lkKblj6/ku6Xot7PovazpobXpnaINCiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5piv5ZCm5Y+v5Lul6Lez6L2sDQogICAgICovCiAgICBjYW5KdW1wVG9QYWdlOiBmdW5jdGlvbiBjYW5KdW1wVG9QYWdlKCkgewogICAgICAvLyDlpoLmnpzlj6rmmL7npLrph4fotK3mlofku7YKICAgICAgaWYgKHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgJiYgIXRoaXMuaXNTaG93UmVzcG9uc2UpIHsKICAgICAgICByZXR1cm4gdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkOwogICAgICB9CiAgICAgIC8vIOWmguaenOWPquaYvuekuuWTjeW6lOaWh+S7tgogICAgICBpZiAodGhpcy5pc1Nob3dSZXNwb25zZSAmJiAhdGhpcy5pc1Nob3dQcm9jdXJlbWVudCkgewogICAgICAgIHJldHVybiB0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQ7CiAgICAgIH0KICAgICAgLy8g5aaC5p6c5a+55q+U5qih5byP77yI5Lik5Liq6YO95pi+56S677yJCiAgICAgIGlmICh0aGlzLmlzU2hvd1Jlc3BvbnNlICYmIHRoaXMuaXNTaG93UHJvY3VyZW1lbnQpIHsKICAgICAgICByZXR1cm4gdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkICYmIHRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZDsKICAgICAgfQogICAgICByZXR1cm4gZmFsc2U7CiAgICB9LAogICAgLyoqDQogICAgICog5aSE55CGUERG5riy5p+T54q25oCB5Y+Y5YyWDQogICAgICogQHBhcmFtIHtib29sZWFufSBpc1JlbmRlcmVkIOaYr+WQpua4suafk+WujOaIkA0KICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwZGZUeXBlIFBERuexu+Wei++8midyZXNwb25zZScg5oiWICdwcm9jdXJlbWVudCcNCiAgICAgKi8KICAgIGhhbmRsZVBkZlJlbmRlclN0YXR1c0NoYW5nZTogZnVuY3Rpb24gaGFuZGxlUGRmUmVuZGVyU3RhdHVzQ2hhbmdlKGlzUmVuZGVyZWQsIHBkZlR5cGUpIHsKICAgICAgaWYgKHBkZlR5cGUgPT09ICdyZXNwb25zZScpIHsKICAgICAgICB0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQgPSBpc1JlbmRlcmVkOwogICAgICB9IGVsc2UgaWYgKHBkZlR5cGUgPT09ICdwcm9jdXJlbWVudCcpIHsKICAgICAgICB0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQgPSBpc1JlbmRlcmVkOwogICAgICB9CiAgICAgIGlmIChpc1JlbmRlcmVkKSB7CiAgICAgICAgY29uc29sZS5sb2coIiIuY29uY2F0KHBkZlR5cGUgPT09ICdyZXNwb25zZScgPyAn5ZON5bqUJyA6ICfph4fotK0nLCAiXHU2NTg3XHU0RUY2XHU2RTMyXHU2N0QzXHU1QjhDXHU2MjEwXHVGRjBDXHU1M0VGXHU0RUU1XHU4RkRCXHU4ODRDXHU5ODc1XHU5NzYyXHU4REYzXHU4RjZDIikpOwogICAgICB9CgogICAgICAvLyDmo4Dmn6XmiYDmnInmmL7npLrnmoRQREbmmK/lkKbpg73muLLmn5PlrozmiJAKICAgICAgdmFyIGFsbFJlbmRlcmVkID0gdHJ1ZTsKICAgICAgaWYgKHRoaXMuaXNTaG93UmVzcG9uc2UgJiYgIXRoaXMucmVzcG9uc2VQZGZSZW5kZXJlZCkgewogICAgICAgIGFsbFJlbmRlcmVkID0gZmFsc2U7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgJiYgIXRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZCkgewogICAgICAgIGFsbFJlbmRlcmVkID0gZmFsc2U7CiAgICAgIH0KICAgICAgaWYgKGFsbFJlbmRlcmVkKSB7CiAgICAgICAgdGhpcy5pc1BkZlJlbmRlcmluZyA9IGZhbHNlOyAvLyDmiYDmnIlQREbmuLLmn5PlrozmiJDvvIzop6PpmaTnpoHnlKjnirbmgIEKICAgICAgfQogICAgfSwKICAgIC8qKg0KICAgICAqIOaPkOS6pOivhOWIhuW5tuS/ruaUueS4k+Wutui/m+W6pg0KICAgICAqLwogICAgc3VibWl0OiBmdW5jdGlvbiBzdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWUzKCkgewogICAgICAgIHZhciBzYXZlUmVzdWx0LCBkYXRhLCByZXNwb25zZSwgc3RhdHVzLCByZXMsIF90MzsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS53KGZ1bmN0aW9uIChfY29udGV4dDMpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0My5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDMubiA9IDE7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMy5zYXZlVGVtcFJhdGluZygpOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgc2F2ZVJlc3VsdCA9IF9jb250ZXh0My52OwogICAgICAgICAgICAgIGlmIChzYXZlUmVzdWx0KSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDMubiA9IDI7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5hKDIpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgZGF0YSA9IHsKICAgICAgICAgICAgICAgIHByb2plY3RJZDogX3RoaXMzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsCiAgICAgICAgICAgICAgICAvLyDpobnnm65JRAogICAgICAgICAgICAgICAgZXhwZXJ0UmVzdWx0SWQ6IF90aGlzMy5leHBlcnRJbmZvLnJlc3VsdElkLAogICAgICAgICAgICAgICAgLy8g5LiT5a6257uT5p6cSUQKICAgICAgICAgICAgICAgIHNjb3JpbmdNZXRob2RJdGVtSWQ6IF90aGlzMy4kcm91dGUucXVlcnkuc2NvcmluZ01ldGhvZEl0ZW1JZCAvLyDor4TliIbmlrnms5XpoblJRAogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgX2NvbnRleHQzLnAgPSAzOwogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gNDsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9yZXZpZXcuY2hlY2tSZXZpZXdTdW1tYXJ5KShkYXRhKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQzLnY7CiAgICAgICAgICAgICAgaWYgKCEocmVzcG9uc2UuY29kZSA9PT0gMjAwKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSA2OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIC8vIOS/ruaUueS4k+Wutui/m+W6pgogICAgICAgICAgICAgIHN0YXR1cyA9IHsKICAgICAgICAgICAgICAgIGV2YWxFeHBlcnRTY29yZUluZm9JZDogSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZXZhbEV4cGVydFNjb3JlSW5mbyIpKS5ldmFsRXhwZXJ0U2NvcmVJbmZvSWQsCiAgICAgICAgICAgICAgICAvLyDkuJPlrrbor4TliIbkv6Hmga9JRAogICAgICAgICAgICAgICAgZXZhbFN0YXRlOiAxIC8vIOi/m+W6pueKtuaAgQogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSA1OwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2V4cGVydFN0YXR1cy5lZGl0RXZhbEV4cGVydFNjb3JlSW5mbykoc3RhdHVzKTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIHJlcyA9IF9jb250ZXh0My52OwogICAgICAgICAgICAgIC8vIOe8lui+keS4k+WutuivhOWIhueKtuaAgQogICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2Uuc3VjY2Vzcygi5o+Q5Lqk5oiQ5YqfIik7IC8vIOaPkOS6pOaIkOWKn+aPkOekugogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczMuJGVtaXQoInNlbmQiLCAidHdvIik7IC8vIOWPkemAgeS6i+S7tgogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gNzsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7IC8vIOaPkOS6pOWksei0peaPkOekugogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSA5OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDg6CiAgICAgICAgICAgICAgX2NvbnRleHQzLnAgPSA4OwogICAgICAgICAgICAgIF90MyA9IF9jb250ZXh0My52OwogICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcigi5o+Q5Lqk5aSx6LSlIik7IC8vIOW8guW4uOaPkOekugogICAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5hKDIpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzLCBudWxsLCBbWzMsIDhdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKg0KICAgICAqIOaYvuekuumHh+i0reaWh+S7tlBERg0KICAgICAqLwogICAgdmlld1B1cmNoYXNpbmc6IGZ1bmN0aW9uIHZpZXdQdXJjaGFzaW5nKCkgewogICAgICB0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdwcm9jdXJlbWVudCc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrgogICAgICB0aGlzLmlzRG91YmxlVmlldyA9IGZhbHNlOyAvLyDljZXmlofku7bmqKHlvI8KICAgICAgdGhpcy5pc1Nob3dSZXNwb25zZSA9IGZhbHNlOyAvLyDkuI3mmL7npLrlk43lupTmlofku7YKICAgICAgdGhpcy5pc1Nob3dQcm9jdXJlbWVudCA9IHRydWU7IC8vIOaYvuekuumHh+i0reaWh+S7tgogICAgICB0aGlzLmlzUGRmUmVuZGVyaW5nID0gdHJ1ZTsgLy8g5byA5aeL5riy5p+TUERGCgogICAgICAvLyDorr7nva7ph4fotK3mlofku7ZQREblnLDlnYAKICAgICAgaWYgKHRoaXMucHJvamVjdEZpbGVzLnRlbmRlck5vdGljZUZpbGVQYXRoKSB7CiAgICAgICAgdGhpcy5wcm9jdXJlbWVudFBkZlVybCA9IHRoaXMucHJvamVjdEZpbGVzLnRlbmRlck5vdGljZUZpbGVQYXRoOwogICAgICB9CgogICAgICAvLyDlj7Pkvqfor4TliIbpobnmmL7npLrkuLrph4fotK3mlofku7bnmoTor4TliIbpobkKICAgICAgdmFyIHBhZ2VQcm9jdXJlbWVudEFyciA9IFtdOyAvLyDph4fotK3mlofku7bor4TliIbpobnmlbDnu4QKCiAgICAgIGZvciAodmFyIGl0ZW0gaW4gdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2UpIHsKICAgICAgICBwYWdlUHJvY3VyZW1lbnRBcnIucHVzaCh7CiAgICAgICAgICBpdGVtTmFtZTogaXRlbSwKICAgICAgICAgIGp1bXBUb1BhZ2U6IHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2l0ZW1dCiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgY29uc29sZS5sb2codGhpcy5zY29yaW5nTWV0aG9kLnVpdGVtcyk7CiAgICAgIGNvbnNvbGUubG9nKHBhZ2VQcm9jdXJlbWVudEFycik7CiAgICAgIHRoaXMucGFnZVByb2N1cmVtZW50ID0gW107CiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdGhpcy5zY29yaW5nTWV0aG9kLnVpdGVtcy5sZW5ndGg7IGkrKykgewogICAgICAgIGZvciAodmFyIGogPSAwOyBqIDwgcGFnZVByb2N1cmVtZW50QXJyLmxlbmd0aDsgaisrKSB7CiAgICAgICAgICBpZiAodGhpcy5zY29yaW5nTWV0aG9kLnVpdGVtc1tpXS5pdGVtTmFtZSA9PSBwYWdlUHJvY3VyZW1lbnRBcnJbal0uaXRlbU5hbWUpIHsKICAgICAgICAgICAgdGhpcy5wYWdlUHJvY3VyZW1lbnQucHVzaCgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLnNjb3JpbmdNZXRob2QudWl0ZW1zW2ldKSwgcGFnZVByb2N1cmVtZW50QXJyW2pdKSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIGNvbnNvbGUubG9nKHRoaXMucGFnZVByb2N1cmVtZW50KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDot7PovazliLDkuozmrKHmiqXku7fpobXpnaINCiAgICAgKi8KICAgIHNlY29uZE9mZmVyOiBmdW5jdGlvbiBzZWNvbmRPZmZlcigpIHsKICAgICAgdmFyIHF1ZXJ5ID0gewogICAgICAgIHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLAogICAgICAgIC8vIOmhueebrklECiAgICAgICAgempobTogdGhpcy4kcm91dGUucXVlcnkuempobSwKICAgICAgICAvLyDkuJPlrrbor4Hku7blj7fnoIEKICAgICAgICBzY29yaW5nTWV0aG9kSXRlbUlkOiBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJ0ZW5kZXJPZmZlclNjb3JpbmdNZXRob2RJdGVtcyIpKSAvLyDkuozmrKHmiqXku7for4TliIbmlrnms5XpoblJRAogICAgICB9OwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogIi9zZWNvbmRPZmZlciIsCiAgICAgICAgcXVlcnk6IHF1ZXJ5CiAgICAgIH0pOyAvLyDot7PovazpobXpnaIKICAgIH0sCiAgICAvKioNCiAgICAgKiDot7PovazliLDor6LmoIfpobXpnaINCiAgICAgKi8KICAgIGJpZElucXVpcnk6IGZ1bmN0aW9uIGJpZElucXVpcnkoKSB7CiAgICAgIHZhciBxdWVyeSA9IHsKICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwKICAgICAgICAvLyDpobnnm65JRAogICAgICAgIHpqaG06IHRoaXMuJHJvdXRlLnF1ZXJ5LnpqaG0sCiAgICAgICAgLy8g5LiT5a626K+B5Lu25Y+356CBCiAgICAgICAgc2NvcmluZ01ldGhvZEl0ZW1JZDogSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgidGVuZGVyT2ZmZXJTY29yaW5nTWV0aG9kSXRlbXMiKSkgLy8g6K+i5qCH6K+E5YiG5pa55rOV6aG5SUQKICAgICAgfTsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICIvYmlkSW5xdWlyeSIsCiAgICAgICAgcXVlcnk6IHF1ZXJ5CiAgICAgIH0pOyAvLyDot7PovazpobXpnaIKICAgIH0sCiAgICAvKioNCiAgICAgKiDojrflj5blm6DntKDlr7nlupTpobXnoIHvvIjku47mnKzlnLDnvJPlrZjvvIkNCiAgICAgKi8KICAgIGdldEZhY3RvcnNQYWdlOiBmdW5jdGlvbiBnZXRGYWN0b3JzUGFnZSgpIHsKICAgICAgdGhpcy5mYWN0b3JzUGFnZU1hcCA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImVudERvY1Jlc3BvbnNlUGFnZSIpKTsgLy8g6I635Y+W5Zug5a2Q6aG156CB5pig5bCECiAgICB9LAogICAgLyoqDQogICAgICog5Yid5aeL5YyW5LiT5a625ZKM5pys5Zyw5pWw5o2u77yM5Y+q5ZyobW91bnRlZOaXtuiwg+eUqOS4gOasoQ0KICAgICAqLwogICAgaW5pdExvY2FsRGF0YTogZnVuY3Rpb24gaW5pdExvY2FsRGF0YSgpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLmxvY2FsRXhwZXJ0SW5mbyA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImV4cGVydEluZm8iKSB8fCAie30iKTsgLy8g6I635Y+W5pys5Zyw5LiT5a625L+h5oGvCiAgICAgICAgdGhpcy5sb2NhbEVudERvY1Jlc3BvbnNlUGFnZSA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImVudERvY1Jlc3BvbnNlUGFnZSIpIHx8ICJ7fSIpOyAvLyDojrflj5bmnKzlnLDlk43lupTmlofku7bpobXnoIEKICAgICAgICB0aGlzLmxvY2FsRmFjdG9yc1BhZ2VNYXAgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJlbnREb2NSZXNwb25zZVBhZ2UiKSB8fCAie30iKTsgLy8g6I635Y+W5pys5Zyw5Zug5a2Q6aG156CB5pig5bCECiAgICAgICAgdGhpcy5leHBlcnRJbmZvID0gdGhpcy5sb2NhbEV4cGVydEluZm87IC8vIOiuvue9ruS4k+WutuS/oeaBrwogICAgICAgIHRoaXMuZW50RG9jUmVzcG9uc2VQYWdlID0gdGhpcy5sb2NhbEVudERvY1Jlc3BvbnNlUGFnZTsgLy8g6K6+572u5ZON5bqU5paH5Lu26aG156CBCiAgICAgICAgdGhpcy5mYWN0b3JzUGFnZU1hcCA9IHRoaXMubG9jYWxGYWN0b3JzUGFnZU1hcDsgLy8g6K6+572u5Zug5a2Q6aG156CB5pig5bCECiAgICAgICAgY29uc29sZS5sb2coIuacrOWcsOaVsOaNruW3suWIneWni+WMliIsIHsKICAgICAgICAgIGV4cGVydEluZm86IHRoaXMuZXhwZXJ0SW5mbwogICAgICAgIH0pOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIneWni+WMluacrOWcsOaVsOaNruWksei0pToiLCBlcnJvcik7CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKiDliJ3lp4vljJbkuJPlrrbkv6Hmga/vvIjnlKjkuo7lk43lupTkuJPlrrbkv6Hmga/mm7TmlrDvvIkNCiAgICAgKi8KICAgIGluaXRFeHBlcnRJbmZvOiBmdW5jdGlvbiBpbml0RXhwZXJ0SW5mbygpIHsKICAgICAgdHJ5IHsKICAgICAgICB2YXIgZXhwZXJ0SW5mb1N0ciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJleHBlcnRJbmZvIik7CiAgICAgICAgaWYgKGV4cGVydEluZm9TdHIpIHsKICAgICAgICAgIHRoaXMubG9jYWxFeHBlcnRJbmZvID0gSlNPTi5wYXJzZShleHBlcnRJbmZvU3RyKTsKICAgICAgICAgIHRoaXMuZXhwZXJ0SW5mbyA9IHRoaXMubG9jYWxFeHBlcnRJbmZvOwogICAgICAgICAgY29uc29sZS5sb2coIuS4k+WutuS/oeaBr+W3suWIt+aWsCIsIHRoaXMuZXhwZXJ0SW5mbyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIt+aWsOS4k+WutuS/oeaBr+Wksei0pToiLCBlcnJvcik7CiAgICAgIH0KICAgIH0sCiAgICAvKioNCiAgICAgKiDpobXpnaLliJ3lp4vljJbvvIzliqDovb3kvpvlupTllYbjgIHor4TliIbmlrnms5XjgIHmlofku7bnrYnvvIjlubblj5Hor7fmsYLvvIkNCiAgICAgKi8KICAgIGluaXRQYWdlOiBmdW5jdGlvbiBpbml0UGFnZSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLm0oZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgdmFyIF95aWVsZCRQcm9taXNlJGFsbCwgX3lpZWxkJFByb21pc2UkYWxsMiwgc3VwcGxpZXJSZXMsIGFwcHJvdmFsUmVzLCBmaWxlc1JlcywgX3Q0OwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ0Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF90aGlzNC5pbml0TG9jYWxEYXRhKCk7IC8vIOWIneWni+WMluacrOWcsOaVsOaNrgogICAgICAgICAgICAgIF9jb250ZXh0NC5wID0gMTsKICAgICAgICAgICAgICBfY29udGV4dDQubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIFByb21pc2UuYWxsKFsoMCwgX3Jldmlldy5zdXBwbGllckluZm8pKHsKICAgICAgICAgICAgICAgIHByb2plY3RJZDogX3RoaXM0LiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQKICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAvLyDojrflj5bkvpvlupTllYYKICAgICAgICAgICAgICAoMCwgX3Jldmlldy5hcHByb3ZhbFByb2Nlc3MpKF90aGlzNC4kcm91dGUucXVlcnkucHJvamVjdElkLCBfdGhpczQuZXhwZXJ0SW5mby5yZXN1bHRJZCksCiAgICAgICAgICAgICAgLy8g6I635Y+W6K+E5YiG5pa55rOVCiAgICAgICAgICAgICAgKDAsIF9yZXZpZXcuZmlsZXNCeUlkKShfdGhpczQuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCkgLy8g6I635Y+W6aG555uu5paH5Lu2CiAgICAgICAgICAgICAgXSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfeWllbGQkUHJvbWlzZSRhbGwgPSBfY29udGV4dDQudjsKICAgICAgICAgICAgICBfeWllbGQkUHJvbWlzZSRhbGwyID0gKDAsIF9zbGljZWRUb0FycmF5Mi5kZWZhdWx0KShfeWllbGQkUHJvbWlzZSRhbGwsIDMpOwogICAgICAgICAgICAgIHN1cHBsaWVyUmVzID0gX3lpZWxkJFByb21pc2UkYWxsMlswXTsKICAgICAgICAgICAgICBhcHByb3ZhbFJlcyA9IF95aWVsZCRQcm9taXNlJGFsbDJbMV07CiAgICAgICAgICAgICAgZmlsZXNSZXMgPSBfeWllbGQkUHJvbWlzZSRhbGwyWzJdOwogICAgICAgICAgICAgIC8vIOWkhOeQhuS+m+W6lOWVhgogICAgICAgICAgICAgIGlmIChzdXBwbGllclJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICAgIF90aGlzNC5zdXBwbGllck9wdGlvbnMgPSBzdXBwbGllclJlcy5yb3dzLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICByZXR1cm4gaXRlbS5pc0FiYW5kb25lZEJpZCA9PSAwOwogICAgICAgICAgICAgICAgfSk7IC8vIOi/h+a7pOacquW8g+agh+S+m+W6lOWVhgogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2Uud2FybmluZyhzdXBwbGllclJlcy5tc2cpOyAvLyDojrflj5blpLHotKXmj5DnpLoKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgLy8g5aSE55CG6K+E5YiG5pa55rOVCiAgICAgICAgICAgICAgaWYgKGFwcHJvdmFsUmVzLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICAgICAgX3RoaXM0LmF0dGFjaG1lbnRzTGlzdCA9IGFwcHJvdmFsUmVzLmRhdGEuYnVzaVRlbmRlck5vdGljZS5hdHRhY2htZW50cy5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0uZmlsZVR5cGUgPT0gIjAiOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICBfdGhpczQuc2NvcmluZ01ldGhvZCA9IGFwcHJvdmFsUmVzLmRhdGEuc2NvcmluZ01ldGhvZFVpbmZvLnNjb3JpbmdNZXRob2RJdGVtcy5maW5kKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLnNjb3JpbmdNZXRob2RJdGVtSWQgPT0gX3RoaXM0LiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkOwogICAgICAgICAgICAgICAgfSk7IC8vIOiOt+WPluW9k+WJjeivhOWIhuaWueazlQogICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oImV2YWxQcm9qZWN0RXZhbHVhdGlvblByb2Nlc3MiLCBKU09OLnN0cmluZ2lmeShfdGhpczQuc2NvcmluZ01ldGhvZC5ldmFsUHJvamVjdEV2YWx1YXRpb25Qcm9jZXNzKSk7IC8vIOe8k+WtmOivhOWIhua1geeoiwogICAgICAgICAgICAgICAgX3RoaXM0LnJhdGluZ1N0YXRlTWFwID0gX3RoaXM0LnNjb3JpbmdNZXRob2QudWl0ZW1zLnJlZHVjZShmdW5jdGlvbiAoYWNjLCBpdGVtKSB7CiAgICAgICAgICAgICAgICAgIGFjY1tpdGVtLmVudE1ldGhvZEl0ZW1JZF0gPSB7CiAgICAgICAgICAgICAgICAgICAgc3RhdGU6IG51bGwsCiAgICAgICAgICAgICAgICAgICAgcmVhc29uOiAnJwogICAgICAgICAgICAgICAgICB9OyAvLyDliJ3lp4vljJbor4TliIbnirbmgIEKICAgICAgICAgICAgICAgICAgcmV0dXJuIGFjYzsKICAgICAgICAgICAgICAgIH0sIHt9KTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLndhcm5pbmcoYXBwcm92YWxSZXMubXNnKTsgLy8g6I635Y+W5aSx6LSl5o+Q56S6CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIC8vIOWkhOeQhuaWh+S7tgogICAgICAgICAgICAgIGlmIChmaWxlc1Jlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICAgIF90aGlzNC5wcm9qZWN0RmlsZXMgPSBmaWxlc1Jlcy5kYXRhOyAvLyDorr7nva7pobnnm67mlofku7YKICAgICAgICAgICAgICAgIC8vIOazqOmHiuaOieiHquWKqOiuvue9rumHh+i0reaWh+S7tlBERu+8jOaUueS4uueCueWHu+aXtuaJjeiuvue9rgogICAgICAgICAgICAgICAgLy8gaWYgKHRoaXMucHJvamVjdEZpbGVzLnRlbmRlck5vdGljZUZpbGVQYXRoKSB7CiAgICAgICAgICAgICAgICAvLyAgIHRoaXMucHJvY3VyZW1lbnRQZGZVcmwgPSB0aGlzLnByb2plY3RGaWxlcy50ZW5kZXJOb3RpY2VGaWxlUGF0aDsgLy8g6K6+572u6YeH6LSt5paH5Lu2UERGCiAgICAgICAgICAgICAgICAvLyB9CiAgICAgICAgICAgICAgICAvLyBpZiAodGhpcy5wcm9qZWN0RmlsZXMuZmlsZSkgewogICAgICAgICAgICAgICAgLy8gICB0aGlzLnJlc3BvbnNlUGRmVXJsID0gdGhpcy5wcm9qZWN0RmlsZXMuZmlsZVswXTsgLy8g6K6+572u5ZON5bqU5paH5Lu2UERGCiAgICAgICAgICAgICAgICAvLyB9CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzNC4kbWVzc2FnZS53YXJuaW5nKGZpbGVzUmVzLm1zZyk7IC8vIOiOt+WPluWksei0peaPkOekugogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDQubiA9IDQ7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBfY29udGV4dDQucCA9IDM7CiAgICAgICAgICAgICAgX3Q0ID0gX2NvbnRleHQ0LnY7CiAgICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLmVycm9yKCLpobXpnaLliJ3lp4vljJblpLHotKUiKTsgLy8g5byC5bi45o+Q56S6CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTQsIG51bGwsIFtbMSwgM11dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgZG93bmxvYWRGaWxlOiBmdW5jdGlvbiBkb3dubG9hZEZpbGUoaXRlbSkgewogICAgICB0aGlzLiRkb3dubG9hZC56aXAoaXRlbS5maWxlUGF0aCwgaXRlbS5maWxlTmFtZSk7CiAgICB9LAogICAgLy8gPT09PT09PT09PSDmgqzlgZznm7jlhbMgPT09PT09PT09PQogICAgLyoqDQogICAgICog5pi+56S66K+E5YiG6aG55oKs5rWu5qGGDQogICAgICogQHBhcmFtIHtPYmplY3R9IGZhY3Rvckl0ZW0g6K+E5YiG6aG55a+56LGhDQogICAgICovCiAgICBzaG93RmFjdG9yVG9vbHRpcDogZnVuY3Rpb24gc2hvd0ZhY3RvclRvb2x0aXAoZmFjdG9ySXRlbSkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgaWYgKCFmYWN0b3JJdGVtLml0ZW1SZW1hcmspIHJldHVybjsgLy8g5aaC5p6c5rKh5pyJ6K+E5a6h5YaF5a655YiZ5LiN5pi+56S6CgogICAgICAvLyDmuIXpmaTkuYvliY3nmoTlrprml7blmagKICAgICAgaWYgKHRoaXMudG9vbHRpcFRpbWVyKSB7CiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsKICAgICAgfQoKICAgICAgLy8g5bu26L+f5pi+56S65oKs5rWu5qGG77yM6YG/5YWN5b+r6YCf56e75Yqo5pe26aKR57mB5pi+56S6CiAgICAgIHRoaXMudG9vbHRpcFRpbWVyID0gc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LmhvdmVyZWRGYWN0b3JOb2RlID0gZmFjdG9ySXRlbTsKICAgICAgfSwgMzAwKTsgLy8gMzAwbXPlu7bov58KICAgIH0sCiAgICAvKioNCiAgICAgKiDpmpDol4/or4TliIbpobnmgqzmta7moYYNCiAgICAgKi8KICAgIGhpZGVGYWN0b3JUb29sdGlwOiBmdW5jdGlvbiBoaWRlRmFjdG9yVG9vbHRpcCgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIC8vIOa4hemZpOWumuaXtuWZqAogICAgICBpZiAodGhpcy50b29sdGlwVGltZXIpIHsKICAgICAgICBjbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOwogICAgICAgIHRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsKICAgICAgfQoKICAgICAgLy8g5bu26L+f6ZqQ6JeP77yM57uZ55So5oi35pe26Ze056e75Yqo5Yiw5oKs5rWu5qGG5LiKCiAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNi5ob3ZlcmVkRmFjdG9yTm9kZSA9IG51bGw7CiAgICAgIH0sIDEwMCk7CiAgICB9LAogICAgLyoqDQogICAgICog5riF6Zmk5oKs5rWu5qGG5a6a5pe25Zmo77yI5b2T6byg5qCH56e75Yqo5Yiw5oKs5rWu5qGG5LiK5pe277yJDQogICAgICovCiAgICBjbGVhclRvb2x0aXBUaW1lcjogZnVuY3Rpb24gY2xlYXJUb29sdGlwVGltZXIoKSB7CiAgICAgIGlmICh0aGlzLnRvb2x0aXBUaW1lcikgewogICAgICAgIGNsZWFyVGltZW91dCh0aGlzLnRvb2x0aXBUaW1lcik7CiAgICAgICAgdGhpcy50b29sdGlwVGltZXIgPSBudWxsOwogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2UgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJlbnREb2NQcm9jdXJlbWVudFBhZ2UiKSk7IC8vIOWIneWni+WMlumHh+i0reaWh+S7tumhteeggeS/oeaBrwogICAgdGhpcy5pbml0UGFnZSgpOyAvLyDpobXpnaLmjILovb3ml7bliJ3lp4vljJbmlbDmja4KICAgIHRoaXMuZ2V0RmFjdG9yc1BhZ2UoKTsgLy8g6I635Y+W5Zug5a2Q6aG156CBCiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgLy8g5riF55CG5a6a5pe25ZmoCiAgICBpZiAodGhpcy50b29sdGlwVGltZXIpIHsKICAgICAgY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsKICAgICAgdGhpcy50b29sdGlwVGltZXIgPSBudWxsOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_review", "require", "_detail", "_expertStatus", "_entInfo", "PASS", "FAIL", "CHECK_PASS", "CHECK_FAIL", "_default", "exports", "default", "data", "supplierOptions", "<PERSON><PERSON><PERSON><PERSON>", "selectedFactorNode", "selectedSupplierName", "selectedSupplier", "expertInfo", "ratingStateMap", "projectFiles", "isShowResponse", "isShowProcurement", "isDoubleView", "factorDetailList", "entDocResponsePage", "factorsPageMap", "supplierFactorPage", "responsePdfUrl", "procurementPdfUrl", "activeButton", "entDocProcurementPage", "pageProcurement", "attachmentsList", "responsePdfRendered", "procurementPdfRendered", "helpImgList", "isPdfRendering", "factorCodeMap", "checkResult", "checkResultNameMap", "localExpertInfo", "localEntDocResponsePage", "localFactorsPageMap", "hoveredFactorNode", "tooltipTimer", "methods", "validateRatings", "_iterator", "_createForOfIteratorHelper2", "uitems", "_step", "s", "n", "done", "item", "value", "state", "entMethodItemId", "reason", "trim", "console", "log", "concat", "itemName", "err", "e", "f", "getCheckResultState", "factorName", "Object", "keys", "length", "code", "check", "resetRatingStateMap", "_i", "_Object$keys", "key", "saveTempRating", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "response", "_t", "w", "_context", "a", "map", "itemId", "scoringMethodUitemId", "expertResultId", "resultId", "entId", "bidderId", "evaluationResult", "evaluationRemark", "filter", "d", "p", "scoringFactors", "v", "$message", "success", "warning", "msg", "error", "handleSupplierChange", "supplierName", "_this2", "_callee2", "_yield$Promise$allSet", "_yield$Promise$allSet2", "detailResult", "detailRes", "_this2$factorDetailLi", "factor", "_iterator2", "_step2", "checkRes", "_t2", "_context2", "find", "bidderName", "Promise", "allSettled", "getDetailByPsxx", "projectId", "$route", "query", "scoringMethodItemId", "resDocReviewFactorsDecision", "_slicedToArray2", "status", "evalExpertEvaluationDetails", "showResponseFile", "file", "showFileContrast", "handleShowFactorInfo", "factorItem", "canJumpToPage", "jumpToPage", "$refs", "procurement", "skipPage", "handlePdfRenderStatusChange", "isRendered", "pdfType", "allRendered", "submit", "_this3", "_callee3", "saveResult", "res", "_t3", "_context3", "checkReviewSummary", "evalExpertScoreInfoId", "JSON", "parse", "localStorage", "getItem", "evalState", "editEvalExpertScoreInfo", "$emit", "viewPurchasing", "tenderNoticeFilePath", "pageProcurementArr", "push", "i", "j", "_objectSpread2", "secondOffer", "zjhm", "$router", "path", "bidInquiry", "getFactorsPage", "initLocalData", "initExpertInfo", "expertInfoStr", "initPage", "_this4", "_callee4", "_yield$Promise$all", "_yield$Promise$all2", "supplierRes", "approvalRes", "filesRes", "_t4", "_context4", "all", "supplierInfo", "approvalProcess", "filesById", "rows", "isAbandonedBid", "busiTenderNotice", "attachments", "fileType", "scoringMethodUinfo", "scoringMethodItems", "setItem", "stringify", "evalProjectEvaluationProcess", "reduce", "acc", "downloadFile", "$download", "zip", "filePath", "fileName", "showFactorTooltip", "_this5", "itemRemark", "clearTimeout", "setTimeout", "hideFactorTooltip", "_this6", "clearTooltipTimer", "mounted", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/expertReview/compliance/one.vue"], "sourcesContent": ["<template>\r\n  <!-- 页面主容器，flex布局，分为左中右三部分 -->\r\n  <div class=\"compliance-main\">\r\n    <!-- 左侧内容区，包含标题、操作按钮、PDF预览区 -->\r\n    <div class=\"compliance-left\">\r\n      <!-- 顶部标题和操作按钮区 -->\r\n      <div class=\"compliance-header\">\r\n        <!-- 标题及操作步骤图片 -->\r\n        <div class=\"compliance-title-group\">\r\n          <div class=\"compliance-title\">符合性评审</div> <!-- 页面主标题 -->\r\n          <div class=\"compliance-step-img-group\">\r\n            <div class=\"compliance-step-text\">该页面操作说明</div> <!-- 操作步骤说明文字 -->\r\n            <el-image class=\"compliance-step-img\" :src=\"helpImgList[0]\" :preview-src-list=\"helpImgList\">\r\n            </el-image> <!-- 操作步骤图片，可点击放大 -->\r\n          </div>\r\n        </div>\r\n\t      \r\n\t      <!-- 文件列表 -->\r\n\t      <div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t      <div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t      <el-card\r\n\t\t\t      v-for=\"(item, index) in attachmentsList\"\r\n\t\t\t      :key=\"index\"\r\n\t\t\t      class=\"fileItem\"\r\n\t\t\t      shadow=\"hover\"\r\n\t\t\t      @click.native=\"downloadFile(item)\"\r\n\t\t\t      style=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t      >\r\n\t\t\t      <div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t      <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t      <span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t      <i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t      </div>\r\n\t\t      </el-card>\r\n\t      </div>\r\n\t      \r\n        <!-- 右侧操作按钮区 -->\r\n        <div class=\"compliance-header-btns\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\" :disabled=\"isPdfRendering\">询标</el-button> <!-- 跳转到询标页面 -->\r\n          <!-- <el-button class=\"item-button\" @click=\"secondOffer\">发起二次报价</el-button> -->\r\n          <div class=\"compliance-header-btns-bottom\">\r\n           <el-button\r\n            :class=\"['item-button', activeButton === 'procurement' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n            @click=\"viewPurchasing\"\r\n              :disabled=\"isPdfRendering\">采购文件</el-button> <!-- 显示采购文件PDF -->\r\n           \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showResponseFile\"\r\n              :disabled=\"isPdfRendering\">响应文件</el-button> <!-- 显示响应文件PDF -->\r\n            \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showFileContrast\"\r\n              :disabled=\"isPdfRendering\">对比</el-button> <!-- 响应文件与采购文件对比 -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- PDF文件预览区，支持单文件和双文件对比 -->\r\n      <div style=\"height:82%\">\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"compliance-pdf-group\">\r\n         <!-- 采购文件PDF预览 -->\r\n         <div v-if=\"isShowProcurement\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-left' : '']\">\r\n          <!--            <pdfView ref=\"procurement\" :pdfurl=\"procurementPdfUrl\" :uni_key=\"'procurement'\"></pdfView>-->\r\n          \r\n          <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdfUrl\"  :page-height=\"800\"\r\n                           :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n         \r\n         </div>\r\n         \r\n          <!-- 响应文件PDF预览 -->\r\n          <div v-if=\"isShowResponse\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-right' : '']\">\r\n<!--            <pdfView ref=\"response\" :pdfurl=\"responsePdfUrl\" :uni_key=\"'response'\"></pdfView>-->\r\n           \r\n           <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdfUrl\"  :page-height=\"800\" :buffer-size=\"2\"\r\n                            @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n           \r\n          </div>\r\n           \r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 中间分割线 -->\r\n    <div class=\"compliance-divider\"></div>\r\n    <!-- 右侧评分区 -->\r\n    <div class=\"compliance-right\">\r\n      <!-- 供应商选择下拉框 -->\r\n      <div class=\"compliance-select-group\">\r\n        <el-select class=\"compliance-select\" v-model=\"selectedSupplierName\" placeholder=\"请选择供应商\" @change=\"handleSupplierChange\" :disabled=\"isPdfRendering\">\r\n          <el-option v-for=\"item in supplierOptions\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n      <!-- 评分因子列表及操作区 -->\r\n      <div class=\"compliance-factors-group\" v-if=\"isShowResponse\">\r\n\t      <!-- PDF渲染状态提示 -->\r\n\t      <div v-if=\"responsePdfUrl && !responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t      </div>\r\n\t      <div v-else-if=\"responsePdfUrl && responsePdfRendered\" class=\"render-status-tip success\">\r\n\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t      </div>\r\n\t      \r\n        <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n        <template v-if=\"scoringMethod && scoringMethod.uitems\">\r\n          <div v-for=\"(item, index) in scoringMethod.uitems\" :key=\"index\"\r\n               class=\"factor-item\" style=\"margin-bottom:10px\"\r\n               @mouseenter=\"showFactorTooltip(item)\"\r\n               @mouseleave=\"hideFactorTooltip\" >\r\n\t          <!-- 悬浮框 -->\r\n\t          <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t               class=\"factor-tooltip\"\r\n\t               @mouseenter=\"clearTooltipTimer\"\r\n\t               @mouseleave=\"hideFactorTooltip\">\r\n\t\t          <div class=\"tooltip-header\">\r\n\t\t\t          <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t          <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t          </div>\r\n\t\t          <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t          </div>\r\n\t          \r\n            <div>\r\n              <div class=\"factors\">\r\n                <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n                <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n                  <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n                    {{ item.itemName }}\r\n\t                  <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n                  </div>\r\n                </div>\r\n                <!-- 评分单选按钮（通过/不通过） -->\r\n                <div class=\"compliance-factor-radio-group\">\r\n                  <div>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 不通过时填写原因 -->\r\n              <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n              </el-input>\r\n              <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n              <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n              <div class=\"compliance-factor-divider\"></div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <!-- 提交按钮区 -->\r\n        <div class=\"compliance-submit-group\">\r\n          <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n          <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n        </div>\r\n        <!-- 当前选中评分因子的详细说明 -->\r\n        <div class=\"compliance-review-content\">\r\n          <div class=\"compliance-review-title\">评审内容：</div>\r\n          <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n\r\n\t    <div class=\"compliance-factors-group\" v-else>\r\n\t\t    <!-- PDF渲染状态提示 -->\r\n\t\t    <div v-if=\"!procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t    <i class=\"el-icon-loading\"></i>\r\n\t\t\t    <span>采购文件正在渲染中，请稍候...</span>\r\n\t\t    </div>\r\n\t\t    <div v-else class=\"render-status-tip success\">\r\n\t\t\t    <i class=\"el-icon-success\"></i>\r\n\t\t\t    <span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t    </div>\r\n\t\t    \r\n\t\t    <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n\t\t    <template v-if=\"pageProcurement\">\r\n\t\t\t    <div v-for=\"(item, index) in pageProcurement\" :key=\"index\" class=\"factor-item\" style=\"margin-bottom:10px\"\r\n\t\t\t         @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t         @mouseleave=\"hideFactorTooltip\" >\r\n\t\t\t\t    <!-- 悬浮框 -->\r\n\t\t\t\t    <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t         class=\"factor-tooltip\"\r\n\t\t\t\t         @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t         @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t    <div class=\"tooltip-header\">\r\n\t\t\t\t\t\t    <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t    <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t\t    <div>\r\n\t\t\t\t\t    <div class=\"factors\">\r\n\t\t\t\t\t\t    <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n\t\t\t\t\t\t\t    <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n\t\t\t\t\t\t\t\t    {{ item.itemName }}\r\n\t\t\t\t\t\t\t\t    <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    <!-- 评分单选按钮（通过/不通过） -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-radio-group\">\r\n\t\t\t\t\t\t\t    <div>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <!-- 不通过时填写原因 -->\r\n\t\t\t\t\t    <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t    </el-input>\r\n\t\t\t\t\t    <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n\t\t\t\t\t    <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n\t\t\t\t\t    <div class=\"compliance-factor-divider\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t    </div>\r\n\t\t    </template>\r\n\t\t    <!-- 提交按钮区 -->\r\n\t\t    <div class=\"compliance-submit-group\">\r\n\t\t\t    <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n\t\t\t    <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n\t\t    </div>\r\n\t\t    <!-- 当前选中评分因子的详细说明 -->\r\n\t\t    <div class=\"compliance-review-content\">\r\n\t\t\t    <div class=\"compliance-review-title\">评审内容：</div>\r\n\t\t\t    <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t    </div>\r\n\t    </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 常量定义\r\nconst PASS = '1'; // 通过\r\nconst FAIL = '0'; // 不通过\r\nconst CHECK_PASS = '系统初验通过'; // 系统初验通过文本\r\nconst CHECK_FAIL = '系统初验未通过'; // 系统初验未通过文本\r\n\r\nimport {\r\n  supplierInfo, // 获取供应商信息API\r\n  approvalProcess, // 获取评分方法API\r\n  scoringFactors, // 提交评分因子API\r\n  checkReviewSummary, // 检查评审汇总API\r\n  filesById, // 获取项目相关文件API\r\n} from \"@/api/expert/review\"; // 导入专家评审相关API\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\"; // 获取评分详情API\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\"; // 编辑专家评分状态API\r\nimport { resDocReviewFactorsDecision } from \"@/api/docResponse/entInfo\"; // 获取响应文件评审因子决策API\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      supplierOptions: [], // 供应商下拉选项列表\r\n      scoringMethod: null, // 当前评分方法对象\r\n      selectedFactorNode: {}, // 当前选中的评分因子节点\r\n      selectedSupplierName: '', // 当前选中的供应商名称\r\n      selectedSupplier: {}, // 当前选中的供应商对象\r\n      expertInfo: {}, // 当前专家信息\r\n      ratingStateMap: {}, // 评分项状态映射（key为评分项ID，value为{state, reason}）\r\n      projectFiles: {}, // 项目相关文件对象\r\n      isShowResponse: false, // 是否显示响应文件\r\n      isShowProcurement: false, // 是否显示采购文件\r\n      isDoubleView: false, // 是否双文件对比模式\r\n      factorDetailList: [], // 评分因子详细列表\r\n      entDocResponsePage: null, // 企业响应文件页码信息\r\n      factorsPageMap: null, // 供应商因子页码映射\r\n      supplierFactorPage: null, // 当前供应商因子页码\r\n      responsePdfUrl: null, // 响应文件PDF地址\r\n      procurementPdfUrl: null, // 采购文件PDF地址\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n   entDocProcurementPage: {}, // 采购文件页码信息\r\n    pageProcurement:[], // 采购文件的评分项\r\n    attachmentsList:[], // 文件列表\r\n   \r\n   // PDF渲染状态管理\r\n   responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n   procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n   helpImgList: [\"/evalution/help.jpg\"], // 操作帮助图片列表\r\n\r\n    // PDF渲染状态控制\r\n    isPdfRendering: false, // PDF是否正在渲染\r\n      // 评分项名称与后端字段映射\r\n      factorCodeMap: {\r\n        \"特定资格要求\": \"zgzs\",\r\n        \"响应内容\": \"jsplb\",\r\n        \"采购需求\": \"jsplb\",\r\n        \"供货期限\": \"ghqx\",\r\n        \"投标报价\": \"tbbj\"\r\n      },\r\n      checkResult: {}, // 系统初验结果对象\r\n      // 系统初验结果名称映射\r\n      checkResultNameMap: {\r\n        \"符合《中华人民共和国政府采购法》第二十二条规定\": CHECK_PASS,\r\n        \"特定资格要求\": CHECK_PASS,\r\n        \"信用查询\": CHECK_PASS,\r\n        \"响应人名称\": CHECK_PASS,\r\n        \"响应内容\": CHECK_PASS,\r\n        \"采购需求\": CHECK_PASS,\r\n        \"供货期限\": CHECK_PASS,\r\n        \"投标报价\": CHECK_PASS\r\n      },\r\n      // 本地缓存数据\r\n      localExpertInfo: null, // 本地专家信息\r\n      localEntDocResponsePage: null, // 本地响应文件页码\r\n      localFactorsPageMap: null, // 本地因子页码映射\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateRatings() {\r\n      for (const item of this.scoringMethod.uitems) { // 遍历所有评分项\r\n        const state = this.ratingStateMap[item.entMethodItemId].state; // 获取评分状态\r\n        const reason = this.ratingStateMap[item.entMethodItemId].reason; // 获取评分原因\r\n        // 评分结果未填写\r\n        if (state === null || state === '') {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`); // 提示未填写\r\n          return true;\r\n        }\r\n        // 不通过但未填写原因 - 将评审项设置为空，然后继续执行后续流程\r\n        if (state === FAIL && (!reason || reason.trim() === '')) {\r\n          // 将此评审项设置为空（未评审状态）\r\n          this.ratingStateMap[item.entMethodItemId].state = null;\r\n          this.ratingStateMap[item.entMethodItemId].reason = \"\";\r\n          console.log(`${item.itemName}评审不通过但未填写备注，已将该评审项设置为空`);\r\n        }\r\n      }\r\n      return true; // 全部填写返回true\r\n    },\r\n    /**\r\n     * 获取系统初验结果（通过/未通过）\r\n     * @param {string} factorName 评分项名称\r\n     * @returns {string} 1-通过 0-未通过\r\n     */\r\n    getCheckResultState(factorName) {\r\n      if (!this.checkResult || Object.keys(this.checkResult).length === 0) return ''; // 没有初验结果直接返回空\r\n      let code = this.factorCodeMap[factorName]; // 获取评分项对应的后端字段\r\n      let check = PASS; // 默认通过\r\n      if (code) {\r\n        check = this.checkResult[code]; // 获取初验结果\r\n        // 投标报价特殊处理\r\n        if (factorName === \"投标报价\" && check === PASS) {\r\n          check = this.checkResult['mxbjb']; // 明细报价表\r\n        }\r\n      }\r\n      // 设置初验结果名称\r\n      if (check === FAIL) {\r\n        this.checkResultNameMap[factorName] = CHECK_FAIL; // 未通过\r\n      } else {\r\n        check = PASS;\r\n        this.checkResultNameMap[factorName] = CHECK_PASS; // 通过\r\n      }\r\n      return check; // 返回初验结果\r\n    },\r\n    /**\r\n     * 重置所有评分项的状态\r\n     */\r\n    resetRatingStateMap() {\r\n      if (!this.scoringMethod) return; // 没有评分方法直接返回\r\n      for (const key of Object.keys(this.ratingStateMap)) { // 遍历所有评分项\r\n        this.ratingStateMap[key].state = null; // 重置状态\r\n        this.ratingStateMap[key].reason = ''; // 重置原因\r\n      }\r\n    },\r\n    /**\r\n     * 临时保存评分结果到后端\r\n     * 校验通过后才会保存\r\n     * @returns {boolean} 保存是否成功\r\n     */\r\n    async saveTempRating() {\r\n      if (!this.validateRatings()) return false; // 校验不通过不保存，返回false\r\n      // 构造提交数据\r\n      const data = this.scoringMethod.uitems.map(item => {\r\n        const itemId = item.entMethodItemId; // 获取评分项ID\r\n        return {\r\n          scoringMethodUitemId: itemId, // 评分项ID\r\n          expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n          entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          evaluationResult: this.ratingStateMap[itemId].state, // 评分结果\r\n          evaluationRemark: this.ratingStateMap[itemId].reason || '' // 评分原因\r\n        };\r\n      }).filter(d => d.evaluationResult !== null && d.evaluationResult !== ''); // 过滤未填写的项\r\n      if (data.length > 0) {\r\n        try {\r\n          const response = await scoringFactors(data); // 提交评分因子\r\n          if (response.code === 200) {\r\n            this.$message.success(\"保存成功\"); // 保存成功提示\r\n            return true; // 保存成功返回true\r\n          } else {\r\n            this.$message.warning(response.msg); // 保存失败提示\r\n            return false; // 保存失败返回false\r\n          }\r\n        } catch (e) {\r\n          this.$message.error(\"保存失败\"); // 异常提示\r\n          return false; // 异常返回false\r\n        }\r\n      }\r\n      return true; // 没有数据需要保存时也返回true\r\n    },\r\n    /**\r\n     * 供应商切换事件，切换时自动保存上一个供应商评分，并并发获取新供应商的评分详情和系统初验\r\n     * @param {string} supplierName 供应商名称\r\n     */\r\n    async handleSupplierChange(supplierName) {\r\n      // 切换前保存上一个供应商评分\r\n      if (Object.keys(this.selectedSupplier).length !== 0) {\r\n        await this.saveTempRating(); // 保存评分\r\n      }\r\n      // 查找当前选中的供应商对象\r\n      this.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName); // 查找供应商\r\n      // 获取当前供应商因子页码\r\n      this.supplierFactorPage = this.factorsPageMap[this.selectedSupplier.bidderId]; // 获取页码\r\n      // 并发获取评分详情和系统初验\r\n      // 使用 Promise.allSettled 让两个请求独立执行，互不影响\r\n      try {\r\n        const [detailResult, checkResult] = await Promise.allSettled([\r\n          getDetailByPsxx({\r\n            expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n          }),\r\n          resDocReviewFactorsDecision({\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          })\r\n        ]);\r\n\r\n        // 处理评分详情请求结果\r\n        if (detailResult.status === 'fulfilled') {\r\n          const detailRes = detailResult.value;\r\n          if (detailRes.code === 200) {\r\n            this.factorDetailList = detailRes.data; // 评分详情列表\r\n            const factor = this.factorDetailList.find(item => item.bidderName === supplierName)?.evalExpertEvaluationDetails; // 当前供应商评分详情\r\n            this.resetRatingStateMap(); // 重置评分状态\r\n            if (factor) {\r\n              for (const item of factor) {\r\n                this.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark; // 设置评分原因\r\n                this.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult; // 设置评分结果\r\n              }\r\n            }\r\n          } else {\r\n            this.$message.warning(detailRes.msg); // 评分详情获取失败\r\n          }\r\n        } else {\r\n          console.error(\"获取评分详情失败:\", detailResult.reason);\r\n          this.$message.error(\"获取评分详情失败\"); // 评分详情请求异常\r\n        }\r\n\r\n        // 处理系统初验请求结果\r\n        if (checkResult.status === 'fulfilled') {\r\n          const checkRes = checkResult.value;\r\n          if (checkRes.code === 200) {\r\n            this.checkResult = checkRes.data; // 设置初验结果\r\n          } else {\r\n            console.error(\"获取系统初验结果失败:\", checkRes.msg);\r\n            this.$message.warning(\"获取系统初验结果失败\"); // 系统初验获取失败\r\n          }\r\n        } else {\r\n          console.error(\"系统初验请求失败:\", checkResult.reason);\r\n          this.$message.error(\"系统初验请求失败\"); // 系统初验请求异常\r\n        }\r\n      } catch (e) {\r\n        console.error(\"请求处理异常:\", e);\r\n        this.$message.error(\"获取供应商详情失败\"); // 异常提示\r\n      }\r\n      // 默认显示响应文件\r\n      this.showResponseFile();\r\n    },\r\n    /**\r\n     * 显示响应文件PDF\r\n     */\r\n    showResponseFile() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'response'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowProcurement = false; // 不显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n      this.isPdfRendering = true; // 开始渲染PDF\r\n    },\r\n    /**\r\n     * 文件对比（双文件模式）\r\n     */\r\n    showFileContrast() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'contrast'; // 设置当前激活按钮\r\n      this.isDoubleView = true; // 双文件模式\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n      this.isPdfRendering = true; // 开始渲染PDF\r\n    },\r\n    /**\r\n     * 点击评分项名称，跳转到对应PDF页码\r\n     * @param {Object} factorItem 当前评分因子项\r\n     */\r\n    handleShowFactorInfo(factorItem) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectedFactorNode = factorItem; // 设置当前选中因子\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.isShowProcurement && !this.isShowResponse) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (factorItem.jumpToPage) {\r\n          this.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (!this.supplierFactorPage || Object.keys(this.supplierFactorPage).length === 0) {\r\n        this.$message.warning(\"请先选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n\r\n      // 跳转到响应文件对应页码\r\n      if (this.isShowResponse && this.$refs.response) {\r\n\t      if (!this.responsePdfRendered) {\r\n\t\t      this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n        this.$refs.response.skipPage(this.supplierFactorPage[this.selectedFactorNode.itemName]); // 响应文件跳页\r\n      }\r\n\r\n      // 跳转到采购文件对应页码\r\n      if (this.isShowProcurement && this.$refs.procurement) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n        if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        } else {\r\n          // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n          // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n        }\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.isShowProcurement && !this.isShowResponse) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.isShowResponse && !this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.isShowResponse && this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t   if (pdfType === 'response') {\r\n\t    this.responsePdfRendered = isRendered;\r\n\t   } else if (pdfType === 'procurement') {\r\n\t    this.procurementPdfRendered = isRendered;\r\n\t   }\r\n\t   \r\n\t   if (isRendered) {\r\n\t    console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t   }\r\n\r\n\t     // 检查所有显示的PDF是否都渲染完成\r\n\t     let allRendered = true;\r\n\t     if (this.isShowResponse && !this.responsePdfRendered) {\r\n\t       allRendered = false;\r\n\t     }\r\n\t     if (this.isShowProcurement && !this.procurementPdfRendered) {\r\n\t       allRendered = false;\r\n\t     }\r\n\t     \r\n\t     if (allRendered) {\r\n\t       this.isPdfRendering = false; // 所有PDF渲染完成，解除禁用状态\r\n\t     }\r\n\t  },\r\n\t  \r\n    /**\r\n     * 提交评分并修改专家进度\r\n     */\r\n    async submit() {\r\n      // 先保存评分，如果保存失败则不继续提交\r\n      const saveResult = await this.saveTempRating();\r\n      if (!saveResult) {\r\n        // 保存失败，不继续提交流程\r\n        return;\r\n      }\r\n\r\n      const data = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n      };\r\n      try {\r\n        const response = await checkReviewSummary(data); // 检查评审汇总\r\n\r\n        if (response.code === 200) {\r\n          // 修改专家进度\r\n          const status = {\r\n            evalExpertScoreInfoId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\")).evalExpertScoreInfoId, // 专家评分信息ID\r\n            evalState: 1, // 进度状态\r\n          };\r\n          const res = await editEvalExpertScoreInfo(status); // 编辑专家评分状态\r\n          if (res.code === 200) {\r\n            this.$message.success(\"提交成功\"); // 提交成功提示\r\n          }\r\n          this.$emit(\"send\", \"two\"); // 发送事件\r\n        } else {\r\n          this.$message.warning(response.msg); // 提交失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"提交失败\"); // 异常提示\r\n      }\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowResponse = false; // 不显示响应文件\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      this.isPdfRendering = true; // 开始渲染PDF\r\n      \r\n      // 设置采购文件PDF地址\r\n      if (this.projectFiles.tenderNoticeFilePath) {\r\n        this.procurementPdfUrl = this.projectFiles.tenderNoticeFilePath;\r\n      }\r\n      \r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringMethod.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringMethod.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringMethod.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringMethod.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n    /**\r\n     * 跳转到二次报价页面\r\n     */\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 二次报价评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 跳转到询标页面\r\n     */\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 询标评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/bidInquiry\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 获取因素对应页码（从本地缓存）\r\n     */\r\n    getFactorsPage() {\r\n      this.factorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\")); // 获取因子页码映射\r\n    },\r\n    /**\r\n     * 初始化专家和本地数据，只在mounted时调用一次\r\n     */\r\n    initLocalData() {\r\n      try {\r\n        this.localExpertInfo = JSON.parse(localStorage.getItem(\"expertInfo\") || \"{}\"); // 获取本地专家信息\r\n        this.localEntDocResponsePage = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地响应文件页码\r\n        this.localFactorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地因子页码映射\r\n        this.expertInfo = this.localExpertInfo; // 设置专家信息\r\n        this.entDocResponsePage = this.localEntDocResponsePage; // 设置响应文件页码\r\n        this.factorsPageMap = this.localFactorsPageMap; // 设置因子页码映射\r\n        console.log(\"本地数据已初始化\", { expertInfo: this.expertInfo });\r\n      } catch (error) {\r\n        console.error(\"初始化本地数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息（用于响应专家信息更新）\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const expertInfoStr = localStorage.getItem(\"expertInfo\");\r\n        if (expertInfoStr) {\r\n          this.localExpertInfo = JSON.parse(expertInfoStr);\r\n          this.expertInfo = this.localExpertInfo;\r\n          console.log(\"专家信息已刷新\", this.expertInfo);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"刷新专家信息失败:\", error);\r\n      }\r\n    },\r\n    /**\r\n     * 页面初始化，加载供应商、评分方法、文件等（并发请求）\r\n     */\r\n    async initPage() {\r\n      this.initLocalData(); // 初始化本地数据\r\n      try {\r\n        // 并发获取供应商、评分方法、文件\r\n        const [supplierRes, approvalRes, filesRes] = await Promise.all([\r\n          supplierInfo({ projectId: this.$route.query.projectId }), // 获取供应商\r\n          approvalProcess(this.$route.query.projectId, this.expertInfo.resultId), // 获取评分方法\r\n          filesById(this.$route.query.projectId) // 获取项目文件\r\n        ]);\r\n        // 处理供应商\r\n        if (supplierRes.code === 200) {\r\n          this.supplierOptions = supplierRes.rows.filter(item => item.isAbandonedBid == 0); // 过滤未弃标供应商\r\n        } else {\r\n          this.$message.warning(supplierRes.msg); // 获取失败提示\r\n        }\r\n        // 处理评分方法\r\n        if (approvalRes.code === 200) {\r\n\t        this.attachmentsList = approvalRes.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n          this.scoringMethod = approvalRes.data.scoringMethodUinfo.scoringMethodItems.find(\r\n            item => item.scoringMethodItemId == this.$route.query.scoringMethodItemId\r\n          ); // 获取当前评分方法\r\n          localStorage.setItem(\"evalProjectEvaluationProcess\", JSON.stringify(this.scoringMethod.evalProjectEvaluationProcess)); // 缓存评分流程\r\n          this.ratingStateMap = this.scoringMethod.uitems.reduce((acc, item) => {\r\n            acc[item.entMethodItemId] = { state: null, reason: '' }; // 初始化评分状态\r\n            return acc;\r\n          }, {});\r\n        } else {\r\n          this.$message.warning(approvalRes.msg); // 获取失败提示\r\n        }\r\n        // 处理文件\r\n        if (filesRes.code === 200) {\r\n          this.projectFiles = filesRes.data; // 设置项目文件\r\n          // 注释掉自动设置采购文件PDF，改为点击时才设置\r\n          // if (this.projectFiles.tenderNoticeFilePath) {\r\n          //   this.procurementPdfUrl = this.projectFiles.tenderNoticeFilePath; // 设置采购文件PDF\r\n          // }\r\n          // if (this.projectFiles.file) {\r\n          //   this.responsePdfUrl = this.projectFiles.file[0]; // 设置响应文件PDF\r\n          // }\r\n        } else {\r\n          this.$message.warning(filesRes.msg); // 获取失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"页面初始化失败\"); // 异常提示\r\n      }\r\n    },\r\n\t  \r\n\t  downloadFile(item){\r\n\t\t  this.$download.zip(item.filePath,item.fileName);\r\n\t  },\r\n\t  \r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n\t  this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\")); // 初始化采购文件页码信息\r\n    this.initPage(); // 页面挂载时初始化数据\r\n    this.getFactorsPage(); // 获取因子页码\r\n  },\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.compliance-main {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n.compliance-left {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n.compliance-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-title-group {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333;\r\n}\r\n.compliance-title {\r\n  // nothing extra\r\n}\r\n.compliance-step-img-group {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n.compliance-step-text {\r\n  font-size: 12px;\r\n}\r\n.compliance-step-img {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n.compliance-header-btns {\r\n  text-align: right;\r\n}\r\n.compliance-header-btns-bottom {\r\n  margin-top: 20px;\r\n}\r\n.compliance-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.compliance-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.compliance-pdf-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  height: 82%;\r\n}\r\n.compliance-pdf {\r\n  width: 49%;\r\n}\r\n.compliance-pdf-border-right {\r\n  border-right: 1px solid #176ADB;\r\n}\r\n.compliance-pdf-border-left {\r\n  border-left: 1px solid #176ADB;\r\n}\r\n.compliance-divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n.compliance-right {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n.compliance-select-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-select {\r\n  width: 100%;\r\n}\r\n\r\n// 禁用状态的下拉框样式\r\n::v-deep .el-select {\r\n  &.is-disabled {\r\n    .el-input__inner {\r\n      background-color: #f5f5f5;\r\n      border-color: #e4e7ed;\r\n      color: #c0c4cc;\r\n      cursor: not-allowed;\r\n    }\r\n  }\r\n}\r\n.compliance-factors-group {\r\n  padding: 15px 20px;\r\n}\r\n.compliance-factor-title-group {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n.compliance-factor-title {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #333;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-factor-radio-group {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n}\r\n.compliance-factor-divider {\r\n  height: 1px;\r\n  background-color: #DCDFE6;\r\n  margin-top: 10px;\r\n}\r\n.compliance-submit-group {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n.compliance-submit-btn {\r\n  background-color: #176ADB;\r\n}\r\n.compliance-review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n.compliance-review-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-review-html {\r\n  padding: 6px 30px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333;\r\n  &:hover {\r\n    color: #333;\r\n  }\r\n  \r\n  // 禁用状态样式\r\n  &[disabled] {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    background-color: #f5f5f5 !important;\r\n    color: #c0c4cc !important;\r\n    border-color: #e4e7ed !important;\r\n  }\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\t.fileItem {\r\n\t\ttransition: all 0.3s ease;\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\t\t\r\n\t\t::v-deep .el-card__body {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoPA,IAAAA,OAAA,GAAAC,OAAA;AAOA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,aAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAfA;AACA,IAAAI,IAAA;AACA,IAAAC,IAAA;AACA,IAAAC,UAAA;AACA,IAAAC,UAAA;;AAQA;AACA;AACA;AACA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA;MAAA;MACAC,aAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,UAAA;MAAA;MACAC,cAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,YAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,cAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,cAAA;MAAA;MACAC,iBAAA;MAAA;;MAEA;MACAC,YAAA;MAAA;;MAEAC,qBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MAAA;;MAEA;MACAC,mBAAA;MAAA;MACAC,sBAAA;MAAA;;MAEAC,WAAA;MAAA;;MAEA;MACAC,cAAA;MAAA;MACA;MACAC,aAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC,WAAA;MAAA;MACA;MACAC,kBAAA;QACA,2BAAAjC,UAAA;QACA,UAAAA,UAAA;QACA,QAAAA,UAAA;QACA,SAAAA,UAAA;QACA,QAAAA,UAAA;QACA,QAAAA,UAAA;QACA,QAAAA,UAAA;QACA,QAAAA;MACA;MACA;MACAkC,eAAA;MAAA;MACAC,uBAAA;MAAA;MACAC,mBAAA;MAAA;;MAEA;MACAC,iBAAA;MAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA;IACA;AACA;AACA;AACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAtC,OAAA,EACA,KAAAG,aAAA,CAAAoC,MAAA;QAAAC,KAAA;MAAA;QAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAK,KAAA;UAAA;UACA,IAAAC,KAAA,QAAAtC,cAAA,CAAAoC,IAAA,CAAAG,eAAA,EAAAD,KAAA;UACA,IAAAE,MAAA,QAAAxC,cAAA,CAAAoC,IAAA,CAAAG,eAAA,EAAAC,MAAA;UACA;UACA,IAAAF,KAAA,aAAAA,KAAA;YACA;YACA;UACA;UACA;UACA,IAAAA,KAAA,KAAAnD,IAAA,MAAAqD,MAAA,IAAAA,MAAA,CAAAC,IAAA;YACA;YACA,KAAAzC,cAAA,CAAAoC,IAAA,CAAAG,eAAA,EAAAD,KAAA;YACA,KAAAtC,cAAA,CAAAoC,IAAA,CAAAG,eAAA,EAAAC,MAAA;YACAE,OAAA,CAAAC,GAAA,IAAAC,MAAA,CAAAR,IAAA,CAAAS,QAAA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAjB,SAAA,CAAAkB,CAAA,CAAAD,GAAA;MAAA;QAAAjB,SAAA,CAAAmB,CAAA;MAAA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC,mBAAA,WAAAA,oBAAAC,UAAA;MACA,UAAA9B,WAAA,IAAA+B,MAAA,CAAAC,IAAA,MAAAhC,WAAA,EAAAiC,MAAA;MACA,IAAAC,IAAA,QAAAnC,aAAA,CAAA+B,UAAA;MACA,IAAAK,KAAA,GAAArE,IAAA;MACA,IAAAoE,IAAA;QACAC,KAAA,QAAAnC,WAAA,CAAAkC,IAAA;QACA;QACA,IAAAJ,UAAA,eAAAK,KAAA,KAAArE,IAAA;UACAqE,KAAA,QAAAnC,WAAA;QACA;MACA;MACA;MACA,IAAAmC,KAAA,KAAApE,IAAA;QACA,KAAAkC,kBAAA,CAAA6B,UAAA,IAAA7D,UAAA;MACA;QACAkE,KAAA,GAAArE,IAAA;QACA,KAAAmC,kBAAA,CAAA6B,UAAA,IAAA9D,UAAA;MACA;MACA,OAAAmE,KAAA;IACA;IACA;AACA;AACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,UAAA7D,aAAA;MACA,SAAA8D,EAAA,MAAAC,YAAA,GAAAP,MAAA,CAAAC,IAAA,MAAApD,cAAA,GAAAyD,EAAA,GAAAC,YAAA,CAAAL,MAAA,EAAAI,EAAA;QAAA,IAAAE,GAAA,GAAAD,YAAA,CAAAD,EAAA;QAAA;QACA,KAAAzD,cAAA,CAAA2D,GAAA,EAAArB,KAAA;QACA,KAAAtC,cAAA,CAAA2D,GAAA,EAAAnB,MAAA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAoB,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAtE,OAAA,mBAAAuE,aAAA,CAAAvE,OAAA,IAAAwE,CAAA,UAAAC,QAAA;QAAA,IAAAxE,IAAA,EAAAyE,QAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAvE,OAAA,IAAA4E,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAnC,CAAA;YAAA;cAAA,IACA2B,KAAA,CAAAjC,eAAA;gBAAAyC,QAAA,CAAAnC,CAAA;gBAAA;cAAA;cAAA,OAAAmC,QAAA,CAAAC,CAAA;YAAA;cAAA;cACA;cACA7E,IAAA,GAAAoE,KAAA,CAAAlE,aAAA,CAAAoC,MAAA,CAAAwC,GAAA,WAAAnC,IAAA;gBACA,IAAAoC,MAAA,GAAApC,IAAA,CAAAG,eAAA;gBACA;kBACAkC,oBAAA,EAAAD,MAAA;kBAAA;kBACAE,cAAA,EAAAb,KAAA,CAAA9D,UAAA,CAAA4E,QAAA;kBAAA;kBACAC,KAAA,EAAAf,KAAA,CAAA/D,gBAAA,CAAA+E,QAAA;kBAAA;kBACAC,gBAAA,EAAAjB,KAAA,CAAA7D,cAAA,CAAAwE,MAAA,EAAAlC,KAAA;kBAAA;kBACAyC,gBAAA,EAAAlB,KAAA,CAAA7D,cAAA,CAAAwE,MAAA,EAAAhC,MAAA;gBACA;cACA,GAAAwC,MAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAH,gBAAA,aAAAG,CAAA,CAAAH,gBAAA;cAAA;cAAA,MACArF,IAAA,CAAA4D,MAAA;gBAAAgB,QAAA,CAAAnC,CAAA;gBAAA;cAAA;cAAAmC,QAAA,CAAAa,CAAA;cAAAb,QAAA,CAAAnC,CAAA;cAAA,OAEA,IAAAiD,sBAAA,EAAA1F,IAAA;YAAA;cAAAyE,QAAA,GAAAG,QAAA,CAAAe,CAAA;cAAA,MACAlB,QAAA,CAAAZ,IAAA;gBAAAe,QAAA,CAAAnC,CAAA;gBAAA;cAAA;cACA2B,KAAA,CAAAwB,QAAA,CAAAC,OAAA;cAAA,OAAAjB,QAAA,CAAAC,CAAA,IACA;YAAA;cAEAT,KAAA,CAAAwB,QAAA,CAAAE,OAAA,CAAArB,QAAA,CAAAsB,GAAA;cAAA,OAAAnB,QAAA,CAAAC,CAAA,IACA;YAAA;cAAAD,QAAA,CAAAnC,CAAA;cAAA;YAAA;cAAAmC,QAAA,CAAAa,CAAA;cAAAf,EAAA,GAAAE,QAAA,CAAAe,CAAA;cAGAvB,KAAA,CAAAwB,QAAA,CAAAI,KAAA;cAAA,OAAApB,QAAA,CAAAC,CAAA,IACA;YAAA;cAAA,OAAAD,QAAA,CAAAC,CAAA,IAGA;UAAA;QAAA,GAAAL,OAAA;MAAA;IACA;IACA;AACA;AACA;AACA;IACAyB,oBAAA,WAAAA,qBAAAC,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9B,kBAAA,CAAAtE,OAAA,mBAAAuE,aAAA,CAAAvE,OAAA,IAAAwE,CAAA,UAAA6B,SAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAA5E,WAAA,EAAA6E,SAAA,EAAAC,qBAAA,EAAAC,MAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAjE,IAAA,EAAAkE,QAAA,EAAAC,GAAA;QAAA,WAAAxC,aAAA,CAAAvE,OAAA,IAAA4E,CAAA,WAAAoC,SAAA;UAAA,kBAAAA,SAAA,CAAAtE,CAAA;YAAA;cAAA,MAEAiB,MAAA,CAAAC,IAAA,CAAAwC,MAAA,CAAA9F,gBAAA,EAAAuD,MAAA;gBAAAmD,SAAA,CAAAtE,CAAA;gBAAA;cAAA;cAAAsE,SAAA,CAAAtE,CAAA;cAAA,OACA0D,MAAA,CAAAhC,cAAA;YAAA;cAEA;cACAgC,MAAA,CAAA9F,gBAAA,GAAA8F,MAAA,CAAAlG,eAAA,CAAA+G,IAAA,WAAArE,IAAA;gBAAA,OAAAA,IAAA,CAAAsE,UAAA,KAAAf,YAAA;cAAA;cACA;cACAC,MAAA,CAAApF,kBAAA,GAAAoF,MAAA,CAAArF,cAAA,CAAAqF,MAAA,CAAA9F,gBAAA,CAAA+E,QAAA;cACA;cACA;cAAA2B,SAAA,CAAAtB,CAAA;cAAAsB,SAAA,CAAAtE,CAAA;cAAA,OAEAyE,OAAA,CAAAC,UAAA,EACA,IAAAC,uBAAA;gBACAnC,cAAA,EAAAkB,MAAA,CAAA7F,UAAA,CAAA4E,QAAA;gBAAA;gBACAmC,SAAA,EAAAlB,MAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAAF,SAAA;gBAAA;gBACAG,mBAAA,EAAArB,MAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAAC,mBAAA;cACA,IACA,IAAAC,oCAAA;gBACAJ,SAAA,EAAAlB,MAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAAF,SAAA;gBAAA;gBACAlC,KAAA,EAAAgB,MAAA,CAAA9F,gBAAA,CAAA+E,QAAA;cACA,GACA;YAAA;cAAAiB,qBAAA,GAAAU,SAAA,CAAApB,CAAA;cAAAW,sBAAA,OAAAoB,eAAA,CAAA3H,OAAA,EAAAsG,qBAAA;cAVAE,YAAA,GAAAD,sBAAA;cAAA3E,WAAA,GAAA2E,sBAAA;cAYA;cACA,IAAAC,YAAA,CAAAoB,MAAA;gBACAnB,SAAA,GAAAD,YAAA,CAAA3D,KAAA;gBACA,IAAA4D,SAAA,CAAA3C,IAAA;kBACAsC,MAAA,CAAAvF,gBAAA,GAAA4F,SAAA,CAAAxG,IAAA;kBACA0G,MAAA,IAAAD,qBAAA,GAAAN,MAAA,CAAAvF,gBAAA,CAAAoG,IAAA,WAAArE,IAAA;oBAAA,OAAAA,IAAA,CAAAsE,UAAA,KAAAf,YAAA;kBAAA,gBAAAO,qBAAA,uBAAAA,qBAAA,CAAAmB,2BAAA;kBACAzB,MAAA,CAAApC,mBAAA;kBACA,IAAA2C,MAAA;oBAAAC,UAAA,OAAAtE,2BAAA,CAAAtC,OAAA,EACA2G,MAAA;oBAAA;sBAAA,KAAAC,UAAA,CAAAnE,CAAA,MAAAoE,MAAA,GAAAD,UAAA,CAAAlE,CAAA,IAAAC,IAAA;wBAAAC,IAAA,GAAAiE,MAAA,CAAAhE,KAAA;wBACAuD,MAAA,CAAA5F,cAAA,CAAAoC,IAAA,CAAAqC,oBAAA,EAAAjC,MAAA,GAAAJ,IAAA,CAAA2C,gBAAA;wBACAa,MAAA,CAAA5F,cAAA,CAAAoC,IAAA,CAAAqC,oBAAA,EAAAnC,KAAA,GAAAF,IAAA,CAAA0C,gBAAA;sBACA;oBAAA,SAAAhC,GAAA;sBAAAsD,UAAA,CAAArD,CAAA,CAAAD,GAAA;oBAAA;sBAAAsD,UAAA,CAAApD,CAAA;oBAAA;kBACA;gBACA;kBACA4C,MAAA,CAAAP,QAAA,CAAAE,OAAA,CAAAU,SAAA,CAAAT,GAAA;gBACA;cACA;gBACA9C,OAAA,CAAA+C,KAAA,cAAAO,YAAA,CAAAxD,MAAA;gBACAoD,MAAA,CAAAP,QAAA,CAAAI,KAAA;cACA;;cAEA;cACA,IAAArE,WAAA,CAAAgG,MAAA;gBACAd,QAAA,GAAAlF,WAAA,CAAAiB,KAAA;gBACA,IAAAiE,QAAA,CAAAhD,IAAA;kBACAsC,MAAA,CAAAxE,WAAA,GAAAkF,QAAA,CAAA7G,IAAA;gBACA;kBACAiD,OAAA,CAAA+C,KAAA,gBAAAa,QAAA,CAAAd,GAAA;kBACAI,MAAA,CAAAP,QAAA,CAAAE,OAAA;gBACA;cACA;gBACA7C,OAAA,CAAA+C,KAAA,cAAArE,WAAA,CAAAoB,MAAA;gBACAoD,MAAA,CAAAP,QAAA,CAAAI,KAAA;cACA;cAAAe,SAAA,CAAAtE,CAAA;cAAA;YAAA;cAAAsE,SAAA,CAAAtB,CAAA;cAAAqB,GAAA,GAAAC,SAAA,CAAApB,CAAA;cAEA1C,OAAA,CAAA+C,KAAA,YAAAc,GAAA;cACAX,MAAA,CAAAP,QAAA,CAAAI,KAAA;YAAA;cAEA;cACAG,MAAA,CAAA0B,gBAAA;YAAA;cAAA,OAAAd,SAAA,CAAAlC,CAAA;UAAA;QAAA,GAAAuB,QAAA;MAAA;IACA;IACA;AACA;AACA;IACAyB,gBAAA,WAAAA,iBAAA;MACA,UAAAxH,gBAAA,IAAAqD,MAAA,CAAAC,IAAA,MAAAtD,gBAAA,EAAAuD,MAAA;QACA,KAAAgC,QAAA,CAAAE,OAAA;QACA;MACA;MACA,KAAA5E,YAAA;MACA,KAAAP,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAD,cAAA;MACA,KAAAO,cAAA,QAAAR,YAAA,CAAAsH,IAAA,MAAAzH,gBAAA,CAAA+E,QAAA;MACA,KAAA3D,cAAA;IACA;IACA;AACA;AACA;IACAsG,gBAAA,WAAAA,iBAAA;MACA,UAAA1H,gBAAA,IAAAqD,MAAA,CAAAC,IAAA,MAAAtD,gBAAA,EAAAuD,MAAA;QACA,KAAAgC,QAAA,CAAAE,OAAA;QACA;MACA;MACA,KAAA5E,YAAA;MACA,KAAAP,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAD,cAAA;MACA,KAAAO,cAAA,QAAAR,YAAA,CAAAsH,IAAA,MAAAzH,gBAAA,CAAA+E,QAAA;MACA,KAAA3D,cAAA;IACA;IACA;AACA;AACA;AACA;IACAuG,oBAAA,WAAAA,qBAAAC,UAAA;MACA;MACA,UAAAC,aAAA;QACA,KAAAtC,QAAA,CAAAE,OAAA;QACA;MACA;MAEA,KAAA3F,kBAAA,GAAA8H,UAAA;;MAEA;MACA,SAAAvH,iBAAA,UAAAD,cAAA;QACA,UAAAc,sBAAA;UACA,KAAAqE,QAAA,CAAAE,OAAA;UACA;QACA;QAEA,IAAAmC,UAAA,CAAAE,UAAA;UACA,KAAAC,KAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAL,UAAA,CAAAE,UAAA;QACA,gBAAAhH,qBAAA,SAAAA,qBAAA,CAAA8G,UAAA,CAAA7E,QAAA;UACA,KAAAgF,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAAnH,qBAAA,CAAA8G,UAAA,CAAA7E,QAAA;QACA;QACA;MACA;;MAEA;MACA,UAAArC,kBAAA,IAAA2C,MAAA,CAAAC,IAAA,MAAA5C,kBAAA,EAAA6C,MAAA;QACA,KAAAgC,QAAA,CAAAE,OAAA;QACA;MACA;;MAEA;MACA,SAAArF,cAAA,SAAA2H,KAAA,CAAA3D,QAAA;QACA,UAAAnD,mBAAA;UACA,KAAAsE,QAAA,CAAAE,OAAA;UACA;QACA;QACA,KAAAsC,KAAA,CAAA3D,QAAA,CAAA6D,QAAA,MAAAvH,kBAAA,MAAAZ,kBAAA,CAAAiD,QAAA;MACA;;MAEA;MACA,SAAA1C,iBAAA,SAAA0H,KAAA,CAAAC,WAAA;QACA,UAAA9G,sBAAA;UACA,KAAAqE,QAAA,CAAAE,OAAA;UACA;QACA;;QAEA;QACA,SAAA3E,qBAAA,SAAAA,qBAAA,CAAA8G,UAAA,CAAA7E,QAAA;UACA,KAAAgF,KAAA,CAAAC,WAAA,CAAAC,QAAA,MAAAnH,qBAAA,CAAA8G,UAAA,CAAA7E,QAAA;QACA;UACA;UACA;QAAA;MAEA;IACA;IAEA;AACA;AACA;AACA;IACA8E,aAAA,WAAAA,cAAA;MACA;MACA,SAAAxH,iBAAA,UAAAD,cAAA;QACA,YAAAc,sBAAA;MACA;MACA;MACA,SAAAd,cAAA,UAAAC,iBAAA;QACA,YAAAY,mBAAA;MACA;MACA;MACA,SAAAb,cAAA,SAAAC,iBAAA;QACA,YAAAY,mBAAA,SAAAC,sBAAA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAgH,2BAAA,WAAAA,4BAAAC,UAAA,EAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAnH,mBAAA,GAAAkH,UAAA;MACA,WAAAC,OAAA;QACA,KAAAlH,sBAAA,GAAAiH,UAAA;MACA;MAEA,IAAAA,UAAA;QACAvF,OAAA,CAAAC,GAAA,IAAAC,MAAA,CAAAsF,OAAA;MACA;;MAEA;MACA,IAAAC,WAAA;MACA,SAAAjI,cAAA,UAAAa,mBAAA;QACAoH,WAAA;MACA;MACA,SAAAhI,iBAAA,UAAAa,sBAAA;QACAmH,WAAA;MACA;MAEA,IAAAA,WAAA;QACA,KAAAjH,cAAA;MACA;IACA;IAEA;AACA;AACA;IACAkH,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MAAA,WAAAvE,kBAAA,CAAAtE,OAAA,mBAAAuE,aAAA,CAAAvE,OAAA,IAAAwE,CAAA,UAAAsE,SAAA;QAAA,IAAAC,UAAA,EAAA9I,IAAA,EAAAyE,QAAA,EAAAkD,MAAA,EAAAoB,GAAA,EAAAC,GAAA;QAAA,WAAA1E,aAAA,CAAAvE,OAAA,IAAA4E,CAAA,WAAAsE,SAAA;UAAA,kBAAAA,SAAA,CAAAxG,CAAA;YAAA;cAAAwG,SAAA,CAAAxG,CAAA;cAAA,OAEAmG,MAAA,CAAAzE,cAAA;YAAA;cAAA2E,UAAA,GAAAG,SAAA,CAAAtD,CAAA;cAAA,IACAmD,UAAA;gBAAAG,SAAA,CAAAxG,CAAA;gBAAA;cAAA;cAAA,OAAAwG,SAAA,CAAApE,CAAA;YAAA;cAKA7E,IAAA;gBACAqH,SAAA,EAAAuB,MAAA,CAAAtB,MAAA,CAAAC,KAAA,CAAAF,SAAA;gBAAA;gBACApC,cAAA,EAAA2D,MAAA,CAAAtI,UAAA,CAAA4E,QAAA;gBAAA;gBACAsC,mBAAA,EAAAoB,MAAA,CAAAtB,MAAA,CAAAC,KAAA,CAAAC,mBAAA;cACA;cAAAyB,SAAA,CAAAxD,CAAA;cAAAwD,SAAA,CAAAxG,CAAA;cAAA,OAEA,IAAAyG,0BAAA,EAAAlJ,IAAA;YAAA;cAAAyE,QAAA,GAAAwE,SAAA,CAAAtD,CAAA;cAAA,MAEAlB,QAAA,CAAAZ,IAAA;gBAAAoF,SAAA,CAAAxG,CAAA;gBAAA;cAAA;cACA;cACAkF,MAAA;gBACAwB,qBAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA,yBAAAJ,qBAAA;gBAAA;gBACAK,SAAA;cACA;cAAAP,SAAA,CAAAxG,CAAA;cAAA,OACA,IAAAgH,qCAAA,EAAA9B,MAAA;YAAA;cAAAoB,GAAA,GAAAE,SAAA,CAAAtD,CAAA;cAAA;cACA,IAAAoD,GAAA,CAAAlF,IAAA;gBACA+E,MAAA,CAAAhD,QAAA,CAAAC,OAAA;cACA;cACA+C,MAAA,CAAAc,KAAA;cAAAT,SAAA,CAAAxG,CAAA;cAAA;YAAA;cAEAmG,MAAA,CAAAhD,QAAA,CAAAE,OAAA,CAAArB,QAAA,CAAAsB,GAAA;YAAA;cAAAkD,SAAA,CAAAxG,CAAA;cAAA;YAAA;cAAAwG,SAAA,CAAAxD,CAAA;cAAAuD,GAAA,GAAAC,SAAA,CAAAtD,CAAA;cAGAiD,MAAA,CAAAhD,QAAA,CAAAI,KAAA;YAAA;cAAA,OAAAiD,SAAA,CAAApE,CAAA;UAAA;QAAA,GAAAgE,QAAA;MAAA;IAEA;IACA;AACA;AACA;IACAc,cAAA,WAAAA,eAAA;MACA,KAAAzI,YAAA;MACA,KAAAP,YAAA;MACA,KAAAF,cAAA;MACA,KAAAC,iBAAA;MACA,KAAAe,cAAA;;MAEA;MACA,SAAAjB,YAAA,CAAAoJ,oBAAA;QACA,KAAA3I,iBAAA,QAAAT,YAAA,CAAAoJ,oBAAA;MACA;;MAEA;MACA,IAAAC,kBAAA;;MAEA,SAAAlH,IAAA,SAAAxB,qBAAA;QACA0I,kBAAA,CAAAC,IAAA;UACA1G,QAAA,EAAAT,IAAA;UACAwF,UAAA,OAAAhH,qBAAA,CAAAwB,IAAA;QACA;MACA;MAEAM,OAAA,CAAAC,GAAA,MAAAhD,aAAA,CAAAoC,MAAA;MACAW,OAAA,CAAAC,GAAA,CAAA2G,kBAAA;MACA,KAAAzI,eAAA;MACA,SAAA2I,CAAA,MAAAA,CAAA,QAAA7J,aAAA,CAAAoC,MAAA,CAAAsB,MAAA,EAAAmG,CAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAH,kBAAA,CAAAjG,MAAA,EAAAoG,CAAA;UACA,SAAA9J,aAAA,CAAAoC,MAAA,CAAAyH,CAAA,EAAA3G,QAAA,IAAAyG,kBAAA,CAAAG,CAAA,EAAA5G,QAAA;YACA,KAAAhC,eAAA,CAAA0I,IAAA,KAAAG,cAAA,CAAAlK,OAAA,MAAAkK,cAAA,CAAAlK,OAAA,WAAAG,aAAA,CAAAoC,MAAA,CAAAyH,CAAA,IAAAF,kBAAA,CAAAG,CAAA;UACA;QACA;MACA;MACA/G,OAAA,CAAAC,GAAA,MAAA9B,eAAA;IACA;IACA;AACA;AACA;IACA8I,WAAA,WAAAA,YAAA;MACA,IAAA3C,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QAAA;QACA8C,IAAA,OAAA7C,MAAA,CAAAC,KAAA,CAAA4C,IAAA;QAAA;QACA3C,mBAAA,EAAA4B,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA;MACA,KAAAa,OAAA,CAAAN,IAAA;QAAAO,IAAA;QAAA9C,KAAA,EAAAA;MAAA;IACA;IACA;AACA;AACA;IACA+C,UAAA,WAAAA,WAAA;MACA,IAAA/C,KAAA;QACAF,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QAAA;QACA8C,IAAA,OAAA7C,MAAA,CAAAC,KAAA,CAAA4C,IAAA;QAAA;QACA3C,mBAAA,EAAA4B,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA;MACA,KAAAa,OAAA,CAAAN,IAAA;QAAAO,IAAA;QAAA9C,KAAA,EAAAA;MAAA;IACA;IACA;AACA;AACA;IACAgD,cAAA,WAAAA,eAAA;MACA,KAAAzJ,cAAA,GAAAsI,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;IACA;AACA;AACA;IACAiB,aAAA,WAAAA,cAAA;MACA;QACA,KAAA3I,eAAA,GAAAuH,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;QACA,KAAAzH,uBAAA,GAAAsH,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;QACA,KAAAxH,mBAAA,GAAAqH,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;QACA,KAAAjJ,UAAA,QAAAuB,eAAA;QACA,KAAAhB,kBAAA,QAAAiB,uBAAA;QACA,KAAAhB,cAAA,QAAAiB,mBAAA;QACAkB,OAAA,CAAAC,GAAA;UAAA5C,UAAA,OAAAA;QAAA;MACA,SAAA0F,KAAA;QACA/C,OAAA,CAAA+C,KAAA,eAAAA,KAAA;MACA;IACA;IAEA;AACA;AACA;IACAyE,cAAA,WAAAA,eAAA;MACA;QACA,IAAAC,aAAA,GAAApB,YAAA,CAAAC,OAAA;QACA,IAAAmB,aAAA;UACA,KAAA7I,eAAA,GAAAuH,IAAA,CAAAC,KAAA,CAAAqB,aAAA;UACA,KAAApK,UAAA,QAAAuB,eAAA;UACAoB,OAAA,CAAAC,GAAA,iBAAA5C,UAAA;QACA;MACA,SAAA0F,KAAA;QACA/C,OAAA,CAAA+C,KAAA,cAAAA,KAAA;MACA;IACA;IACA;AACA;AACA;IACA2E,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MAAA,WAAAvG,kBAAA,CAAAtE,OAAA,mBAAAuE,aAAA,CAAAvE,OAAA,IAAAwE,CAAA,UAAAsG,SAAA;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,QAAA,EAAAC,GAAA;QAAA,WAAA7G,aAAA,CAAAvE,OAAA,IAAA4E,CAAA,WAAAyG,SAAA;UAAA,kBAAAA,SAAA,CAAA3I,CAAA;YAAA;cACAmI,MAAA,CAAAJ,aAAA;cAAAY,SAAA,CAAA3F,CAAA;cAAA2F,SAAA,CAAA3I,CAAA;cAAA,OAGAyE,OAAA,CAAAmE,GAAA,EACA,IAAAC,oBAAA;gBAAAjE,SAAA,EAAAuD,MAAA,CAAAtD,MAAA,CAAAC,KAAA,CAAAF;cAAA;cAAA;cACA,IAAAkE,uBAAA,EAAAX,MAAA,CAAAtD,MAAA,CAAAC,KAAA,CAAAF,SAAA,EAAAuD,MAAA,CAAAtK,UAAA,CAAA4E,QAAA;cAAA;cACA,IAAAsG,iBAAA,EAAAZ,MAAA,CAAAtD,MAAA,CAAAC,KAAA,CAAAF,SAAA;cAAA,CACA;YAAA;cAAAyD,kBAAA,GAAAM,SAAA,CAAAzF,CAAA;cAAAoF,mBAAA,OAAArD,eAAA,CAAA3H,OAAA,EAAA+K,kBAAA;cAJAE,WAAA,GAAAD,mBAAA;cAAAE,WAAA,GAAAF,mBAAA;cAAAG,QAAA,GAAAH,mBAAA;cAKA;cACA,IAAAC,WAAA,CAAAnH,IAAA;gBACA+G,MAAA,CAAA3K,eAAA,GAAA+K,WAAA,CAAAS,IAAA,CAAAlG,MAAA,WAAA5C,IAAA;kBAAA,OAAAA,IAAA,CAAA+I,cAAA;gBAAA;cACA;gBACAd,MAAA,CAAAhF,QAAA,CAAAE,OAAA,CAAAkF,WAAA,CAAAjF,GAAA;cACA;cACA;cACA,IAAAkF,WAAA,CAAApH,IAAA;gBACA+G,MAAA,CAAAvJ,eAAA,GAAA4J,WAAA,CAAAjL,IAAA,CAAA2L,gBAAA,CAAAC,WAAA,CAAArG,MAAA,WAAA5C,IAAA;kBAAA,OAAAA,IAAA,CAAAkJ,QAAA;gBAAA;gBACAjB,MAAA,CAAA1K,aAAA,GAAA+K,WAAA,CAAAjL,IAAA,CAAA8L,kBAAA,CAAAC,kBAAA,CAAA/E,IAAA,CACA,UAAArE,IAAA;kBAAA,OAAAA,IAAA,CAAA6E,mBAAA,IAAAoD,MAAA,CAAAtD,MAAA,CAAAC,KAAA,CAAAC,mBAAA;gBAAA,CACA;gBACA8B,YAAA,CAAA0C,OAAA,iCAAA5C,IAAA,CAAA6C,SAAA,CAAArB,MAAA,CAAA1K,aAAA,CAAAgM,4BAAA;gBACAtB,MAAA,CAAArK,cAAA,GAAAqK,MAAA,CAAA1K,aAAA,CAAAoC,MAAA,CAAA6J,MAAA,WAAAC,GAAA,EAAAzJ,IAAA;kBACAyJ,GAAA,CAAAzJ,IAAA,CAAAG,eAAA;oBAAAD,KAAA;oBAAAE,MAAA;kBAAA;kBACA,OAAAqJ,GAAA;gBACA;cACA;gBACAxB,MAAA,CAAAhF,QAAA,CAAAE,OAAA,CAAAmF,WAAA,CAAAlF,GAAA;cACA;cACA;cACA,IAAAmF,QAAA,CAAArH,IAAA;gBACA+G,MAAA,CAAApK,YAAA,GAAA0K,QAAA,CAAAlL,IAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACA4K,MAAA,CAAAhF,QAAA,CAAAE,OAAA,CAAAoF,QAAA,CAAAnF,GAAA;cACA;cAAAqF,SAAA,CAAA3I,CAAA;cAAA;YAAA;cAAA2I,SAAA,CAAA3F,CAAA;cAAA0F,GAAA,GAAAC,SAAA,CAAAzF,CAAA;cAEAiF,MAAA,CAAAhF,QAAA,CAAAI,KAAA;YAAA;cAAA,OAAAoF,SAAA,CAAAvG,CAAA;UAAA;QAAA,GAAAgG,QAAA;MAAA;IAEA;IAEAwB,YAAA,WAAAA,aAAA1J,IAAA;MACA,KAAA2J,SAAA,CAAAC,GAAA,CAAA5J,IAAA,CAAA6J,QAAA,EAAA7J,IAAA,CAAA8J,QAAA;IACA;IAGA;IACA;AACA;AACA;AACA;IACAC,iBAAA,WAAAA,kBAAAzE,UAAA;MAAA,IAAA0E,MAAA;MACA,KAAA1E,UAAA,CAAA2E,UAAA;;MAEA;MACA,SAAA3K,YAAA;QACA4K,YAAA,MAAA5K,YAAA;MACA;;MAEA;MACA,KAAAA,YAAA,GAAA6K,UAAA;QACAH,MAAA,CAAA3K,iBAAA,GAAAiG,UAAA;MACA;IACA;IAEA;AACA;AACA;IACA8E,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA/K,YAAA;QACA4K,YAAA,MAAA5K,YAAA;QACA,KAAAA,YAAA;MACA;;MAEA;MACA6K,UAAA;QACAE,MAAA,CAAAhL,iBAAA;MACA;IACA;IAEA;AACA;AACA;IACAiL,iBAAA,WAAAA,kBAAA;MACA,SAAAhL,YAAA;QACA4K,YAAA,MAAA5K,YAAA;QACA,KAAAA,YAAA;MACA;IACA;EACA;EACAiL,OAAA,WAAAA,QAAA;IACA,KAAA/L,qBAAA,GAAAiI,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA,KAAAoB,QAAA;IACA,KAAAJ,cAAA;EACA;EACA4C,aAAA,WAAAA,cAAA;IACA;IACA,SAAAlL,YAAA;MACA4K,YAAA,MAAA5K,YAAA;MACA,KAAAA,YAAA;IACA;EACA;AACA", "ignoreList": []}]}