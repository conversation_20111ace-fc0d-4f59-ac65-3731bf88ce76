{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\components\\pdfView\\index-improved.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\components\\pdfView\\index-improved.vue", "mtime": 1753956960909}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\babel.config.js", "mtime": 1750995480034}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_pdfjsDist", "_interopRequireDefault", "require", "_axios", "baseURL", "process", "env", "VUE_APP_BASE_API", "_default", "exports", "default", "name", "props", "pdfurl", "type", "String", "uni_key", "pageHeight", "Number", "bufferSize", "data", "url", "pdfDocument", "totalPages", "curPage", "isLoading", "loadingProgress", "loadingDetail", "display_enlarge", "allPagesRendered", "pageCache", "Map", "renderedPages", "Set", "renderingPages", "maxCacheSize", "computed", "watch", "handler", "newVal", "undefined", "console", "log", "resetState", "loadPDF", "immediate", "methods", "handleResize", "container", "$el", "querySelector", "containerHeight", "clientHeight", "formatProgress", "percentage", "concat", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "useStreaming", "_t", "w", "_context", "n", "p", "$nextTick", "$refs", "pdfContainer", "scrollTop", "configurePdfWorker", "shouldUseStreaming", "v", "loadPDFWithStreaming", "loadPDFStandard", "error", "message", "a", "worker", "pdfjsLib", "GlobalWorkerOptions", "workerSrc", "e", "version", "_this2", "_callee2", "filePath", "response", "_t2", "_context2", "extractFilePath", "axios", "get", "params", "fileSize", "warn", "startsWith", "substring", "length", "extractFileName", "parts", "split", "_this3", "_callee3", "streamingUrl", "loadingTask", "_context3", "encodeURIComponent", "getDocument", "rangeChunkSize", "disableAutoFetch", "disableStream", "disable<PERSON><PERSON><PERSON>", "isEvalSupported", "useWorkerFetch", "maxImageSize", "cMapUrl", "cMapPacked", "httpHeaders", "onProgress", "progressData", "percent", "Math", "round", "loaded", "total", "handleProgress", "promise", "numPages", "setTimeout", "startStreamingOptimizedRendering", "_this4", "_callee4", "_context4", "disable<PERSON><PERSON><PERSON>", "startOptimizedRendering", "progress", "min", "_this5", "_callee5", "initialPages", "i", "_context5", "renderPage", "sleep", "scheduleLazyRendering", "_this6", "_callee6", "_context6", "scheduleStreamingLazyRendering", "_this7", "remainingPages", "push", "renderNextPage", "_ref", "_callee7", "pageNum", "_context7", "shift", "has", "apply", "arguments", "_this8", "_ref2", "_callee8", "nearbyPages", "_pageNum", "_context8", "checkAllPagesRendered", "getNearbyUnrenderedPages", "currentPage", "range", "max", "_this9", "clear", "$emit", "_retryTimer", "clearTimeout", "scrollTo", "handleScroll", "event", "target", "canvases", "querySelectorAll", "canvas", "rect", "getBoundingClientRect", "containerRect", "top", "bottom", "parseInt", "getAttribute", "renderAllPages", "_this0", "_callee9", "batchSize", "end", "promises", "_context9", "Promise", "all", "_arguments", "_this1", "_callee0", "isEnlarge", "cache<PERSON>ey", "page", "scale", "viewport", "context", "_t3", "_context0", "add", "renderCanvas", "getPage", "getViewport", "document", "createElement", "getContext", "height", "width", "render", "canvasContext", "set", "delete", "f", "sourceCanvas", "_this10", "refName", "clearRect", "drawImage", "size", "manageCacheSize", "_this11", "keys", "Array", "from", "toRemove", "slice", "for<PERSON>ach", "key", "skipPage", "_this12", "_callee1", "targetCanvas", "canvasSelector", "fallback<PERSON><PERSON><PERSON>", "targetTop", "_context1", "getElementById", "scrollIntoView", "behavior", "block", "inline", "enlarge", "_this13", "_callee10", "_context10", "body", "style", "overflow", "documentElement", "getEnlargeVisiblePages", "close", "ms", "resolve", "mounted", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener"], "sources": ["src/components/pdfView/index-improved.vue"], "sourcesContent": ["<!--\r\n  PDF文件查看器组件 - 增强版\r\n  功能：\r\n  1. 支持PDF文件的在线预览\r\n  2. 提供页面导航功能（上一页、下一页、首页）\r\n  3. 支持PDF页面放大查看\r\n  4. 自动计算当前页码\r\n  5. 虚拟滚动和懒加载，防止大文件浏览器卡死\r\n-->\r\n<template>\r\n\t<div>\r\n\t\t<!-- PDF文件加载进度条 -->\r\n\t\t<div v-if=\"isLoading\" class=\"pdf-loading-container\">\r\n\t\t\t<div class=\"loading-content\">\r\n\t\t\t\t<div class=\"loading-text\">正在加载PDF文件...</div>\r\n\t\t\t\t<el-progress\r\n\t\t\t\t\t:percentage=\"loadingProgress\"\r\n\t\t\t\t\t:stroke-width=\"8\"\r\n\t\t\t\t\t:show-text=\"true\"\r\n\t\t\t\t\t:format=\"formatProgress\"\r\n\t\t\t\t\tstatus=\"success\"\r\n\t\t\t\t\tclass=\"loading-progress\">\r\n\t\t\t\t</el-progress>\r\n\t\t\t\t<div class=\"loading-detail\">{{ loadingDetail }}</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t<!-- PDF主容器：固定高度600px，支持滚动查看多页PDF -->\r\n\t\t<div ref=\"pdfContainer\" id=\"pdf-container\" style=\"width:100%;overflow: auto;height: 600px;\" v-show=\"!isLoading\" @scroll=\"handleScroll\">\r\n\t\t\t<!-- 导航按钮栏：粘性定位，始终显示在顶部 -->\r\n\t\t\t<div style=\"position:sticky;top:0;z-index:99;background:#fff;padding:10px 0;border-bottom:1px solid #eee;\">\r\n\t\t\t\t<!-- 上一页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"skipPage(curPage - 1)\">上一页</el-button>\r\n\t\t\t\t<!-- 首页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"skipPage(1)\">首页</el-button>\r\n\t\t\t\t<!-- 下一页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"skipPage(curPage + 1)\">下一页</el-button>\r\n\t\t\t\t<!-- 放大当前页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"enlarge(curPage)\">放大</el-button>\r\n\t\t\t\t<!-- 显示当前页码 -->\r\n\t\t\t\t<span style=\"margin-left: 20px; color: #666;\">\r\n\t\t\t\t\t第 {{ curPage }}/{{ totalPages }} 页\r\n\t\t\t\t</span>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<!-- 直接渲染所有页面，取消虚拟滚动 -->\r\n\t\t\t<div style=\"padding: 10px;\">\r\n\t\t\t\t<div\r\n\t\t\t\t\tv-for=\"pageNum in totalPages\"\r\n\t\t\t\t\t:key=\"pageNum\"\r\n\t\t\t\t\tclass=\"pdf-page\"\r\n\t\t\t\t\tstyle=\"margin-bottom: 20px; text-align: center;\">\r\n\t\t\t\t\t<canvas\r\n\t\t\t\t\t\t:ref=\"`canvas-${pageNum}`\"\r\n\t\t\t\t\t\t:style=\"{ maxWidth: '100%', height: 'auto', border: '1px solid #ddd', borderRadius: '4px' }\"\r\n\t\t\t\t\t\t:data-page=\"pageNum\">\r\n\t\t\t\t\t</canvas>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t<!-- PDF放大查看模态框 -->\r\n\t\t<div v-show=\"display_enlarge\" class=\"canvas-enlarge\">\r\n\t\t\t<!-- 关闭按钮：位于右上角 -->\r\n\t\t\t<div style=\"position: absolute; top: 20px; right: 20px; z-index: 1000;\">\r\n\t\t\t\t<el-button type=\"danger\" icon=\"el-icon-close\" circle @click=\"close()\">\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<!-- 放大后的PDF渲染容器：宽度70%，高度56.25rem，支持滚动 -->\r\n\t\t\t<div id=\"enlarge\" style=\"width:70%;height:56.25rem;overflow: auto;\">\r\n\t\t\t\t<!-- 放大模式下的PDF页面渲染 -->\r\n\t\t\t\t<template v-if=\"totalPages > 0\">\r\n\t\t\t\t\t<!-- 遍历每一页PDF，为每页创建放大版本的canvas元素 -->\r\n\t\t\t\t\t<div v-for=\"pageNum in getEnlargeVisiblePages()\" :key=\"`enlarge-${pageNum}`\">\r\n\t\t\t\t\t\t<canvas\r\n\t\t\t\t\t\t\t:ref=\"`enlarge-canvas-${pageNum}`\"\r\n\t\t\t\t\t\t\t:style=\"{ width: '100%', marginBottom: '20px' }\"\r\n\t\t\t\t\t\t\t:data-page=\"pageNum\">\r\n\t\t\t\t\t\t</canvas>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</template>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n// 导入PDF.js库，用于PDF文件的解析和渲染\r\nimport pdfjsLib from \"pdfjs-dist\";\r\nimport axios from 'axios';\r\n// 获取API基础URL，用于拼接完整的PDF文件URL\r\nconst baseURL = process.env.VUE_APP_BASE_API;\r\n\r\nexport default {\r\n\t// 组件名称\r\n\tname: 'PdfViewImproved',\r\n\t\r\n\t// 组件属性定义\r\n\tprops: {\r\n\t\t// PDF文件URL，父组件传入的PDF文件路径\r\n\t\tpdfurl: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"\",\r\n\t\t},\r\n\t\t// 唯一标识符，用于区分多个PDF查看器实例，确保DOM元素ID不冲突\r\n\t\tuni_key: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"a\",\r\n\t\t},\r\n\t\t// 每页高度（像素），用于虚拟滚动计算\r\n\t\tpageHeight: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 800\r\n\t\t},\r\n\t\t// 缓冲区大小（页数）\r\n\t\tbufferSize: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 3\r\n\t\t}\r\n\t},\r\n\t\r\n\t// 组件数据\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 完整的PDF文件URL（包含baseURL）\r\n\t\t\turl: \"\",\r\n\t\t\t// PDF文档对象\r\n\t\t\tpdfDocument: null,\r\n\t\t\t// 总页数\r\n\t\t\ttotalPages: 0,\r\n\t\t\t// 当前页码\r\n\t\t\tcurPage: 1,\r\n\t\t\t// 是否正在加载PDF\r\n\t\t\tisLoading: false,\r\n\t\t\t// 加载进度百分比（0-100）\r\n\t\t\tloadingProgress: 0,\r\n\t\t\t// 加载详细信息\r\n\t\t\tloadingDetail: \"\",\r\n\t\t\t// 是否显示放大模式\r\n\t\t\tdisplay_enlarge: false,\r\n\t\t\t// 所有页面是否渲染完成\r\n\t\t\tallPagesRendered: false,\r\n\t\t\t\r\n\t\t\t// 页面缓存\r\n\t\t\tpageCache: new Map(),\r\n\t\t\trenderedPages: new Set(),\r\n\t\t\trenderingPages: new Set(),\r\n\t\t\t\r\n\t\t\t// 内存管理\r\n\t\t\tmaxCacheSize: 50, // 最大缓存页数\r\n\t\t};\r\n\t},\r\n\t\r\n\tcomputed: {\r\n\t\t// 移除虚拟滚动相关计算属性\r\n\t},\r\n\t\r\n\twatch: {\r\n\t\t// 监听PDF URL变化\r\n\t\tpdfurl: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\tif (newVal != null && newVal !== undefined) {\r\n\t\t\t\t\tthis.url = newVal;\r\n\t\t\t\t\tconsole.log(\"this.url\",this.url)\r\n\t\t\t\t\tif (this.url !== \"\") {\r\n\t\t\t\t\t\tthis.url = baseURL + this.url;\r\n\t\t\t\t\t\t// 每次切换PDF都重置状态并回到顶部\r\n\t\t\t\t\t\tthis.resetState();\r\n\t\t\t\t\t\tthis.loadPDF();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t\t// 移除visiblePages的watch监听器\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\t/**\r\n\t\t * 处理窗口大小变化\r\n\t\t */\r\n\t\thandleResize() {\r\n\t\t\tconst container = this.$el.querySelector(\"#pdf-container\");\r\n\t\t\tif (container) {\r\n\t\t\t\tthis.containerHeight = container.clientHeight;\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 格式化进度条显示文本\r\n\t\t */\r\n\t\tformatProgress(percentage) {\r\n\t\t\treturn `${percentage}%`;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 加载PDF文档 - 支持分片加载\r\n\t\t */\r\n\t\tasync loadPDF() {\r\n\t\t\ttry {\r\n\t\t\t\t// 立即滚动到顶部，在加载开始前\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tconst container = this.$refs.pdfContainer;\r\n\t\t\t\t\tif (container) {\r\n\t\t\t\t\t\tcontainer.scrollTop = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tthis.loadingProgress = 0;\r\n\t\t\t\tthis.loadingDetail = \"正在初始化PDF加载器...\";\r\n\t\t\t\t\r\n\t\t\t\t// 重置状态\r\n\t\t\t\tthis.resetState();\r\n\t\t\t\t\r\n\t\t\t\t// 配置PDF.js - 修复worker路径问题\r\n\t\t\t\tthis.configurePdfWorker();\r\n\t\t\t\t\r\n\t\t\t\t// 检测是否使用分片加载\r\n\t\t\t\tconst useStreaming = await this.shouldUseStreaming();\r\n\t\t\t\tconsole.log(\"检测是否使用分片加载\",useStreaming)\r\n\t\t\t\tif (useStreaming) {\r\n\t\t\t\t\tawait this.loadPDFWithStreaming();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tawait this.loadPDFStandard();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error loading PDF:\", error);\r\n\t\t\t\tthis.loadingDetail = \"PDF加载失败：\" + error.message;\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 配置PDF.js worker\r\n\t\t */\r\n\t\tconfigurePdfWorker() {\r\n\t\t\ttry {\r\n\t\t\t\tconst worker = require('pdfjs-dist/build/pdf.worker.min.js');\r\n\t\t\t\tpdfjsLib.GlobalWorkerOptions.workerSrc = worker;\r\n\t\t\t} catch (e) {\r\n\t\t\t\t// 如果require失败，使用CDN\r\n\t\t\t\tpdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 判断是否使用分片加载\r\n\t\t * 直接传入完整文件路径\r\n\t\t */\r\n\t\tasync shouldUseStreaming() {\r\n\t\t\ttry {\r\n\t\t\t\t// 从完整URL中提取文件路径部分\r\n\t\t\t\tconst filePath = this.extractFilePath(this.url);\r\n\t\t\t\tconst response = await axios.get(`${baseURL}/common/pdf/info`, {\r\n\t\t\t\t\tparams: { filePath }\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log(response)\r\n\t\t\t\t// 文件大于5MB时使用分片加载\r\n\t\t\t\treturn response.data.fileSize > 5 * 1024 * 1024;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.warn(\"无法获取文件信息，使用标准加载模式\", error);\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 提取文件路径（从完整URL中提取相对路径）\r\n\t\t */\r\n\t\textractFilePath(url) {\r\n\t\t\t// 从完整URL中提取相对路径部分\r\n\t\t\tconst baseURL = process.env.VUE_APP_BASE_API;\r\n\t\t\tif (url.startsWith(baseURL)) {\r\n\t\t\t\treturn url.substring(baseURL.length);\r\n\t\t\t}\r\n\t\t\t// 如果URL已经是相对路径，直接返回\r\n\t\t\treturn url;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 提取文件名（保留原有方法用于兼容）\r\n\t\t */\r\n\t\textractFileName(url) {\r\n\t\t\tconst parts = url.split('/');\r\n\t\t\treturn parts[parts.length - 1];\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 分片加载PDF - 真正的HTTP Range分片加载\r\n\t\t * 使用后端/stream接口，支持断点续传和按需加载\r\n\t\t */\r\n\t\tasync loadPDFWithStreaming() {\r\n\t\t\tthis.loadingProgress = 10;\r\n\t\t\tthis.loadingDetail = \"正在初始化分片加载模式...\";\r\n\t\t\t\r\n\t\t\tconst filePath = this.extractFilePath(this.url);\r\n\t\t\tconst streamingUrl = `${baseURL}/common/pdf/stream?filePath=${encodeURIComponent(filePath)}`;\r\n\t\t\t\r\n\t\t\tconsole.log(\"🚀 使用分片加载模式，URL:\", streamingUrl);\r\n\t\t\t\r\n\t\t\t// 分片加载专用配置 - 真正的Range分片\r\n\t\t\tconst loadingTask = pdfjsLib.getDocument({\r\n\t\t\t\turl: streamingUrl,\r\n\t\t\t\trangeChunkSize: 32 * 1024,    // 32KB 更小分片，网络容错更强\r\n\t\t\t\tdisableAutoFetch: true,       // 关键：禁止自动获取，只加载需要的部分\r\n\t\t\t\tdisableStream: false,         // 启用流式传输\r\n\t\t\t\tdisableRange: false,          // 关键：启用Range请求，支持断点续传\r\n\t\t\t\tisEvalSupported: false,       // 安全考虑\r\n\t\t\t\tuseWorkerFetch: true,         // 使用worker处理网络请求\r\n\t\t\t\tmaxImageSize: 10 * 1024 * 1024, // 限制图片大小，防止内存溢出\r\n\t\t\t\tcMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.16.105/cmaps/', // 字体映射\r\n\t\t\t\tcMapPacked: true,\r\n\t\t\t\thttpHeaders: {\r\n\t\t\t\t\t'Accept': 'application/pdf',\r\n\t\t\t\t\t'Cache-Control': 'no-cache',\r\n\t\t\t\t\t'Pragma': 'no-cache',\r\n\t\t\t\t},\r\n\t\t\t\tonProgress: (progressData) => {\r\n\t\t\t\t\t// 分片加载的进度更精确，按块显示\r\n\t\t\t\t\tconst percent = Math.round((progressData.loaded / progressData.total) * 100);\r\n\t\t\t\t\tthis.handleProgress(progressData);\r\n\t\t\t\t\tif (progressData.loaded < progressData.total) {\r\n\t\t\t\t\t\tthis.loadingDetail = `📦 分片下载中... ${percent}% (${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB)`;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.loadingDetail = `✅ 分片下载完成，正在解析...`;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.pdfDocument = await loadingTask.promise;\r\n\t\t\tthis.totalPages = this.pdfDocument.numPages;\r\n\t\t\t\r\n\t\t\tthis.loadingProgress = 100;\r\n\t\t\tthis.loadingDetail = `🎉 PDF分片加载完成！共${this.totalPages}页，内存占用优化`;\r\n\t\t\t\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t// 分片模式下使用按需加载策略\r\n\t\t\t\tthis.startStreamingOptimizedRendering();\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 标准加载模式 - 传统的完整文件下载\r\n\t\t * 直接下载整个PDF文件到内存，适合小文件\r\n\t\t */\r\n\t\tasync loadPDFStandard() {\r\n\t\t\tthis.loadingProgress = 10;\r\n\t\t\tthis.loadingDetail = \"正在标准加载模式...\";\r\n\t\t\t\r\n\t\t\tconsole.log(\"使用标准加载模式，URL:\", this.url);\r\n\t\t\t\r\n\t\t\t// 标准加载专用配置 - 一次性下载整个文件\r\n\t\t\tconst loadingTask = pdfjsLib.getDocument({\r\n\t\t\t\turl: this.url,\r\n\t\t\t\trangeChunkSize: 65536 * 16, // 256KB 大块，减少请求次数\r\n\t\t\t\tdisableAutoFetch: false,    // 允许自动获取\r\n\t\t\t\tdisableStream: true,       // 禁用流式，一次性加载\r\n\t\t\t\tdisableRange: true,        // 禁用Range请求，强制完整下载\r\n\t\t\t\tdisableWorker: false,      // 启用worker处理\r\n\t\t\t\tisEvalSupported: false,\r\n\t\t\t\tuseWorkerFetch: false,\r\n\t\t\t\thttpHeaders: {\r\n\t\t\t\t\t'Accept': 'application/pdf',\r\n\t\t\t\t},\r\n\t\t\t\tonProgress: (progressData) => {\r\n\t\t\t\t\t// 标准加载显示整体进度\r\n\t\t\t\t\tthis.handleProgress(progressData);\r\n\t\t\t\t\tthis.loadingDetail = `完整下载中... ${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB`;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.pdfDocument = await loadingTask.promise;\r\n\t\t\tthis.totalPages = this.pdfDocument.numPages;\r\n\t\t\t\r\n\t\t\tthis.loadingProgress = 100;\r\n\t\t\tthis.loadingDetail = `PDF标准加载完成！共${this.totalPages}页`;\r\n\t\t\t\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t// 标准模式下使用渐进式渲染\r\n\t\t\t\tthis.startOptimizedRendering();\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 处理加载进度\r\n\t\t */\r\n\t\thandleProgress(progressData) {\r\n\t\t\tif (progressData.total > 0) {\r\n\t\t\t\tconst progress = Math.round((progressData.loaded / progressData.total) * 80) + 10;\r\n\t\t\t\tthis.loadingProgress = Math.min(progress, 99);\r\n\t\t\t\tthis.loadingDetail = `正在下载... ${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB`;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 开始优化渲染（标准模式）\r\n\t\t */\r\n\t\tasync startOptimizedRendering() {\r\n\t\t\tif (!this.pdfDocument || this.totalPages === 0) return;\r\n\t\t\t\r\n\t\t\tconsole.log(`开始优化渲染 ${this.totalPages} 页PDF...`);\r\n\t\t\t\r\n\t\t\t// 分批渲染，避免阻塞UI\r\n\t\t\tconst initialPages = Math.min(5, this.totalPages);\r\n\t\t\tfor (let i = 1; i <= initialPages; i++) {\r\n\t\t\t\tawait this.renderPage(i);\r\n\t\t\t\tawait this.sleep(10);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 延迟加载剩余页面\r\n\t\t\tthis.scheduleLazyRendering();\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 分片模式下的优化渲染（按需加载策略）\r\n\t\t */\r\n\t\tasync startStreamingOptimizedRendering() {\r\n\t\t\tif (!this.pdfDocument || this.totalPages === 0) return;\r\n\t\t\t\r\n\t\t\tconsole.log(`开始分片模式优化渲染 ${this.totalPages} 页PDF...`);\r\n\t\t\t\r\n\t\t\t// 分片模式下使用更激进的按需加载策略\r\n\t\t\tconst initialPages = Math.min(3, this.totalPages);\r\n\t\t\tfor (let i = 1; i <= initialPages; i++) {\r\n\t\t\t\tawait this.renderPage(i);\r\n\t\t\t\tawait this.sleep(5);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 分片模式下使用延迟加载策略，减少初始内存占用\r\n\t\t\tthis.scheduleStreamingLazyRendering();\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 延迟加载剩余页面（标准模式）\r\n\t\t */\r\n\t\tscheduleLazyRendering() {\r\n\t\t\tconst remainingPages = [];\r\n\t\t\tfor (let i = 6; i <= this.totalPages; i++) {\r\n\t\t\t\tremainingPages.push(i);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst renderNextPage = async () => {\r\n\t\t\t\tif (remainingPages.length === 0) return;\r\n\t\t\t\t\r\n\t\t\t\tconst pageNum = remainingPages.shift();\r\n\t\t\t\tif (!this.renderedPages.has(pageNum)) {\r\n\t\t\t\t\tawait this.renderPage(pageNum);\r\n\t\t\t\t\tawait this.sleep(20);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 继续渲染下一页\r\n\t\t\t\tsetTimeout(renderNextPage, 100);\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 延迟开始渲染剩余页面\r\n\t\t\tsetTimeout(renderNextPage, 1000);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 分片模式下的延迟加载策略（更激进的按需加载）\r\n\t\t */\r\n\t\tscheduleStreamingLazyRendering() {\r\n\t\t\tconst remainingPages = [];\r\n\t\t\tfor (let i = 4; i <= this.totalPages; i++) {\r\n\t\t\t\tremainingPages.push(i);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst renderNextPage = async () => {\r\n\t\t\t\tif (remainingPages.length === 0) {\r\n\t\t\t\t\tthis.checkAllPagesRendered();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 分片模式下更智能的加载策略：优先加载用户附近页面\r\n\t\t\t\tconst nearbyPages = this.getNearbyUnrenderedPages();\r\n\t\t\t\tif (nearbyPages.length > 0) {\r\n\t\t\t\t\tconst pageNum = nearbyPages[0];\r\n\t\t\t\t\tif (!this.renderedPages.has(pageNum) && !this.renderingPages.has(pageNum)) {\r\n\t\t\t\t\t\tawait this.renderPage(pageNum);\r\n\t\t\t\t\t\tawait this.sleep(50);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (remainingPages.length > 0) {\r\n\t\t\t\t\t// 回退到顺序加载\r\n\t\t\t\t\tconst pageNum = remainingPages.shift();\r\n\t\t\t\t\tif (!this.renderedPages.has(pageNum) && !this.renderingPages.has(pageNum)) {\r\n\t\t\t\t\t\tawait this.renderPage(pageNum);\r\n\t\t\t\t\t\tawait this.sleep(30);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 继续渲染下一页，分片模式下间隔更短\r\n\t\t\t\tsetTimeout(renderNextPage, 200);\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 延迟开始渲染剩余页面，分片模式下延迟更短\r\n\t\t\tsetTimeout(renderNextPage, 300);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 获取当前页面附近的未渲染页面\r\n\t\t */\r\n\t\tgetNearbyUnrenderedPages() {\r\n\t\t\tconst nearbyPages = [];\r\n\t\t\tconst currentPage = this.curPage;\r\n\t\t\tconst range = 2; // 加载当前页前后2页\r\n\t\t\t\r\n\t\t\tfor (let i = Math.max(1, currentPage - range); i <= Math.min(this.totalPages, currentPage + range); i++) {\r\n\t\t\t\tif (!this.renderedPages.has(i) && !this.renderingPages.has(i)) {\r\n\t\t\t\t\tnearbyPages.push(i);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn nearbyPages;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 重置组件状态\r\n\t\t */\r\n\t\tresetState() {\r\n\t\t\tthis.totalPages = 0;\r\n\t\t\tthis.curPage = 1;\r\n\t\t\tthis.allPagesRendered = false; // 重置渲染完成状态\r\n\t\t\tthis.pageCache.clear();\r\n\t\t\tthis.renderedPages.clear();\r\n\t\t\tthis.renderingPages.clear();\r\n\t\t\tthis.loadingDetail = \"\"; // 清空加载详情\r\n\t\t\t\r\n\t\t\t// 通知父组件渲染状态变化\r\n\t\t\tthis.$emit('render-status-change', false);\r\n\t\t\t\r\n\t\t\t// 清理定时器\r\n\t\t\tif (this._retryTimer) {\r\n\t\t\t\tclearTimeout(this._retryTimer);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 关键：在重置状态时立即滚动到顶部\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst container = this.$refs.pdfContainer;\r\n\t\t\t\tif (container) {\r\n\t\t\t\t\tcontainer.scrollTop = 0;\r\n\t\t\t\t\tcontainer.scrollTo(0, 0);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t\r\n\t\t/**\r\n\t\t * 处理滚动事件\r\n\t\t */\r\n\t\thandleScroll(event) {\r\n\t\t\tconst container = event.target;\r\n\t\t\tconst canvases = container.querySelectorAll('canvas[data-page]');\r\n\t\t\t\r\n\t\t\tfor (let i = 0; i < canvases.length; i++) {\r\n\t\t\t\tconst canvas = canvases[i];\r\n\t\t\t\tconst rect = canvas.getBoundingClientRect();\r\n\t\t\t\tconst containerRect = container.getBoundingClientRect();\r\n\t\t\t\t\r\n\t\t\t\t// 如果canvas顶部在容器视口内\r\n\t\t\t\tif (rect.top >= containerRect.top && rect.top <= containerRect.bottom) {\r\n\t\t\t\t\tthis.curPage = parseInt(canvas.getAttribute('data-page'));\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t/**\r\n\t\t * 渲染所有页面（取消虚拟滚动，解决显示问题）\r\n\t\t */\r\n\t\tasync renderAllPages() {\r\n\t\t\tif (!this.pdfDocument || this.totalPages === 0) return;\r\n\t\t\t\r\n\t\t\tconsole.log(`开始渲染 ${this.totalPages} 页PDF...`);\r\n\t\t\t\r\n\t\t\t// 清空之前的渲染状态\r\n\t\t\tthis.renderedPages.clear();\r\n\t\t\tthis.renderingPages.clear();\r\n\t\t\tthis.allPagesRendered = false;\r\n\t\t\t\r\n\t\t\t// 通知父组件开始渲染\r\n\t\t\tthis.$emit('render-status-change', false);\r\n\t\t\t\r\n\t\t\t// 创建所有页面的占位符\r\n\t\t\tthis.renderedPages = new Set();\r\n\t\t\t\r\n\t\t\t// 批量渲染页面，每批2个页面避免阻塞\r\n\t\t\tconst batchSize = 2;\r\n\t\t\tfor (let i = 1; i <= this.totalPages; i += batchSize) {\r\n\t\t\t\tconst end = Math.min(i + batchSize - 1, this.totalPages);\r\n\t\t\t\tconst promises = [];\r\n\t\t\t\t\r\n\t\t\t\tfor (let pageNum = i; pageNum <= end; pageNum++) {\r\n\t\t\t\t\tpromises.push(this.renderPage(pageNum));\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tawait Promise.all(promises);\r\n\t\t\t\t\r\n\t\t\t\t// 给用户界面更新机会\r\n\t\t\t\tawait this.sleep(50);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查是否所有页面都已渲染完成\r\n\t\t\tthis.checkAllPagesRendered();\r\n\t\t\t\r\n\t\t\tconsole.log('所有页面渲染完成');\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 渲染单个页面（简化版）\r\n\t\t */\r\n\t\tasync renderPage(pageNum, isEnlarge = false) {\r\n\t\t\tif (!this.pdfDocument || pageNum < 1 || pageNum > this.totalPages) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.renderingPages.has(pageNum)) return;\r\n\t\t\t\r\n\t\t\tthis.renderingPages.add(pageNum);\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tconst cacheKey = `${pageNum}-${isEnlarge ? 'enlarge' : 'normal'}`;\r\n\t\t\t\t\r\n\t\t\t\t// 检查缓存\r\n\t\t\t\tif (this.pageCache.has(cacheKey)) {\r\n\t\t\t\t\tthis.renderCanvas(pageNum, this.pageCache.get(cacheKey), isEnlarge);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取页面\r\n\t\t\t\tconst page = await this.pdfDocument.getPage(pageNum);\r\n\t\t\t\tconst scale = isEnlarge ? 2 : 1.2;\r\n\t\t\t\tconst viewport = page.getViewport({ scale });\r\n\t\t\t\t\r\n\t\t\t\t// 创建canvas进行渲染\r\n\t\t\t\tconst canvas = document.createElement('canvas');\r\n\t\t\t\tconst context = canvas.getContext('2d');\r\n\t\t\t\t\r\n\t\t\t\tcanvas.height = viewport.height;\r\n\t\t\t\tcanvas.width = viewport.width;\r\n\t\t\t\t\r\n\t\t\t\tawait page.render({\r\n\t\t\t\t\tcanvasContext: context,\r\n\t\t\t\t\tviewport: viewport\r\n\t\t\t\t}).promise;\r\n\t\t\t\t\r\n\t\t\t\t// 缓存渲染结果\r\n\t\t\t\tthis.pageCache.set(cacheKey, canvas);\r\n\t\t\t\t\r\n\t\t\t\t// 渲染到实际canvas\r\n\t\t\t\tthis.renderCanvas(pageNum, canvas, isEnlarge);\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(`渲染第${pageNum}页失败:`, error);\r\n\t\t\t} finally {\r\n\t\t\t\tthis.renderingPages.delete(pageNum);\r\n\t\t\t\tthis.renderedPages.add(pageNum);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 渲染到实际canvas\r\n\t\t */\r\n\t\trenderCanvas(pageNum, sourceCanvas, isEnlarge = false) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst refName = isEnlarge ? `enlarge-canvas-${pageNum}` : `canvas-${pageNum}`;\r\n\t\t\t\tconst canvases = this.$refs[refName];\r\n\t\t\t\t\r\n\t\t\t\t// 确保canvas存在\r\n\t\t\t\tif (!canvases || canvases.length === 0) {\r\n\t\t\t\t\tconsole.warn(`Canvas for page ${pageNum} not found, retrying...`);\r\n\t\t\t\t\tsetTimeout(() => this.renderCanvas(pageNum, sourceCanvas, isEnlarge), 100);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst canvas = canvases[0];\r\n\t\t\t\tif (!canvas) {\r\n\t\t\t\t\tconsole.warn(`Canvas element for page ${pageNum} is null`);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst context = canvas.getContext('2d');\r\n\t\t\t\tif (!context) {\r\n\t\t\t\t\tconsole.warn(`Canvas context for page ${pageNum} is null`);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tcanvas.height = sourceCanvas.height;\r\n\t\t\t\tcanvas.width = sourceCanvas.width;\r\n\t\t\t\t\r\n\t\t\t\tcontext.clearRect(0, 0, canvas.width, canvas.height);\r\n\t\t\t\tcontext.drawImage(sourceCanvas, 0, 0);\r\n\t\t\t\t\r\n\t\t\t\t// 确保页面标记为已渲染\r\n\t\t\t\tthis.renderedPages.add(pageNum);\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否所有页面都已渲染完成\r\n\t\t\t\tthis.checkAllPagesRendered();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 检查所有页面是否渲染完成\r\n\t\t */\r\n\t\tcheckAllPagesRendered() {\r\n\t\t\tif (this.totalPages > 0 && this.renderedPages.size >= this.totalPages) {\r\n\t\t\t\tthis.allPagesRendered = true;\r\n\t\t\t\tconsole.log('所有PDF页面渲染完成，启用点击事件');\r\n\t\t\t\t// 通知父组件所有页面渲染完成\r\n\t\t\t\tthis.$emit('render-status-change', true);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 管理缓存大小\r\n\t\t */\r\n\t\tmanageCacheSize() {\r\n\t\t\tif (this.pageCache.size > this.maxCacheSize) {\r\n\t\t\t\tconst keys = Array.from(this.pageCache.keys());\r\n\t\t\t\tconst toRemove = keys.slice(0, keys.length - this.maxCacheSize);\r\n\t\t\t\t\r\n\t\t\t\ttoRemove.forEach(key => {\r\n\t\t\t\t\tthis.pageCache.delete(key);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 跳转到指定页面\r\n\t\t */\r\n\t\tasync skipPage(pageNum) {\r\n\t\t\tif (pageNum < 1) pageNum = 1;\r\n\t\t\tif (pageNum > this.totalPages) pageNum = this.totalPages;\r\n\t\t\t\r\n\t\t\tthis.curPage = pageNum;\r\n\t\t\t\r\n\t\t\t// 滚动到指定页面 - 使用精确的DOM查找\r\n\t\t\tconst container = document.getElementById('pdf-container');\r\n\t\t\tif (!container) {\r\n\t\t\t\tconsole.error('PDF容器未找到');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用ref查找canvas元素\r\n\t\t\tconst refName = `canvas-${pageNum}`;\r\n\t\t\tconst targetCanvas = this.$refs[refName];\r\n\t\t\t\r\n\t\t\tif (targetCanvas && targetCanvas[0]) {\r\n\t\t\t\t// 直接滚动到目标canvas\r\n\t\t\t\ttargetCanvas[0].scrollIntoView({\r\n\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\tblock: 'start',\r\n\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// 使用querySelector作为备选方案\r\n\t\t\t\tconst canvasSelector = `canvas[data-page=\"${pageNum}\"]`;\r\n\t\t\t\tconst fallbackCanvas = container.querySelector(canvasSelector);\r\n\t\t\t\t\r\n\t\t\t\tif (fallbackCanvas) {\r\n\t\t\t\t\tfallbackCanvas.scrollIntoView({\r\n\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\tblock: 'start',\r\n\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果找不到canvas，直接滚动到对应位置\r\n\t\t\t\t\tconst pageHeight = 800; // 估计的页面高度\r\n\t\t\t\t\tconst targetTop = (pageNum - 1) * pageHeight;\r\n\t\t\t\t\tcontainer.scrollTo({\r\n\t\t\t\t\t\ttop: targetTop,\r\n\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 放大显示\r\n\t\t * @param {Number} pageNum - 要放大的页码\r\n\t\t */\r\n\t\tasync enlarge(pageNum) {\r\n\t\t\t// 确保当前页码被更新为要放大的页码\r\n\t\t\tthis.curPage = pageNum;\r\n\t\t\t\r\n\t\t\tthis.display_enlarge = true;\r\n\t\t\tdocument.body.style.overflow = \"hidden\";\r\n\t\t\tdocument.documentElement.style.overflow = \"hidden\";\r\n\t\t\t\r\n\t\t\t// 预渲染放大版本（只渲染当前页）\r\n\t\t\tawait this.renderPage(pageNum, true);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 获取放大模式可见页面\r\n\t\t * 只返回当前页码，实现只放大当前预览的页面\r\n\t\t */\r\n\t\tgetEnlargeVisiblePages() {\r\n\t\t\tif (!this.totalPages) return [];\r\n\t\t\t\r\n\t\t\t// 只返回当前页码\r\n\t\t\treturn [this.curPage];\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 关闭放大模式\r\n\t\t */\r\n\t\tclose() {\r\n\t\t\tthis.display_enlarge = false;\r\n\t\t\tdocument.body.style.overflow = \"auto\";\r\n\t\t\tdocument.documentElement.style.overflow = \"auto\";\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 工具方法：延时\r\n\t\t */\r\n\t\tsleep(ms) {\r\n\t\t\treturn new Promise(resolve => setTimeout(resolve, ms));\r\n\t\t}\r\n\t},\r\n\t\r\n\tmounted() {\r\n\t\t// 添加窗口大小变化监听\r\n\t\twindow.addEventListener('resize', this.handleResize);\r\n\t},\r\n\t\r\n\tbeforeDestroy() {\r\n\t\t// 清理资源\r\n\t\tthis.pageCache.clear();\r\n\t\tthis.renderedPages.clear();\r\n\t\tthis.renderingPages.clear();\r\n\t\tclearTimeout(this._retryTimer);\r\n\t\twindow.removeEventListener('resize', this.handleResize);\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.pdf-loading-container {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100vh;\r\n\tbackground-color: rgba(255, 255, 255, 0.9);\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tz-index: 1000;\r\n}\r\n\r\n.loading-content {\r\n\tbackground: white;\r\n\tpadding: 40px;\r\n\tborder-radius: 12px;\r\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n\ttext-align: center;\r\n\tmin-width: 400px;\r\n\tmax-width: 500px;\r\n}\r\n\r\n.loading-text {\r\n\tfont-size: 18px;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20px;\r\n}\r\n\r\n.loading-progress {\r\n\tmargin: 20px 0;\r\n}\r\n\r\n.loading-detail {\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n\tmargin-top: 15px;\r\n\tmin-height: 20px;\r\n}\r\n\r\n.pdf-page {\r\n\tposition: relative;\r\n\tmargin-bottom: 20px;\r\n\ttext-align: center;\r\n}\r\n\r\n.canvas-enlarge {\r\n\tz-index: 999;\r\n\tbackground-color: rgba(0, 0, 0, 0.8);\r\n\tposition: fixed;\r\n\twidth: 100%;\r\n\theight: 100vh;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n#enlarge {\r\n\tbackground: white;\r\n\tborder-radius: 8px;\r\n\tpadding: 20px;\r\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;;AAGA;AACA,IAAAE,OAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACA;EACAC,IAAA;EAEA;EACAC,KAAA;IACA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAL,OAAA;IACA;IACA;IACAM,OAAA;MACAF,IAAA,EAAAC,MAAA;MACAL,OAAA;IACA;IACA;IACAO,UAAA;MACAH,IAAA,EAAAI,MAAA;MACAR,OAAA;IACA;IACA;IACAS,UAAA;MACAL,IAAA,EAAAI,MAAA;MACAR,OAAA;IACA;EACA;EAEA;EACAU,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,GAAA;MACA;MACAC,WAAA;MACA;MACAC,UAAA;MACA;MACAC,OAAA;MACA;MACAC,SAAA;MACA;MACAC,eAAA;MACA;MACAC,aAAA;MACA;MACAC,eAAA;MACA;MACAC,gBAAA;MAEA;MACAC,SAAA,MAAAC,GAAA;MACAC,aAAA,MAAAC,GAAA;MACAC,cAAA,MAAAD,GAAA;MAEA;MACAE,YAAA;IACA;EACA;EAEAC,QAAA;IACA;EAAA,CACA;EAEAC,KAAA;IACA;IACAxB,MAAA;MACAyB,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,YAAAA,MAAA,KAAAC,SAAA;UACA,KAAAnB,GAAA,GAAAkB,MAAA;UACAE,OAAA,CAAAC,GAAA,kBAAArB,GAAA;UACA,SAAAA,GAAA;YACA,KAAAA,GAAA,GAAAjB,OAAA,QAAAiB,GAAA;YACA;YACA,KAAAsB,UAAA;YACA,KAAAC,OAAA;UACA;QACA;MACA;MACAC,SAAA;IACA;IACA;EACA;EAEAC,OAAA;IACA;AACA;AACA;IACAC,YAAA,WAAAA,aAAA;MACA,IAAAC,SAAA,QAAAC,GAAA,CAAAC,aAAA;MACA,IAAAF,SAAA;QACA,KAAAG,eAAA,GAAAH,SAAA,CAAAI,YAAA;MACA;IACA;IACA;AACA;AACA;IACAC,cAAA,WAAAA,eAAAC,UAAA;MACA,UAAAC,MAAA,CAAAD,UAAA;IACA;IAEA;AACA;AACA;IACAV,OAAA,WAAAA,QAAA;MAAA,IAAAY,KAAA;MAAA,WAAAC,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAAC,QAAA;QAAA,IAAAC,YAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAEA;cACAV,KAAA,CAAAW,SAAA;gBACA,IAAAnB,SAAA,GAAAQ,KAAA,CAAAY,KAAA,CAAAC,YAAA;gBACA,IAAArB,SAAA;kBACAA,SAAA,CAAAsB,SAAA;gBACA;cACA;cAEAd,KAAA,CAAA/B,SAAA;cACA+B,KAAA,CAAA9B,eAAA;cACA8B,KAAA,CAAA7B,aAAA;;cAEA;cACA6B,KAAA,CAAAb,UAAA;;cAEA;cACAa,KAAA,CAAAe,kBAAA;;cAEA;cAAAP,QAAA,CAAAC,CAAA;cAAA,OACAT,KAAA,CAAAgB,kBAAA;YAAA;cAAAX,YAAA,GAAAG,QAAA,CAAAS,CAAA;cACAhC,OAAA,CAAAC,GAAA,eAAAmB,YAAA;cAAA,KACAA,YAAA;gBAAAG,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACAT,KAAA,CAAAkB,oBAAA;YAAA;cAAAV,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OAEAT,KAAA,CAAAmB,eAAA;YAAA;cAAAX,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAS,CAAA;cAIAhC,OAAA,CAAAmC,KAAA,uBAAAd,EAAA;cACAN,KAAA,CAAA7B,aAAA,gBAAAmC,EAAA,CAAAe,OAAA;cACArB,KAAA,CAAA/B,SAAA;YAAA;cAAA,OAAAuC,QAAA,CAAAc,CAAA;UAAA;QAAA,GAAAlB,OAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAW,kBAAA,WAAAA,mBAAA;MACA;QACA,IAAAQ,MAAA,GAAA7E,OAAA;QACA8E,kBAAA,CAAAC,mBAAA,CAAAC,SAAA,GAAAH,MAAA;MACA,SAAAI,CAAA;QACA;QACAH,kBAAA,CAAAC,mBAAA,CAAAC,SAAA,8CAAA3B,MAAA,CAAAyB,kBAAA,CAAAI,OAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAZ,kBAAA,WAAAA,mBAAA;MAAA,IAAAa,MAAA;MAAA,WAAA5B,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAA2B,SAAA;QAAA,IAAAC,QAAA,EAAAC,QAAA,EAAAC,GAAA;QAAA,WAAA/B,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAA2B,SAAA;UAAA,kBAAAA,SAAA,CAAAzB,CAAA;YAAA;cAAAyB,SAAA,CAAAxB,CAAA;cAEA;cACAqB,QAAA,GAAAF,MAAA,CAAAM,eAAA,CAAAN,MAAA,CAAAhE,GAAA;cAAAqE,SAAA,CAAAzB,CAAA;cAAA,OACA2B,cAAA,CAAAC,GAAA,IAAAtC,MAAA,CAAAnD,OAAA;gBACA0F,MAAA;kBAAAP,QAAA,EAAAA;gBAAA;cACA;YAAA;cAFAC,QAAA,GAAAE,SAAA,CAAAjB,CAAA;cAGAhC,OAAA,CAAAC,GAAA,CAAA8C,QAAA;cACA;cAAA,OAAAE,SAAA,CAAAZ,CAAA,IACAU,QAAA,CAAApE,IAAA,CAAA2E,QAAA;YAAA;cAAAL,SAAA,CAAAxB,CAAA;cAAAuB,GAAA,GAAAC,SAAA,CAAAjB,CAAA;cAEAhC,OAAA,CAAAuD,IAAA,sBAAAP,GAAA;cAAA,OAAAC,SAAA,CAAAZ,CAAA,IACA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAK,eAAA,WAAAA,gBAAAtE,GAAA;MACA;MACA,IAAAjB,OAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA,IAAAc,GAAA,CAAA4E,UAAA,CAAA7F,OAAA;QACA,OAAAiB,GAAA,CAAA6E,SAAA,CAAA9F,OAAA,CAAA+F,MAAA;MACA;MACA;MACA,OAAA9E,GAAA;IACA;IAEA;AACA;AACA;IACA+E,eAAA,WAAAA,gBAAA/E,GAAA;MACA,IAAAgF,KAAA,GAAAhF,GAAA,CAAAiF,KAAA;MACA,OAAAD,KAAA,CAAAA,KAAA,CAAAF,MAAA;IACA;IAEA;AACA;AACA;AACA;IACAzB,oBAAA,WAAAA,qBAAA;MAAA,IAAA6B,MAAA;MAAA,WAAA9C,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAA6C,SAAA;QAAA,IAAAjB,QAAA,EAAAkB,YAAA,EAAAC,WAAA;QAAA,WAAAhD,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAA4C,SAAA;UAAA,kBAAAA,SAAA,CAAA1C,CAAA;YAAA;cACAsC,MAAA,CAAA7E,eAAA;cACA6E,MAAA,CAAA5E,aAAA;cAEA4D,QAAA,GAAAgB,MAAA,CAAAZ,eAAA,CAAAY,MAAA,CAAAlF,GAAA;cACAoF,YAAA,MAAAlD,MAAA,CAAAnD,OAAA,kCAAAmD,MAAA,CAAAqD,kBAAA,CAAArB,QAAA;cAEA9C,OAAA,CAAAC,GAAA,qBAAA+D,YAAA;;cAEA;cACAC,WAAA,GAAA1B,kBAAA,CAAA6B,WAAA;gBACAxF,GAAA,EAAAoF,YAAA;gBACAK,cAAA;gBAAA;gBACAC,gBAAA;gBAAA;gBACAC,aAAA;gBAAA;gBACAC,YAAA;gBAAA;gBACAC,eAAA;gBAAA;gBACAC,cAAA;gBAAA;gBACAC,YAAA;gBAAA;gBACAC,OAAA;gBAAA;gBACAC,UAAA;gBACAC,WAAA;kBACA;kBACA;kBACA;gBACA;gBACAC,UAAA,WAAAA,WAAAC,YAAA;kBACA;kBACA,IAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,YAAA,CAAAI,MAAA,GAAAJ,YAAA,CAAAK,KAAA;kBACAvB,MAAA,CAAAwB,cAAA,CAAAN,YAAA;kBACA,IAAAA,YAAA,CAAAI,MAAA,GAAAJ,YAAA,CAAAK,KAAA;oBACAvB,MAAA,CAAA5E,aAAA,qDAAA4B,MAAA,CAAAmE,OAAA,SAAAnE,MAAA,CAAAoE,IAAA,CAAAC,KAAA,CAAAH,YAAA,CAAAI,MAAA,mBAAAtE,MAAA,CAAAoE,IAAA,CAAAC,KAAA,CAAAH,YAAA,CAAAK,KAAA;kBACA;oBACAvB,MAAA,CAAA5E,aAAA;kBACA;gBACA;cACA;cAAAgF,SAAA,CAAA1C,CAAA;cAAA,OAEAyC,WAAA,CAAAsB,OAAA;YAAA;cAAAzB,MAAA,CAAAjF,WAAA,GAAAqF,SAAA,CAAAlC,CAAA;cACA8B,MAAA,CAAAhF,UAAA,GAAAgF,MAAA,CAAAjF,WAAA,CAAA2G,QAAA;cAEA1B,MAAA,CAAA7E,eAAA;cACA6E,MAAA,CAAA5E,aAAA,sEAAA4B,MAAA,CAAAgD,MAAA,CAAAhF,UAAA;cAEA2G,UAAA;gBACA3B,MAAA,CAAA9E,SAAA;gBACA;gBACA8E,MAAA,CAAA4B,gCAAA;cACA;YAAA;cAAA,OAAAxB,SAAA,CAAA7B,CAAA;UAAA;QAAA,GAAA0B,QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACA7B,eAAA,WAAAA,gBAAA;MAAA,IAAAyD,MAAA;MAAA,WAAA3E,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAA0E,SAAA;QAAA,IAAA3B,WAAA;QAAA,WAAAhD,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAAuE,SAAA;UAAA,kBAAAA,SAAA,CAAArE,CAAA;YAAA;cACAmE,MAAA,CAAA1G,eAAA;cACA0G,MAAA,CAAAzG,aAAA;cAEAc,OAAA,CAAAC,GAAA,kBAAA0F,MAAA,CAAA/G,GAAA;;cAEA;cACAqF,WAAA,GAAA1B,kBAAA,CAAA6B,WAAA;gBACAxF,GAAA,EAAA+G,MAAA,CAAA/G,GAAA;gBACAyF,cAAA;gBAAA;gBACAC,gBAAA;gBAAA;gBACAC,aAAA;gBAAA;gBACAC,YAAA;gBAAA;gBACAsB,aAAA;gBAAA;gBACArB,eAAA;gBACAC,cAAA;gBACAI,WAAA;kBACA;gBACA;gBACAC,UAAA,WAAAA,WAAAC,YAAA;kBACA;kBACAW,MAAA,CAAAL,cAAA,CAAAN,YAAA;kBACAW,MAAA,CAAAzG,aAAA,wCAAA4B,MAAA,CAAAoE,IAAA,CAAAC,KAAA,CAAAH,YAAA,CAAAI,MAAA,mBAAAtE,MAAA,CAAAoE,IAAA,CAAAC,KAAA,CAAAH,YAAA,CAAAK,KAAA;gBACA;cACA;cAAAQ,SAAA,CAAArE,CAAA;cAAA,OAEAyC,WAAA,CAAAsB,OAAA;YAAA;cAAAI,MAAA,CAAA9G,WAAA,GAAAgH,SAAA,CAAA7D,CAAA;cACA2D,MAAA,CAAA7G,UAAA,GAAA6G,MAAA,CAAA9G,WAAA,CAAA2G,QAAA;cAEAG,MAAA,CAAA1G,eAAA;cACA0G,MAAA,CAAAzG,aAAA,yDAAA4B,MAAA,CAAA6E,MAAA,CAAA7G,UAAA;cAEA2G,UAAA;gBACAE,MAAA,CAAA3G,SAAA;gBACA;gBACA2G,MAAA,CAAAI,uBAAA;cACA;YAAA;cAAA,OAAAF,SAAA,CAAAxD,CAAA;UAAA;QAAA,GAAAuD,QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAN,cAAA,WAAAA,eAAAN,YAAA;MACA,IAAAA,YAAA,CAAAK,KAAA;QACA,IAAAW,QAAA,GAAAd,IAAA,CAAAC,KAAA,CAAAH,YAAA,CAAAI,MAAA,GAAAJ,YAAA,CAAAK,KAAA;QACA,KAAApG,eAAA,GAAAiG,IAAA,CAAAe,GAAA,CAAAD,QAAA;QACA,KAAA9G,aAAA,kCAAA4B,MAAA,CAAAoE,IAAA,CAAAC,KAAA,CAAAH,YAAA,CAAAI,MAAA,mBAAAtE,MAAA,CAAAoE,IAAA,CAAAC,KAAA,CAAAH,YAAA,CAAAK,KAAA;MACA;IACA;IAEA;AACA;AACA;IACAU,uBAAA,WAAAA,wBAAA;MAAA,IAAAG,MAAA;MAAA,WAAAlF,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAAiF,SAAA;QAAA,IAAAC,YAAA,EAAAC,CAAA;QAAA,WAAApF,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAAgF,SAAA;UAAA,kBAAAA,SAAA,CAAA9E,CAAA;YAAA;cAAA,MACA,CAAA0E,MAAA,CAAArH,WAAA,IAAAqH,MAAA,CAAApH,UAAA;gBAAAwH,SAAA,CAAA9E,CAAA;gBAAA;cAAA;cAAA,OAAA8E,SAAA,CAAAjE,CAAA;YAAA;cAEArC,OAAA,CAAAC,GAAA,yCAAAa,MAAA,CAAAoF,MAAA,CAAApH,UAAA;;cAEA;cACAsH,YAAA,GAAAlB,IAAA,CAAAe,GAAA,IAAAC,MAAA,CAAApH,UAAA;cACAuH,CAAA;YAAA;cAAA,MAAAA,CAAA,IAAAD,YAAA;gBAAAE,SAAA,CAAA9E,CAAA;gBAAA;cAAA;cAAA8E,SAAA,CAAA9E,CAAA;cAAA,OACA0E,MAAA,CAAAK,UAAA,CAAAF,CAAA;YAAA;cAAAC,SAAA,CAAA9E,CAAA;cAAA,OACA0E,MAAA,CAAAM,KAAA;YAAA;cAFAH,CAAA;cAAAC,SAAA,CAAA9E,CAAA;cAAA;YAAA;cAKA;cACA0E,MAAA,CAAAO,qBAAA;YAAA;cAAA,OAAAH,SAAA,CAAAjE,CAAA;UAAA;QAAA,GAAA8D,QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAT,gCAAA,WAAAA,iCAAA;MAAA,IAAAgB,MAAA;MAAA,WAAA1F,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAAyF,SAAA;QAAA,IAAAP,YAAA,EAAAC,CAAA;QAAA,WAAApF,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAAsF,SAAA;UAAA,kBAAAA,SAAA,CAAApF,CAAA;YAAA;cAAA,MACA,CAAAkF,MAAA,CAAA7H,WAAA,IAAA6H,MAAA,CAAA5H,UAAA;gBAAA8H,SAAA,CAAApF,CAAA;gBAAA;cAAA;cAAA,OAAAoF,SAAA,CAAAvE,CAAA;YAAA;cAEArC,OAAA,CAAAC,GAAA,iEAAAa,MAAA,CAAA4F,MAAA,CAAA5H,UAAA;;cAEA;cACAsH,YAAA,GAAAlB,IAAA,CAAAe,GAAA,IAAAS,MAAA,CAAA5H,UAAA;cACAuH,CAAA;YAAA;cAAA,MAAAA,CAAA,IAAAD,YAAA;gBAAAQ,SAAA,CAAApF,CAAA;gBAAA;cAAA;cAAAoF,SAAA,CAAApF,CAAA;cAAA,OACAkF,MAAA,CAAAH,UAAA,CAAAF,CAAA;YAAA;cAAAO,SAAA,CAAApF,CAAA;cAAA,OACAkF,MAAA,CAAAF,KAAA;YAAA;cAFAH,CAAA;cAAAO,SAAA,CAAApF,CAAA;cAAA;YAAA;cAKA;cACAkF,MAAA,CAAAG,8BAAA;YAAA;cAAA,OAAAD,SAAA,CAAAvE,CAAA;UAAA;QAAA,GAAAsE,QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAF,qBAAA,WAAAA,sBAAA;MAAA,IAAAK,MAAA;MACA,IAAAC,cAAA;MACA,SAAAV,CAAA,MAAAA,CAAA,SAAAvH,UAAA,EAAAuH,CAAA;QACAU,cAAA,CAAAC,IAAA,CAAAX,CAAA;MACA;MAEA,IAAAY,eAAA;QAAA,IAAAC,IAAA,OAAAlG,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAAiG,SAAA;UAAA,IAAAC,OAAA;UAAA,WAAAnG,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAA+F,SAAA;YAAA,kBAAAA,SAAA,CAAA7F,CAAA;cAAA;gBAAA,MACAuF,cAAA,CAAArD,MAAA;kBAAA2D,SAAA,CAAA7F,CAAA;kBAAA;gBAAA;gBAAA,OAAA6F,SAAA,CAAAhF,CAAA;cAAA;gBAEA+E,OAAA,GAAAL,cAAA,CAAAO,KAAA;gBAAA,IACAR,MAAA,CAAAvH,aAAA,CAAAgI,GAAA,CAAAH,OAAA;kBAAAC,SAAA,CAAA7F,CAAA;kBAAA;gBAAA;gBAAA6F,SAAA,CAAA7F,CAAA;gBAAA,OACAsF,MAAA,CAAAP,UAAA,CAAAa,OAAA;cAAA;gBAAAC,SAAA,CAAA7F,CAAA;gBAAA,OACAsF,MAAA,CAAAN,KAAA;cAAA;gBAGA;gBACAf,UAAA,CAAAwB,eAAA;cAAA;gBAAA,OAAAI,SAAA,CAAAhF,CAAA;YAAA;UAAA,GAAA8E,QAAA;QAAA,CACA;QAAA,gBAXAF,eAAA;UAAA,OAAAC,IAAA,CAAAM,KAAA,OAAAC,SAAA;QAAA;MAAA,GAWA;;MAEA;MACAhC,UAAA,CAAAwB,eAAA;IACA;IAEA;AACA;AACA;IACAJ,8BAAA,WAAAA,+BAAA;MAAA,IAAAa,MAAA;MACA,IAAAX,cAAA;MACA,SAAAV,CAAA,MAAAA,CAAA,SAAAvH,UAAA,EAAAuH,CAAA;QACAU,cAAA,CAAAC,IAAA,CAAAX,CAAA;MACA;MAEA,IAAAY,gBAAA;QAAA,IAAAU,KAAA,OAAA3G,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAA0G,SAAA;UAAA,IAAAC,WAAA,EAAAT,OAAA,EAAAU,QAAA;UAAA,WAAA7G,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAAyG,SAAA;YAAA,kBAAAA,SAAA,CAAAvG,CAAA;cAAA;gBAAA,MACAuF,cAAA,CAAArD,MAAA;kBAAAqE,SAAA,CAAAvG,CAAA;kBAAA;gBAAA;gBACAkG,MAAA,CAAAM,qBAAA;gBAAA,OAAAD,SAAA,CAAA1F,CAAA;cAAA;gBAIA;gBACAwF,WAAA,GAAAH,MAAA,CAAAO,wBAAA;gBAAA,MACAJ,WAAA,CAAAnE,MAAA;kBAAAqE,SAAA,CAAAvG,CAAA;kBAAA;gBAAA;gBACA4F,OAAA,GAAAS,WAAA;gBAAA,MACA,CAAAH,MAAA,CAAAnI,aAAA,CAAAgI,GAAA,CAAAH,OAAA,MAAAM,MAAA,CAAAjI,cAAA,CAAA8H,GAAA,CAAAH,OAAA;kBAAAW,SAAA,CAAAvG,CAAA;kBAAA;gBAAA;gBAAAuG,SAAA,CAAAvG,CAAA;gBAAA,OACAkG,MAAA,CAAAnB,UAAA,CAAAa,OAAA;cAAA;gBAAAW,SAAA,CAAAvG,CAAA;gBAAA,OACAkG,MAAA,CAAAlB,KAAA;cAAA;gBAAAuB,SAAA,CAAAvG,CAAA;gBAAA;cAAA;gBAAA,MAEAuF,cAAA,CAAArD,MAAA;kBAAAqE,SAAA,CAAAvG,CAAA;kBAAA;gBAAA;gBACA;gBACA4F,QAAA,GAAAL,cAAA,CAAAO,KAAA;gBAAA,MACA,CAAAI,MAAA,CAAAnI,aAAA,CAAAgI,GAAA,CAAAH,QAAA,MAAAM,MAAA,CAAAjI,cAAA,CAAA8H,GAAA,CAAAH,QAAA;kBAAAW,SAAA,CAAAvG,CAAA;kBAAA;gBAAA;gBAAAuG,SAAA,CAAAvG,CAAA;gBAAA,OACAkG,MAAA,CAAAnB,UAAA,CAAAa,QAAA;cAAA;gBAAAW,SAAA,CAAAvG,CAAA;gBAAA,OACAkG,MAAA,CAAAlB,KAAA;cAAA;gBAIA;gBACAf,UAAA,CAAAwB,gBAAA;cAAA;gBAAA,OAAAc,SAAA,CAAA1F,CAAA;YAAA;UAAA,GAAAuF,QAAA;QAAA,CACA;QAAA,gBAzBAX,eAAA;UAAA,OAAAU,KAAA,CAAAH,KAAA,OAAAC,SAAA;QAAA;MAAA,GAyBA;;MAEA;MACAhC,UAAA,CAAAwB,gBAAA;IACA;IAEA;AACA;AACA;IACAgB,wBAAA,WAAAA,yBAAA;MACA,IAAAJ,WAAA;MACA,IAAAK,WAAA,QAAAnJ,OAAA;MACA,IAAAoJ,KAAA;;MAEA,SAAA9B,CAAA,GAAAnB,IAAA,CAAAkD,GAAA,IAAAF,WAAA,GAAAC,KAAA,GAAA9B,CAAA,IAAAnB,IAAA,CAAAe,GAAA,MAAAnH,UAAA,EAAAoJ,WAAA,GAAAC,KAAA,GAAA9B,CAAA;QACA,UAAA9G,aAAA,CAAAgI,GAAA,CAAAlB,CAAA,WAAA5G,cAAA,CAAA8H,GAAA,CAAAlB,CAAA;UACAwB,WAAA,CAAAb,IAAA,CAAAX,CAAA;QACA;MACA;MAEA,OAAAwB,WAAA;IACA;IAEA;AACA;AACA;IACA3H,UAAA,WAAAA,WAAA;MAAA,IAAAmI,MAAA;MACA,KAAAvJ,UAAA;MACA,KAAAC,OAAA;MACA,KAAAK,gBAAA;MACA,KAAAC,SAAA,CAAAiJ,KAAA;MACA,KAAA/I,aAAA,CAAA+I,KAAA;MACA,KAAA7I,cAAA,CAAA6I,KAAA;MACA,KAAApJ,aAAA;;MAEA;MACA,KAAAqJ,KAAA;;MAEA;MACA,SAAAC,WAAA;QACAC,YAAA,MAAAD,WAAA;MACA;;MAEA;MACA,KAAA9G,SAAA;QACA,IAAAnB,SAAA,GAAA8H,MAAA,CAAA1G,KAAA,CAAAC,YAAA;QACA,IAAArB,SAAA;UACAA,SAAA,CAAAsB,SAAA;UACAtB,SAAA,CAAAmI,QAAA;QACA;MACA;IACA;IAGA;AACA;AACA;IACAC,YAAA,WAAAA,aAAAC,KAAA;MACA,IAAArI,SAAA,GAAAqI,KAAA,CAAAC,MAAA;MACA,IAAAC,QAAA,GAAAvI,SAAA,CAAAwI,gBAAA;MAEA,SAAA1C,CAAA,MAAAA,CAAA,GAAAyC,QAAA,CAAApF,MAAA,EAAA2C,CAAA;QACA,IAAA2C,MAAA,GAAAF,QAAA,CAAAzC,CAAA;QACA,IAAA4C,IAAA,GAAAD,MAAA,CAAAE,qBAAA;QACA,IAAAC,aAAA,GAAA5I,SAAA,CAAA2I,qBAAA;;QAEA;QACA,IAAAD,IAAA,CAAAG,GAAA,IAAAD,aAAA,CAAAC,GAAA,IAAAH,IAAA,CAAAG,GAAA,IAAAD,aAAA,CAAAE,MAAA;UACA,KAAAtK,OAAA,GAAAuK,QAAA,CAAAN,MAAA,CAAAO,YAAA;UACA;QACA;MACA;IACA;IAIA;AACA;AACA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,WAAAzI,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAAwI,SAAA;QAAA,IAAAC,SAAA,EAAAtD,CAAA,EAAAuD,GAAA,EAAAC,QAAA,EAAAzC,OAAA;QAAA,WAAAnG,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAAwI,SAAA;UAAA,kBAAAA,SAAA,CAAAtI,CAAA;YAAA;cAAA,MACA,CAAAiI,MAAA,CAAA5K,WAAA,IAAA4K,MAAA,CAAA3K,UAAA;gBAAAgL,SAAA,CAAAtI,CAAA;gBAAA;cAAA;cAAA,OAAAsI,SAAA,CAAAzH,CAAA;YAAA;cAEArC,OAAA,CAAAC,GAAA,6BAAAa,MAAA,CAAA2I,MAAA,CAAA3K,UAAA;;cAEA;cACA2K,MAAA,CAAAlK,aAAA,CAAA+I,KAAA;cACAmB,MAAA,CAAAhK,cAAA,CAAA6I,KAAA;cACAmB,MAAA,CAAArK,gBAAA;;cAEA;cACAqK,MAAA,CAAAlB,KAAA;;cAEA;cACAkB,MAAA,CAAAlK,aAAA,OAAAC,GAAA;;cAEA;cACAmK,SAAA;cACAtD,CAAA;YAAA;cAAA,MAAAA,CAAA,IAAAoD,MAAA,CAAA3K,UAAA;gBAAAgL,SAAA,CAAAtI,CAAA;gBAAA;cAAA;cACAoI,GAAA,GAAA1E,IAAA,CAAAe,GAAA,CAAAI,CAAA,GAAAsD,SAAA,MAAAF,MAAA,CAAA3K,UAAA;cACA+K,QAAA;cAEA,KAAAzC,OAAA,GAAAf,CAAA,EAAAe,OAAA,IAAAwC,GAAA,EAAAxC,OAAA;gBACAyC,QAAA,CAAA7C,IAAA,CAAAyC,MAAA,CAAAlD,UAAA,CAAAa,OAAA;cACA;cAAA0C,SAAA,CAAAtI,CAAA;cAAA,OAEAuI,OAAA,CAAAC,GAAA,CAAAH,QAAA;YAAA;cAAAC,SAAA,CAAAtI,CAAA;cAAA,OAGAiI,MAAA,CAAAjD,KAAA;YAAA;cAXAH,CAAA,IAAAsD,SAAA;cAAAG,SAAA,CAAAtI,CAAA;cAAA;YAAA;cAcA;cACAiI,MAAA,CAAAzB,qBAAA;cAEAhI,OAAA,CAAAC,GAAA;YAAA;cAAA,OAAA6J,SAAA,CAAAzH,CAAA;UAAA;QAAA,GAAAqH,QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAnD,UAAA,WAAAA,WAAAa,OAAA;MAAA,IAAA6C,UAAA,GAAAxC,SAAA;QAAAyC,MAAA;MAAA,WAAAlJ,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAAiJ,SAAA;QAAA,IAAAC,SAAA,EAAAC,QAAA,EAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA,EAAAxB,MAAA,EAAAyB,OAAA,EAAAC,GAAA;QAAA,WAAAzJ,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAAqJ,SAAA;UAAA,kBAAAA,SAAA,CAAAnJ,CAAA;YAAA;cAAA4I,SAAA,GAAAH,UAAA,CAAAvG,MAAA,QAAAuG,UAAA,QAAAlK,SAAA,GAAAkK,UAAA;cAAA,MACA,CAAAC,MAAA,CAAArL,WAAA,IAAAuI,OAAA,QAAAA,OAAA,GAAA8C,MAAA,CAAApL,UAAA;gBAAA6L,SAAA,CAAAnJ,CAAA;gBAAA;cAAA;cAAA,OAAAmJ,SAAA,CAAAtI,CAAA;YAAA;cAAA,KAIA6H,MAAA,CAAAzK,cAAA,CAAA8H,GAAA,CAAAH,OAAA;gBAAAuD,SAAA,CAAAnJ,CAAA;gBAAA;cAAA;cAAA,OAAAmJ,SAAA,CAAAtI,CAAA;YAAA;cAEA6H,MAAA,CAAAzK,cAAA,CAAAmL,GAAA,CAAAxD,OAAA;cAAAuD,SAAA,CAAAlJ,CAAA;cAGA4I,QAAA,MAAAvJ,MAAA,CAAAsG,OAAA,OAAAtG,MAAA,CAAAsJ,SAAA,0BAEA;cAAA,KACAF,MAAA,CAAA7K,SAAA,CAAAkI,GAAA,CAAA8C,QAAA;gBAAAM,SAAA,CAAAnJ,CAAA;gBAAA;cAAA;cACA0I,MAAA,CAAAW,YAAA,CAAAzD,OAAA,EAAA8C,MAAA,CAAA7K,SAAA,CAAA+D,GAAA,CAAAiH,QAAA,GAAAD,SAAA;cAAA,OAAAO,SAAA,CAAAtI,CAAA;YAAA;cAAAsI,SAAA,CAAAnJ,CAAA;cAAA,OAKA0I,MAAA,CAAArL,WAAA,CAAAiM,OAAA,CAAA1D,OAAA;YAAA;cAAAkD,IAAA,GAAAK,SAAA,CAAA3I,CAAA;cACAuI,KAAA,GAAAH,SAAA;cACAI,QAAA,GAAAF,IAAA,CAAAS,WAAA;gBAAAR,KAAA,EAAAA;cAAA,IAEA;cACAvB,MAAA,GAAAgC,QAAA,CAAAC,aAAA;cACAR,OAAA,GAAAzB,MAAA,CAAAkC,UAAA;cAEAlC,MAAA,CAAAmC,MAAA,GAAAX,QAAA,CAAAW,MAAA;cACAnC,MAAA,CAAAoC,KAAA,GAAAZ,QAAA,CAAAY,KAAA;cAAAT,SAAA,CAAAnJ,CAAA;cAAA,OAEA8I,IAAA,CAAAe,MAAA;gBACAC,aAAA,EAAAb,OAAA;gBACAD,QAAA,EAAAA;cACA,GAAAjF,OAAA;YAAA;cAEA;cACA2E,MAAA,CAAA7K,SAAA,CAAAkM,GAAA,CAAAlB,QAAA,EAAArB,MAAA;;cAEA;cACAkB,MAAA,CAAAW,YAAA,CAAAzD,OAAA,EAAA4B,MAAA,EAAAoB,SAAA;cAAAO,SAAA,CAAAnJ,CAAA;cAAA;YAAA;cAAAmJ,SAAA,CAAAlJ,CAAA;cAAAiJ,GAAA,GAAAC,SAAA,CAAA3I,CAAA;cAGAhC,OAAA,CAAAmC,KAAA,sBAAArB,MAAA,CAAAsG,OAAA,0BAAAsD,GAAA;YAAA;cAAAC,SAAA,CAAAlJ,CAAA;cAEAyI,MAAA,CAAAzK,cAAA,CAAA+L,MAAA,CAAApE,OAAA;cACA8C,MAAA,CAAA3K,aAAA,CAAAqL,GAAA,CAAAxD,OAAA;cAAA,OAAAuD,SAAA,CAAAc,CAAA;YAAA;cAAA,OAAAd,SAAA,CAAAtI,CAAA;UAAA;QAAA,GAAA8H,QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAU,YAAA,WAAAA,aAAAzD,OAAA,EAAAsE,YAAA;MAAA,IAAAC,OAAA;MAAA,IAAAvB,SAAA,GAAA3C,SAAA,CAAA/D,MAAA,QAAA+D,SAAA,QAAA1H,SAAA,GAAA0H,SAAA;MACA,KAAA/F,SAAA;QACA,IAAAkK,OAAA,GAAAxB,SAAA,qBAAAtJ,MAAA,CAAAsG,OAAA,cAAAtG,MAAA,CAAAsG,OAAA;QACA,IAAA0B,QAAA,GAAA6C,OAAA,CAAAhK,KAAA,CAAAiK,OAAA;;QAEA;QACA,KAAA9C,QAAA,IAAAA,QAAA,CAAApF,MAAA;UACA1D,OAAA,CAAAuD,IAAA,oBAAAzC,MAAA,CAAAsG,OAAA;UACA3B,UAAA;YAAA,OAAAkG,OAAA,CAAAd,YAAA,CAAAzD,OAAA,EAAAsE,YAAA,EAAAtB,SAAA;UAAA;UACA;QACA;QAEA,IAAApB,MAAA,GAAAF,QAAA;QACA,KAAAE,MAAA;UACAhJ,OAAA,CAAAuD,IAAA,4BAAAzC,MAAA,CAAAsG,OAAA;UACA;QACA;QAEA,IAAAqD,OAAA,GAAAzB,MAAA,CAAAkC,UAAA;QACA,KAAAT,OAAA;UACAzK,OAAA,CAAAuD,IAAA,4BAAAzC,MAAA,CAAAsG,OAAA;UACA;QACA;QAEA4B,MAAA,CAAAmC,MAAA,GAAAO,YAAA,CAAAP,MAAA;QACAnC,MAAA,CAAAoC,KAAA,GAAAM,YAAA,CAAAN,KAAA;QAEAX,OAAA,CAAAoB,SAAA,OAAA7C,MAAA,CAAAoC,KAAA,EAAApC,MAAA,CAAAmC,MAAA;QACAV,OAAA,CAAAqB,SAAA,CAAAJ,YAAA;;QAEA;QACAC,OAAA,CAAApM,aAAA,CAAAqL,GAAA,CAAAxD,OAAA;;QAEA;QACAuE,OAAA,CAAA3D,qBAAA;MACA;IACA;IAEA;AACA;AACA;IACAA,qBAAA,WAAAA,sBAAA;MACA,SAAAlJ,UAAA,aAAAS,aAAA,CAAAwM,IAAA,SAAAjN,UAAA;QACA,KAAAM,gBAAA;QACAY,OAAA,CAAAC,GAAA;QACA;QACA,KAAAsI,KAAA;MACA;IACA;IAEA;AACA;AACA;IACAyD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,SAAA5M,SAAA,CAAA0M,IAAA,QAAArM,YAAA;QACA,IAAAwM,IAAA,GAAAC,KAAA,CAAAC,IAAA,MAAA/M,SAAA,CAAA6M,IAAA;QACA,IAAAG,QAAA,GAAAH,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAAxI,MAAA,QAAAhE,YAAA;QAEA2M,QAAA,CAAAE,OAAA,WAAAC,GAAA;UACAP,OAAA,CAAA5M,SAAA,CAAAmM,MAAA,CAAAgB,GAAA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC,QAAA,WAAAA,SAAArF,OAAA;MAAA,IAAAsF,OAAA;MAAA,WAAA1L,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAAyL,SAAA;QAAA,IAAApM,SAAA,EAAAqL,OAAA,EAAAgB,YAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAtO,UAAA,EAAAuO,SAAA;QAAA,WAAA9L,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAA0L,SAAA;UAAA,kBAAAA,SAAA,CAAAxL,CAAA;YAAA;cACA,IAAA4F,OAAA,MAAAA,OAAA;cACA,IAAAA,OAAA,GAAAsF,OAAA,CAAA5N,UAAA,EAAAsI,OAAA,GAAAsF,OAAA,CAAA5N,UAAA;cAEA4N,OAAA,CAAA3N,OAAA,GAAAqI,OAAA;;cAEA;cACA7G,SAAA,GAAAyK,QAAA,CAAAiC,cAAA;cAAA,IACA1M,SAAA;gBAAAyM,SAAA,CAAAxL,CAAA;gBAAA;cAAA;cACAxB,OAAA,CAAAmC,KAAA;cAAA,OAAA6K,SAAA,CAAA3K,CAAA;YAAA;cAIA;cACAuJ,OAAA,aAAA9K,MAAA,CAAAsG,OAAA;cACAwF,YAAA,GAAAF,OAAA,CAAA/K,KAAA,CAAAiK,OAAA;cAEA,IAAAgB,YAAA,IAAAA,YAAA;gBACA;gBACAA,YAAA,IAAAM,cAAA;kBACAC,QAAA;kBACAC,KAAA;kBACAC,MAAA;gBACA;cACA;gBACA;gBACAR,cAAA,yBAAA/L,MAAA,CAAAsG,OAAA;gBACA0F,cAAA,GAAAvM,SAAA,CAAAE,aAAA,CAAAoM,cAAA;gBAEA,IAAAC,cAAA;kBACAA,cAAA,CAAAI,cAAA;oBACAC,QAAA;oBACAC,KAAA;oBACAC,MAAA;kBACA;gBACA;kBACA;kBACA7O,UAAA;kBACAuO,SAAA,IAAA3F,OAAA,QAAA5I,UAAA;kBACA+B,SAAA,CAAAmI,QAAA;oBACAU,GAAA,EAAA2D,SAAA;oBACAI,QAAA;kBACA;gBACA;cACA;YAAA;cAAA,OAAAH,SAAA,CAAA3K,CAAA;UAAA;QAAA,GAAAsK,QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAW,OAAA,WAAAA,QAAAlG,OAAA;MAAA,IAAAmG,OAAA;MAAA,WAAAvM,kBAAA,CAAA/C,OAAA,mBAAAgD,aAAA,CAAAhD,OAAA,IAAAiD,CAAA,UAAAsM,UAAA;QAAA,WAAAvM,aAAA,CAAAhD,OAAA,IAAAqD,CAAA,WAAAmM,UAAA;UAAA,kBAAAA,UAAA,CAAAjM,CAAA;YAAA;cACA;cACA+L,OAAA,CAAAxO,OAAA,GAAAqI,OAAA;cAEAmG,OAAA,CAAApO,eAAA;cACA6L,QAAA,CAAA0C,IAAA,CAAAC,KAAA,CAAAC,QAAA;cACA5C,QAAA,CAAA6C,eAAA,CAAAF,KAAA,CAAAC,QAAA;;cAEA;cAAAH,UAAA,CAAAjM,CAAA;cAAA,OACA+L,OAAA,CAAAhH,UAAA,CAAAa,OAAA;YAAA;cAAA,OAAAqG,UAAA,CAAApL,CAAA;UAAA;QAAA,GAAAmL,SAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAM,sBAAA,WAAAA,uBAAA;MACA,UAAAhP,UAAA;;MAEA;MACA,aAAAC,OAAA;IACA;IAEA;AACA;AACA;IACAgP,KAAA,WAAAA,MAAA;MACA,KAAA5O,eAAA;MACA6L,QAAA,CAAA0C,IAAA,CAAAC,KAAA,CAAAC,QAAA;MACA5C,QAAA,CAAA6C,eAAA,CAAAF,KAAA,CAAAC,QAAA;IACA;IAEA;AACA;AACA;IACApH,KAAA,WAAAA,MAAAwH,EAAA;MACA,WAAAjE,OAAA,WAAAkE,OAAA;QAAA,OAAAxI,UAAA,CAAAwI,OAAA,EAAAD,EAAA;MAAA;IACA;EACA;EAEAE,OAAA,WAAAA,QAAA;IACA;IACAC,MAAA,CAAAC,gBAAA,gBAAA9N,YAAA;EACA;EAEA+N,aAAA,WAAAA,cAAA;IACA;IACA,KAAAhP,SAAA,CAAAiJ,KAAA;IACA,KAAA/I,aAAA,CAAA+I,KAAA;IACA,KAAA7I,cAAA,CAAA6I,KAAA;IACAG,YAAA,MAAAD,WAAA;IACA2F,MAAA,CAAAG,mBAAA,gBAAAhO,YAAA;EACA;AACA", "ignoreList": []}]}