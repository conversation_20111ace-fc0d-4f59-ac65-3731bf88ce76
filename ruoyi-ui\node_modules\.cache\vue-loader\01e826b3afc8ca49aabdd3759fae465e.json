{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\agentRoom.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\agentRoom.vue", "mtime": 1753950779107}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["agentRoom.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "agentRoom.vue", "sourceRoot": "src/views/bidOpeningHall", "sourcesContent": ["<template>\r\n\t<!-- 开标室主页面 -->\r\n\t<div>\r\n\t\t<!-- 头部组件，负责流程状态展示和切换 -->\r\n\t\t<BidHeadtwo ref=\"head\" @updateStatus=\"handleStatus\"></BidHeadtwo>\r\n\t\t<div class=\"bidOpeningHall\">\r\n\t\t\t<!-- 主体区域，包含流程节点内容 -->\r\n\t\t\t<el-card id=\"main\" class=\"box-card\">\r\n\t\t\t\t<!-- 平台当前时间与聊天室连接状态 -->\r\n\t\t\t\t<div style=\"height: 10px;\">\r\n\t\t\t\t\t<div style=\"padding: 5px 0 0 20px; float: left;\">平台当前时间：<span >{{ currentTime }}</span></div>\r\n\t\t\t\t\t<div style=\"padding: 5px 20px 0 0; float: right;\">\r\n\t\t\t\t\t\t连接状态：<span :style=\"`color:${isLink ? 'green' : 'red'}`\">{{\r\n\t\t\t\t\t\t\tisLink ? \"已连接\" : \"已断连，请刷新重连\"\r\n\t\t\t\t\t\t}}</span>\r\n\t\t\t\t\t</div></div>\r\n\t\t\t\t<!-- 根据流程节点动态渲染不同子组件 -->\r\n\t\t\t\t<ready ref=\"ready\" v-if=\"node == 'ready' && projectInfo\" :projectInfo=\"projectInfo\" @sendMessage=\"operateSend\"></ready>\r\n\t\t\t\t<publicity ref=\"publicity\" v-if=\"node == 'publicity'\" :projectInfo=\"projectInfo\" @sendMessage=\"operateSend\"></publicity>\r\n\t\t\t\t<decryption ref=\"decryption\" v-if=\"node == 'decryption' && projectInfo\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" @sendMessage=\"operateSend\"></decryption>\r\n\t\t\t\t<bidAnnouncement ref=\"bidAnnouncement\" v-if=\"node == 'bidAnnouncement'\" @sendMessage=\"operateSend\"></bidAnnouncement>\r\n\t\t\t\t<end ref=\"end\" v-if=\"node == 'end'\" @sendMessage=\"operateSend\"></end>\r\n\t\t\t</el-card>\r\n\t\t\t<!-- 聊天室侧边栏 -->\r\n\t\t\t<el-card class=\"box-card\" style=\"width: 15%;\">\r\n\t\t\t\t<div class=\"im\">\r\n\t\t\t\t\t<div class=\"im-title\">{{ userInfo.nickName }}</div>\r\n\t\t\t\t\t<!-- 聊天内容区域 -->\r\n\t\t\t\t\t<div ref=\"messagesContainer\" class=\"im-content\" :style=\"{height: syncedHeight }\">\r\n\t\t\t\t\t\t<div v-for=\"(itemc, indexc) in recordContent\" :key=\"indexc\">\r\n\t\t\t\t\t\t\t<div class=\"sysMessage\" v-if=\"itemc.type == 0\">\r\n\t\t\t\t\t\t\t\t<!-- 系统消息（可扩展） -->\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div v-else>\r\n\t\t\t\t\t\t\t\t<!-- 他人消息 -->\r\n\t\t\t\t\t\t\t\t<div class=\"word\" v-if=\"itemc.sendId !== userInfo.entId\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"message_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{anonymous? \"*******\":itemc.sendName }}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"info-content\">{{ itemc.content }}</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"message_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<!-- 自己消息 -->\r\n\t\t\t\t\t\t\t\t<div class=\"word-my\" v-else>\r\n\t\t\t\t\t\t\t\t\t<div class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"info-content\">{{ itemc.content }}</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"Sender_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<!-- 聊天输入与发送 -->\r\n\t\t\t\t\t<div class=\"im-operation\">\r\n\t\t\t\t\t\t<div style=\"margin-right:5px\">\r\n\t\t\t\t\t\t\t<el-input v-model=\"message\" placeholder=\"输入内容\"></el-input>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-button style=\"height: 36px;background: #176ADB;color:#fff\" @click=\"send\">发送</el-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</el-card>\r\n\t\t</div>\r\n\t\t<!-- 页脚 -->\r\n\t\t<Foot></Foot>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n// 引入各流程节点组件\r\nimport Ready from \"./agentComponent/ready.vue\";\r\nimport publicity from \"./agentComponent/publicity.vue\";\r\nimport decryption from \"./agentComponent/decryption.vue\";\r\nimport bidAnnouncement from \"./agentComponent/bidAnnouncement.vue\";\r\nimport end from \"./agentComponent/end.vue\";\r\n\r\n// 工具方法与API接口\r\nimport {\r\n\tformatDateOption,\r\n\tgetTodayStartWithDate,\r\n\tgetTodayEndWithDate,\r\n} from \"@/utils/index\";\r\nimport { bidInfo, chatHistory } from \"@/api/onlineBidOpening/info\";\r\nimport { getUserProfile } from \"@/api/system/user\";\r\nimport { getSystemTime } from \"@/api/bid/opening\";\r\n\r\nexport default {\r\n\tcomponents: { Ready, publicity, decryption, bidAnnouncement, end },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 当前流程节点\r\n\t\t\tnode: \"ready\",\r\n\t\t\t// 当前用户信息\r\n\t\t\tuserInfo: {},\r\n\t\t\t// 项目信息\r\n\t\t\tprojectInfo: null,\r\n\t\t\t// 接口基础地址\r\n\t\t\tbaseUrl: process.env.VUE_APP_BASE_API,\r\n\t\t\t// 聊天输入内容\r\n\t\t\tmessage: \"\",\r\n\t\t\t// 文本内容（用于日志等）\r\n\t\t\ttext_content: \"\",\r\n\t\t\t// websocket 实例\r\n\t\t\tws: null,\r\n\t\t\t// 是否匿名（根据流程节点控制）\r\n\t\t\tanonymous: true,\r\n\t\t\t// 聊天记录内容\r\n\t\t\trecordContent: [\r\n\t\t\t\t// 示例数据，实际会被接口数据覆盖\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 1,\r\n\t\t\t\t\tcontent: \"Nice to meet you.\",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:00:00\",\r\n\t\t\t\t\tSender: \"张三\",\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 2,\r\n\t\t\t\t\tcontent: \"Nice to meet you.too\",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:01:00\",\r\n\t\t\t\t\tSender: \"李四\",\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 2,\r\n\t\t\t\t\tcontent: \"How are you? \",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:02:00\",\r\n\t\t\t\t\tSender: \"李四\",\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 1,\r\n\t\t\t\t\tcontent: \"I'am fine,Thank you.\",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:03:00\",\r\n\t\t\t\t\tSender: \"张三\",\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t\t// websocket连接状态\r\n\t\t\tisLink: false,\r\n\t\t\t// 聊天内容区高度（与主卡片同步）\r\n\t\t\tsyncedHeight: '450px', // 初始高度\r\n\t\t\t// 平台当前时间\r\n\t\t\tcurrentTime:null,\r\n\t\t\t// 时间更新定时器\r\n\t\t\ttimeInterval: null\r\n\t\t};\r\n\t},\r\n\twatch: {\r\n\t\t// 监听流程节点变化，动态调整聊天区高度\r\n\t\tnode: {\r\n\t\t\thandler() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tvar element = document.getElementById('main');\r\n\t\t\t\t\tconsole.log('element.clientHeight', element.offsetHeight);\r\n\t\t\t\t\tthis.syncedHeight = element.offsetHeight - 120 + 'px'\r\n\t\t\t\t}, 10);\r\n\r\n\t\t\t},\r\n\t\t\tdeep: true\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 初始化，获取项目信息和用户信息\r\n\t\tinit() {\r\n\t\t\t// 获取开标项目信息\r\n\t\t\tconst promise1 = bidInfo({\r\n\t\t\t\tbidOpeningTime: getTodayStartWithDate(),\r\n\t\t\t\tbidOpeningEndTime: getTodayEndWithDate(),\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t}).then((response) => {\r\n\t\t\t\tif (response.code == 200) {\r\n\t\t\t\t\tthis.projectInfo = response.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$modal.msgwarning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// 获取用户信息\r\n\t\t\tconst promise2 = getUserProfile().then((response) => {\r\n\t\t\t\tthis.userInfo = response.data;\r\n\t\t\t});\r\n\r\n\t\t\t// 两个接口都完成后，建立websocket连接\r\n\t\t\tPromise.all([promise1, promise2]).then((result) => {\r\n\t\t\t\tthis.join();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 处理流程节点切换\r\n\t\thandleStatus(data) {\r\n\t\t\t// 根据流程状态判断是否匿名\r\n\t\t\tthis.anonymous = this.$store.getters.agentBidOpenStatus >= 2 ? false : true;\r\n\t\t\tswitch (data) {\r\n\t\t\t\tcase \"签到\":\r\n\t\t\t\t\tthis.node = \"signIn\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"开标准备\":\r\n\t\t\t\t\tthis.node = \"ready\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"投标人公示\":\r\n\t\t\t\t\tthis.node = \"publicity\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"标书解密\":\r\n\t\t\t\t\tthis.node = \"decryption\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"唱标\":\r\n\t\t\t\t\tthis.node = \"bidAnnouncement\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"开标结束\":\r\n\t\t\t\t\tthis.node = \"end\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 节点更新通知，刷新头部状态\r\n\t\tupdateStatus() {\r\n\t\t\tthis.$refs.head.getBidStatus();\r\n\t\t},\r\n\t\t// 格式化开标时间显示 年-月-日\r\n\t\tformatBidOpeningTime(time) {\r\n\t\t\treturn formatDateOption(time, \"date\");\r\n\t\t},\r\n\t\t// 格式化开标时间显示 时-分-秒\r\n\t\tformatBidOpeningTimeTwo(time) {\r\n\t\t\treturn formatDateOption(time, \"time\");\r\n\t\t},\r\n\r\n\t\t// 建立websocket连接，处理心跳、消息、断线重连等\r\n\t\tjoin() {\r\n\t\t\tlet socketUrl = this.baseUrl.replace(\"http\", \"ws\");\r\n\t\t\tconsole.log(\"socketUrl\", socketUrl);\r\n\t\t\t// 拼接websocket地址\r\n\t\t\tthis.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n\t\t\tconst wsurl = this.url;\r\n\t\t\tthis.ws = new WebSocket(wsurl);\r\n\t\t\tconst self = this;\r\n\t\t\t// 心跳检测函数，防止连接超时断开\r\n\t\t\tconst ws_heartCheck = {\r\n\t\t\t\ttimeout: 5000, // 5秒\r\n\t\t\t\ttimeoutObj: null,\r\n\t\t\t\tserverTimeoutObj: null,\r\n\t\t\t\tstart: function () {\r\n\t\t\t\t\tthis.timeoutObj = setTimeout(() => {\r\n\t\t\t\t\t\t// 发送心跳包\r\n\t\t\t\t\t\tself.ws.send(\"ping\");\r\n\t\t\t\t\t\tthis.serverTimeoutObj = setTimeout(() => {\r\n\t\t\t\t\t\t\tself.ws.close(); // 超时未响应则断开\r\n\t\t\t\t\t\t}, this.timeout);\r\n\t\t\t\t\t}, this.timeout);\r\n\t\t\t\t},\r\n\t\t\t\treset: function () {\r\n\t\t\t\t\tclearTimeout(this.timeoutObj); // 重置心跳\r\n\t\t\t\t\tclearTimeout(this.serverTimeoutObj);\r\n\t\t\t\t\tthis.start();\r\n\t\t\t\t},\r\n\t\t\t\tstop: function () {\r\n\t\t\t\t\tclearTimeout(this.timeoutObj);\r\n\t\t\t\t\tclearTimeout(this.serverTimeoutObj);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// 连接打开\r\n\t\t\tthis.ws.onopen = function (event) {\r\n\t\t\t\tws_heartCheck.start();\r\n\t\t\t\tself.text_content = self.text_content + \"已经打开开标室连接!\" + \"\\n\";\r\n\t\t\t\tself.isLink = true;\r\n\t\t\t\tconsole.log(self.text_content);\r\n\t\t\t};\r\n\t\t\t// 收到消息\r\n\t\t\tthis.ws.onmessage = function (event) {\r\n\t\t\t\tconsole.log(event.data);\r\n\t\t\t\tif (event.data == \"ping\") {\r\n\t\t\t\t\tws_heartCheck.reset(); // 心跳包\r\n\t\t\t\t} else if (event.data == \"连接成功\") {\r\n\t\t\t\t} else if (event.data == \"signIn\") {\r\n\t\t\t\t\tself.$refs.publicity.initdataList();\r\n\t\t\t\t} else if (event.data == \"supDecrytion\") {\r\n\t\t\t\t\tself.$refs.decryption.initdataList();\r\n\t\t\t\t} else if (\r\n\t\t\t\t\tevent.data == \"ready\" ||\r\n\t\t\t\t\tevent.data == \"bidPublicity\" ||\r\n\t\t\t\t\tevent.data == \"decryption\" ||\r\n\t\t\t\t\tevent.data == \"nextStep\" ||\r\n\t\t\t\t\tevent.data == \"bidAnnouncement\" ||\r\n\t\t\t\t\tevent.data == \"end\" ||\r\n\t\t\t\t\tevent.data == \"flowLabel\"\r\n\t\t\t\t) {\r\n\t\t\t\t\tself.updateStatus();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tself.initChat();\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// 连接关闭，自动重连\r\n\t\t\tthis.ws.onclose = function (event) {\r\n\t\t\t\tself.text_content = self.text_content + \"已经关闭开标室连接!\" + \"\\n\";\r\n\t\t\t\tself.isLink = false;\r\n\t\t\t\tclearTimeout(ws_heartCheck.timeoutObj);\r\n\t\t\t\tclearTimeout(ws_heartCheck.serverTimeoutObj);\r\n\t\t\t\t//断开后自动重连\r\n\t\t\t\tws_heartCheck.stop();\r\n\t\t\t\tself.join();\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 主动断开websocket连接\r\n\t\texit() {\r\n\t\t\tif (this.ws) {\r\n\t\t\t\tthis.ws.close();\r\n\t\t\t\tthis.ws = null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 发送消息（自己输入）\r\n\t\tsend() {\r\n\t\t\tif (this.ws) {\r\n\t\t\t\tthis.ws.send(this.message);\r\n\t\t\t\tthis.message = \"\";\r\n\t\t\t\tthis.scrollToBottom();\r\n\t\t\t} else {\r\n\t\t\t\talert(\"未连接到开标室服务器\");\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 发送消息（子组件调用）\r\n\t\toperateSend(message) {\r\n\t\t\tif (this.ws) {\r\n\t\t\t\tthis.ws.send(message);\r\n\t\t\t} else {\r\n\t\t\t\talert(\"未连接到开标室服务器\");\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 初始化聊天记录\r\n\t\tinitChat() {\r\n\t\t\tchatHistory(this.$route.query.projectId).then((response) => {\r\n\t\t\t\tif (response.code == 200) {\r\n\t\t\t\t\tthis.recordContent = response.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.recordContent = [];\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 聊天内容滚动到底部\r\n\t\tscrollToBottom() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst container = this.$refs.messagesContainer;\r\n\t\t\t\tcontainer.scrollTop = container.scrollHeight;\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 获取并定时更新时间\r\n\t\tupdateTime() {\r\n\t\t\t// 清理之前的定时器\r\n\t\t\tif (this.timeInterval) {\r\n\t\t\t\tclearInterval(this.timeInterval);\r\n\t\t\t}\r\n\r\n\t\t\tvar _this = this;\r\n\t\t\tgetSystemTime().then((result) => {\r\n\t\t\t\tif(result.code==200){\r\n\t\t\t\t\tvar ct = new Date(result.data);\r\n\t\t\t\t\tthis.timeInterval = setInterval(function(){\r\n\t\t\t\t\t\tct.setSeconds(ct.getSeconds() + 1);\r\n\t\t\t\t\t\t_this.currentTime = formatDateOption(ct, \"cdatetime\");\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t_this.updateStatus();\r\n\t\t\t\t\t\t_this.initChat();\r\n\r\n\t\t\t\t\t}, 1000); // 每秒更新时间\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\t// 生命周期钩子\r\n\tcreated() { },\r\n\tmounted() {\r\n\t\tthis.init();\r\n\t\tthis.initChat();\r\n\r\n\t\tthis.updateTime();\r\n\t},\r\n\tupdated() {\r\n\t\tthis.scrollToBottom();\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.timeInterval) {\r\n\t\t\tclearInterval(this.timeInterval);\r\n\t\t}\r\n\t\t// 断开WebSocket连接\r\n\t\tthis.exit();\r\n\t},\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// 覆盖el-card body内边距\r\n::v-deep .el-card__body {\r\n\tpadding: 0;\r\n}\r\n</style>\r\n\r\n<style scoped lang=\"scss\">\r\n// 聊天消息激活态\r\n.active {\r\n\tbackground-color: rgba(149, 250, 190, 1);\r\n}\r\n// 开标室主布局\r\n.bidOpeningHall {\r\n\tposition: relative;\r\n\tbackground-color: #f5f5f5;\r\n\tdisplay: flex;\r\n\tflex-wrap: nowrap;\r\n\tjustify-content: center;\r\n\talign-content: flex-start;\r\n\talign-items: flex-start;\r\n}\r\n// 主体卡片样式\r\n.box-card {\r\n\tmin-height: 600px;\r\n\twidth: 50%;\r\n\tmargin: 15px 5px;\r\n}\r\n// 聊天室整体样式\r\n.im {\r\n\t.im-title {\r\n\t\twidth: 100%;\r\n\t\theight: 50px;\r\n\t\tbackground: #176adb;\r\n\r\n\t\tfont-weight: 500;\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #ffffff;\r\n\t\tletter-spacing: 0;\r\n\r\n\t\tline-height: 50px;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.im-content {\r\n\t\tmargin: 10px;\r\n\t\tbackground: #f5f5f5;\r\n\t\theight: 450px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\t.im-operation {\r\n\t\tdisplay: flex;\r\n\t\tmargin: 0 10px;\r\n\t\tmargin-bottom: 10px;\r\n\t\toverflow: auto;\r\n\t}\r\n}\r\n// 聊天内容区样式\r\n.im-content {\r\n\t// 他人消息气泡\r\n\t.word {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 20px;\r\n\r\n\t\timg {\r\n\t\t\twidth: 40px;\r\n\t\t\theight: 40px;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t\t.info {\r\n\t\t\twidth: 47%;\r\n\t\t\tmargin-left: 10px;\r\n\t\t\t.Sender_time {\r\n\t\t\t\tpadding-right: 12px;\r\n\t\t\t\tpadding-top: 5px;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\theight: 20px;\r\n\t\t\t}\r\n\t\t\t.message_time {\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\theight: 20px;\r\n\t\t\t\tline-height: 20px;\r\n\t\t\t\tmargin-top: -5px;\r\n\t\t\t\tmargin-top: 5px;\r\n\t\t\t}\r\n\t\t\t.info-content {\r\n\t\t\t\tword-break: break-all;\r\n\t\t\t\t// max-width: 45%;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tpadding: 10px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-top: 8px;\r\n\t\t\t\tbackground: #dbdbdb;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t}\r\n\t\t\t//小三角形\r\n\t\t\t.info-content::before {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: -8px;\r\n\t\t\t\ttop: 8px;\r\n\t\t\t\tcontent: \"\";\r\n\t\t\t\tborder-right: 10px solid #dbdbdb;\r\n\t\t\t\tborder-top: 8px solid transparent;\r\n\t\t\t\tborder-bottom: 8px solid transparent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// 自己消息气泡\r\n\t.word-my {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t\tmargin-bottom: 20px;\r\n\t\timg {\r\n\t\t\twidth: 40px;\r\n\t\t\theight: 40px;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t\t.info {\r\n\t\t\twidth: 90%;\r\n\t\t\t// margin-left: 10px;\r\n\t\t\ttext-align: right;\r\n\t\t\t// position: relative;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: flex-end;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tflex-direction: column;\r\n\t\t\t.info-content {\r\n\t\t\t\tword-break: break-all;\r\n\t\t\t\tmax-width: 45%;\r\n\t\t\t\tpadding: 10px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\t// float: right;\r\n\t\t\t\tmargin-right: 10px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-top: 8px;\r\n\t\t\t\tbackground: #a3c3f6;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t}\r\n\t\t\t.Sender_time {\r\n\t\t\t\tpadding-right: 12px;\r\n\t\t\t\tpadding-top: 5px;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\theight: 20px;\r\n\t\t\t}\r\n\t\t\t//小三角形\r\n\t\t\t.info-content::after {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: -8px;\r\n\t\t\t\ttop: 8px;\r\n\t\t\t\tcontent: \"\";\r\n\t\t\t\tborder-left: 10px solid #a3c3f6;\r\n\t\t\t\tborder-top: 8px solid transparent;\r\n\t\t\t\tborder-bottom: 8px solid transparent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n"]}]}