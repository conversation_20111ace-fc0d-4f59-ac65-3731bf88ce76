{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\components\\pdfView\\index-improved.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\components\\pdfView\\index-improved.vue", "mtime": 1753956960909}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index-improved.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index-improved.vue", "sourceRoot": "src/components/pdfView", "sourcesContent": ["<!--\r\n  PDF文件查看器组件 - 增强版\r\n  功能：\r\n  1. 支持PDF文件的在线预览\r\n  2. 提供页面导航功能（上一页、下一页、首页）\r\n  3. 支持PDF页面放大查看\r\n  4. 自动计算当前页码\r\n  5. 虚拟滚动和懒加载，防止大文件浏览器卡死\r\n-->\r\n<template>\r\n\t<div>\r\n\t\t<!-- PDF文件加载进度条 -->\r\n\t\t<div v-if=\"isLoading\" class=\"pdf-loading-container\">\r\n\t\t\t<div class=\"loading-content\">\r\n\t\t\t\t<div class=\"loading-text\">正在加载PDF文件...</div>\r\n\t\t\t\t<el-progress\r\n\t\t\t\t\t:percentage=\"loadingProgress\"\r\n\t\t\t\t\t:stroke-width=\"8\"\r\n\t\t\t\t\t:show-text=\"true\"\r\n\t\t\t\t\t:format=\"formatProgress\"\r\n\t\t\t\t\tstatus=\"success\"\r\n\t\t\t\t\tclass=\"loading-progress\">\r\n\t\t\t\t</el-progress>\r\n\t\t\t\t<div class=\"loading-detail\">{{ loadingDetail }}</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t<!-- PDF主容器：固定高度600px，支持滚动查看多页PDF -->\r\n\t\t<div ref=\"pdfContainer\" id=\"pdf-container\" style=\"width:100%;overflow: auto;height: 600px;\" v-show=\"!isLoading\" @scroll=\"handleScroll\">\r\n\t\t\t<!-- 导航按钮栏：粘性定位，始终显示在顶部 -->\r\n\t\t\t<div style=\"position:sticky;top:0;z-index:99;background:#fff;padding:10px 0;border-bottom:1px solid #eee;\">\r\n\t\t\t\t<!-- 上一页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"skipPage(curPage - 1)\">上一页</el-button>\r\n\t\t\t\t<!-- 首页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"skipPage(1)\">首页</el-button>\r\n\t\t\t\t<!-- 下一页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"skipPage(curPage + 1)\">下一页</el-button>\r\n\t\t\t\t<!-- 放大当前页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"enlarge(curPage)\">放大</el-button>\r\n\t\t\t\t<!-- 显示当前页码 -->\r\n\t\t\t\t<span style=\"margin-left: 20px; color: #666;\">\r\n\t\t\t\t\t第 {{ curPage }}/{{ totalPages }} 页\r\n\t\t\t\t</span>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<!-- 直接渲染所有页面，取消虚拟滚动 -->\r\n\t\t\t<div style=\"padding: 10px;\">\r\n\t\t\t\t<div\r\n\t\t\t\t\tv-for=\"pageNum in totalPages\"\r\n\t\t\t\t\t:key=\"pageNum\"\r\n\t\t\t\t\tclass=\"pdf-page\"\r\n\t\t\t\t\tstyle=\"margin-bottom: 20px; text-align: center;\">\r\n\t\t\t\t\t<canvas\r\n\t\t\t\t\t\t:ref=\"`canvas-${pageNum}`\"\r\n\t\t\t\t\t\t:style=\"{ maxWidth: '100%', height: 'auto', border: '1px solid #ddd', borderRadius: '4px' }\"\r\n\t\t\t\t\t\t:data-page=\"pageNum\">\r\n\t\t\t\t\t</canvas>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t<!-- PDF放大查看模态框 -->\r\n\t\t<div v-show=\"display_enlarge\" class=\"canvas-enlarge\">\r\n\t\t\t<!-- 关闭按钮：位于右上角 -->\r\n\t\t\t<div style=\"position: absolute; top: 20px; right: 20px; z-index: 1000;\">\r\n\t\t\t\t<el-button type=\"danger\" icon=\"el-icon-close\" circle @click=\"close()\">\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<!-- 放大后的PDF渲染容器：宽度70%，高度56.25rem，支持滚动 -->\r\n\t\t\t<div id=\"enlarge\" style=\"width:70%;height:56.25rem;overflow: auto;\">\r\n\t\t\t\t<!-- 放大模式下的PDF页面渲染 -->\r\n\t\t\t\t<template v-if=\"totalPages > 0\">\r\n\t\t\t\t\t<!-- 遍历每一页PDF，为每页创建放大版本的canvas元素 -->\r\n\t\t\t\t\t<div v-for=\"pageNum in getEnlargeVisiblePages()\" :key=\"`enlarge-${pageNum}`\">\r\n\t\t\t\t\t\t<canvas\r\n\t\t\t\t\t\t\t:ref=\"`enlarge-canvas-${pageNum}`\"\r\n\t\t\t\t\t\t\t:style=\"{ width: '100%', marginBottom: '20px' }\"\r\n\t\t\t\t\t\t\t:data-page=\"pageNum\">\r\n\t\t\t\t\t\t</canvas>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</template>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n// 导入PDF.js库，用于PDF文件的解析和渲染\r\nimport pdfjsLib from \"pdfjs-dist\";\r\nimport axios from 'axios';\r\n// 获取API基础URL，用于拼接完整的PDF文件URL\r\nconst baseURL = process.env.VUE_APP_BASE_API;\r\n\r\nexport default {\r\n\t// 组件名称\r\n\tname: 'PdfViewImproved',\r\n\t\r\n\t// 组件属性定义\r\n\tprops: {\r\n\t\t// PDF文件URL，父组件传入的PDF文件路径\r\n\t\tpdfurl: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"\",\r\n\t\t},\r\n\t\t// 唯一标识符，用于区分多个PDF查看器实例，确保DOM元素ID不冲突\r\n\t\tuni_key: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"a\",\r\n\t\t},\r\n\t\t// 每页高度（像素），用于虚拟滚动计算\r\n\t\tpageHeight: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 800\r\n\t\t},\r\n\t\t// 缓冲区大小（页数）\r\n\t\tbufferSize: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 3\r\n\t\t}\r\n\t},\r\n\t\r\n\t// 组件数据\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 完整的PDF文件URL（包含baseURL）\r\n\t\t\turl: \"\",\r\n\t\t\t// PDF文档对象\r\n\t\t\tpdfDocument: null,\r\n\t\t\t// 总页数\r\n\t\t\ttotalPages: 0,\r\n\t\t\t// 当前页码\r\n\t\t\tcurPage: 1,\r\n\t\t\t// 是否正在加载PDF\r\n\t\t\tisLoading: false,\r\n\t\t\t// 加载进度百分比（0-100）\r\n\t\t\tloadingProgress: 0,\r\n\t\t\t// 加载详细信息\r\n\t\t\tloadingDetail: \"\",\r\n\t\t\t// 是否显示放大模式\r\n\t\t\tdisplay_enlarge: false,\r\n\t\t\t// 所有页面是否渲染完成\r\n\t\t\tallPagesRendered: false,\r\n\t\t\t\r\n\t\t\t// 页面缓存\r\n\t\t\tpageCache: new Map(),\r\n\t\t\trenderedPages: new Set(),\r\n\t\t\trenderingPages: new Set(),\r\n\t\t\t\r\n\t\t\t// 内存管理\r\n\t\t\tmaxCacheSize: 50, // 最大缓存页数\r\n\t\t};\r\n\t},\r\n\t\r\n\tcomputed: {\r\n\t\t// 移除虚拟滚动相关计算属性\r\n\t},\r\n\t\r\n\twatch: {\r\n\t\t// 监听PDF URL变化\r\n\t\tpdfurl: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\tif (newVal != null && newVal !== undefined) {\r\n\t\t\t\t\tthis.url = newVal;\r\n\t\t\t\t\tconsole.log(\"this.url\",this.url)\r\n\t\t\t\t\tif (this.url !== \"\") {\r\n\t\t\t\t\t\tthis.url = baseURL + this.url;\r\n\t\t\t\t\t\t// 每次切换PDF都重置状态并回到顶部\r\n\t\t\t\t\t\tthis.resetState();\r\n\t\t\t\t\t\tthis.loadPDF();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t\t// 移除visiblePages的watch监听器\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\t/**\r\n\t\t * 处理窗口大小变化\r\n\t\t */\r\n\t\thandleResize() {\r\n\t\t\tconst container = this.$el.querySelector(\"#pdf-container\");\r\n\t\t\tif (container) {\r\n\t\t\t\tthis.containerHeight = container.clientHeight;\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 格式化进度条显示文本\r\n\t\t */\r\n\t\tformatProgress(percentage) {\r\n\t\t\treturn `${percentage}%`;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 加载PDF文档 - 支持分片加载\r\n\t\t */\r\n\t\tasync loadPDF() {\r\n\t\t\ttry {\r\n\t\t\t\t// 立即滚动到顶部，在加载开始前\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tconst container = this.$refs.pdfContainer;\r\n\t\t\t\t\tif (container) {\r\n\t\t\t\t\t\tcontainer.scrollTop = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tthis.loadingProgress = 0;\r\n\t\t\t\tthis.loadingDetail = \"正在初始化PDF加载器...\";\r\n\t\t\t\t\r\n\t\t\t\t// 重置状态\r\n\t\t\t\tthis.resetState();\r\n\t\t\t\t\r\n\t\t\t\t// 配置PDF.js - 修复worker路径问题\r\n\t\t\t\tthis.configurePdfWorker();\r\n\t\t\t\t\r\n\t\t\t\t// 检测是否使用分片加载\r\n\t\t\t\tconst useStreaming = await this.shouldUseStreaming();\r\n\t\t\t\tconsole.log(\"检测是否使用分片加载\",useStreaming)\r\n\t\t\t\tif (useStreaming) {\r\n\t\t\t\t\tawait this.loadPDFWithStreaming();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tawait this.loadPDFStandard();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error loading PDF:\", error);\r\n\t\t\t\tthis.loadingDetail = \"PDF加载失败：\" + error.message;\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 配置PDF.js worker\r\n\t\t */\r\n\t\tconfigurePdfWorker() {\r\n\t\t\ttry {\r\n\t\t\t\tconst worker = require('pdfjs-dist/build/pdf.worker.min.js');\r\n\t\t\t\tpdfjsLib.GlobalWorkerOptions.workerSrc = worker;\r\n\t\t\t} catch (e) {\r\n\t\t\t\t// 如果require失败，使用CDN\r\n\t\t\t\tpdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 判断是否使用分片加载\r\n\t\t * 直接传入完整文件路径\r\n\t\t */\r\n\t\tasync shouldUseStreaming() {\r\n\t\t\ttry {\r\n\t\t\t\t// 从完整URL中提取文件路径部分\r\n\t\t\t\tconst filePath = this.extractFilePath(this.url);\r\n\t\t\t\tconst response = await axios.get(`${baseURL}/common/pdf/info`, {\r\n\t\t\t\t\tparams: { filePath }\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log(response)\r\n\t\t\t\t// 文件大于5MB时使用分片加载\r\n\t\t\t\treturn response.data.fileSize > 5 * 1024 * 1024;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.warn(\"无法获取文件信息，使用标准加载模式\", error);\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 提取文件路径（从完整URL中提取相对路径）\r\n\t\t */\r\n\t\textractFilePath(url) {\r\n\t\t\t// 从完整URL中提取相对路径部分\r\n\t\t\tconst baseURL = process.env.VUE_APP_BASE_API;\r\n\t\t\tif (url.startsWith(baseURL)) {\r\n\t\t\t\treturn url.substring(baseURL.length);\r\n\t\t\t}\r\n\t\t\t// 如果URL已经是相对路径，直接返回\r\n\t\t\treturn url;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 提取文件名（保留原有方法用于兼容）\r\n\t\t */\r\n\t\textractFileName(url) {\r\n\t\t\tconst parts = url.split('/');\r\n\t\t\treturn parts[parts.length - 1];\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 分片加载PDF - 真正的HTTP Range分片加载\r\n\t\t * 使用后端/stream接口，支持断点续传和按需加载\r\n\t\t */\r\n\t\tasync loadPDFWithStreaming() {\r\n\t\t\tthis.loadingProgress = 10;\r\n\t\t\tthis.loadingDetail = \"正在初始化分片加载模式...\";\r\n\t\t\t\r\n\t\t\tconst filePath = this.extractFilePath(this.url);\r\n\t\t\tconst streamingUrl = `${baseURL}/common/pdf/stream?filePath=${encodeURIComponent(filePath)}`;\r\n\t\t\t\r\n\t\t\tconsole.log(\"🚀 使用分片加载模式，URL:\", streamingUrl);\r\n\t\t\t\r\n\t\t\t// 分片加载专用配置 - 真正的Range分片\r\n\t\t\tconst loadingTask = pdfjsLib.getDocument({\r\n\t\t\t\turl: streamingUrl,\r\n\t\t\t\trangeChunkSize: 32 * 1024,    // 32KB 更小分片，网络容错更强\r\n\t\t\t\tdisableAutoFetch: true,       // 关键：禁止自动获取，只加载需要的部分\r\n\t\t\t\tdisableStream: false,         // 启用流式传输\r\n\t\t\t\tdisableRange: false,          // 关键：启用Range请求，支持断点续传\r\n\t\t\t\tisEvalSupported: false,       // 安全考虑\r\n\t\t\t\tuseWorkerFetch: true,         // 使用worker处理网络请求\r\n\t\t\t\tmaxImageSize: 10 * 1024 * 1024, // 限制图片大小，防止内存溢出\r\n\t\t\t\tcMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.16.105/cmaps/', // 字体映射\r\n\t\t\t\tcMapPacked: true,\r\n\t\t\t\thttpHeaders: {\r\n\t\t\t\t\t'Accept': 'application/pdf',\r\n\t\t\t\t\t'Cache-Control': 'no-cache',\r\n\t\t\t\t\t'Pragma': 'no-cache',\r\n\t\t\t\t},\r\n\t\t\t\tonProgress: (progressData) => {\r\n\t\t\t\t\t// 分片加载的进度更精确，按块显示\r\n\t\t\t\t\tconst percent = Math.round((progressData.loaded / progressData.total) * 100);\r\n\t\t\t\t\tthis.handleProgress(progressData);\r\n\t\t\t\t\tif (progressData.loaded < progressData.total) {\r\n\t\t\t\t\t\tthis.loadingDetail = `📦 分片下载中... ${percent}% (${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB)`;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.loadingDetail = `✅ 分片下载完成，正在解析...`;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.pdfDocument = await loadingTask.promise;\r\n\t\t\tthis.totalPages = this.pdfDocument.numPages;\r\n\t\t\t\r\n\t\t\tthis.loadingProgress = 100;\r\n\t\t\tthis.loadingDetail = `🎉 PDF分片加载完成！共${this.totalPages}页，内存占用优化`;\r\n\t\t\t\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t// 分片模式下使用按需加载策略\r\n\t\t\t\tthis.startStreamingOptimizedRendering();\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 标准加载模式 - 传统的完整文件下载\r\n\t\t * 直接下载整个PDF文件到内存，适合小文件\r\n\t\t */\r\n\t\tasync loadPDFStandard() {\r\n\t\t\tthis.loadingProgress = 10;\r\n\t\t\tthis.loadingDetail = \"正在标准加载模式...\";\r\n\t\t\t\r\n\t\t\tconsole.log(\"使用标准加载模式，URL:\", this.url);\r\n\t\t\t\r\n\t\t\t// 标准加载专用配置 - 一次性下载整个文件\r\n\t\t\tconst loadingTask = pdfjsLib.getDocument({\r\n\t\t\t\turl: this.url,\r\n\t\t\t\trangeChunkSize: 65536 * 16, // 256KB 大块，减少请求次数\r\n\t\t\t\tdisableAutoFetch: false,    // 允许自动获取\r\n\t\t\t\tdisableStream: true,       // 禁用流式，一次性加载\r\n\t\t\t\tdisableRange: true,        // 禁用Range请求，强制完整下载\r\n\t\t\t\tdisableWorker: false,      // 启用worker处理\r\n\t\t\t\tisEvalSupported: false,\r\n\t\t\t\tuseWorkerFetch: false,\r\n\t\t\t\thttpHeaders: {\r\n\t\t\t\t\t'Accept': 'application/pdf',\r\n\t\t\t\t},\r\n\t\t\t\tonProgress: (progressData) => {\r\n\t\t\t\t\t// 标准加载显示整体进度\r\n\t\t\t\t\tthis.handleProgress(progressData);\r\n\t\t\t\t\tthis.loadingDetail = `完整下载中... ${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB`;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.pdfDocument = await loadingTask.promise;\r\n\t\t\tthis.totalPages = this.pdfDocument.numPages;\r\n\t\t\t\r\n\t\t\tthis.loadingProgress = 100;\r\n\t\t\tthis.loadingDetail = `PDF标准加载完成！共${this.totalPages}页`;\r\n\t\t\t\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t// 标准模式下使用渐进式渲染\r\n\t\t\t\tthis.startOptimizedRendering();\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 处理加载进度\r\n\t\t */\r\n\t\thandleProgress(progressData) {\r\n\t\t\tif (progressData.total > 0) {\r\n\t\t\t\tconst progress = Math.round((progressData.loaded / progressData.total) * 80) + 10;\r\n\t\t\t\tthis.loadingProgress = Math.min(progress, 99);\r\n\t\t\t\tthis.loadingDetail = `正在下载... ${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB`;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 开始优化渲染（标准模式）\r\n\t\t */\r\n\t\tasync startOptimizedRendering() {\r\n\t\t\tif (!this.pdfDocument || this.totalPages === 0) return;\r\n\t\t\t\r\n\t\t\tconsole.log(`开始优化渲染 ${this.totalPages} 页PDF...`);\r\n\t\t\t\r\n\t\t\t// 分批渲染，避免阻塞UI\r\n\t\t\tconst initialPages = Math.min(5, this.totalPages);\r\n\t\t\tfor (let i = 1; i <= initialPages; i++) {\r\n\t\t\t\tawait this.renderPage(i);\r\n\t\t\t\tawait this.sleep(10);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 延迟加载剩余页面\r\n\t\t\tthis.scheduleLazyRendering();\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 分片模式下的优化渲染（按需加载策略）\r\n\t\t */\r\n\t\tasync startStreamingOptimizedRendering() {\r\n\t\t\tif (!this.pdfDocument || this.totalPages === 0) return;\r\n\t\t\t\r\n\t\t\tconsole.log(`开始分片模式优化渲染 ${this.totalPages} 页PDF...`);\r\n\t\t\t\r\n\t\t\t// 分片模式下使用更激进的按需加载策略\r\n\t\t\tconst initialPages = Math.min(3, this.totalPages);\r\n\t\t\tfor (let i = 1; i <= initialPages; i++) {\r\n\t\t\t\tawait this.renderPage(i);\r\n\t\t\t\tawait this.sleep(5);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 分片模式下使用延迟加载策略，减少初始内存占用\r\n\t\t\tthis.scheduleStreamingLazyRendering();\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 延迟加载剩余页面（标准模式）\r\n\t\t */\r\n\t\tscheduleLazyRendering() {\r\n\t\t\tconst remainingPages = [];\r\n\t\t\tfor (let i = 6; i <= this.totalPages; i++) {\r\n\t\t\t\tremainingPages.push(i);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst renderNextPage = async () => {\r\n\t\t\t\tif (remainingPages.length === 0) return;\r\n\t\t\t\t\r\n\t\t\t\tconst pageNum = remainingPages.shift();\r\n\t\t\t\tif (!this.renderedPages.has(pageNum)) {\r\n\t\t\t\t\tawait this.renderPage(pageNum);\r\n\t\t\t\t\tawait this.sleep(20);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 继续渲染下一页\r\n\t\t\t\tsetTimeout(renderNextPage, 100);\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 延迟开始渲染剩余页面\r\n\t\t\tsetTimeout(renderNextPage, 1000);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 分片模式下的延迟加载策略（更激进的按需加载）\r\n\t\t */\r\n\t\tscheduleStreamingLazyRendering() {\r\n\t\t\tconst remainingPages = [];\r\n\t\t\tfor (let i = 4; i <= this.totalPages; i++) {\r\n\t\t\t\tremainingPages.push(i);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst renderNextPage = async () => {\r\n\t\t\t\tif (remainingPages.length === 0) {\r\n\t\t\t\t\tthis.checkAllPagesRendered();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 分片模式下更智能的加载策略：优先加载用户附近页面\r\n\t\t\t\tconst nearbyPages = this.getNearbyUnrenderedPages();\r\n\t\t\t\tif (nearbyPages.length > 0) {\r\n\t\t\t\t\tconst pageNum = nearbyPages[0];\r\n\t\t\t\t\tif (!this.renderedPages.has(pageNum) && !this.renderingPages.has(pageNum)) {\r\n\t\t\t\t\t\tawait this.renderPage(pageNum);\r\n\t\t\t\t\t\tawait this.sleep(50);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (remainingPages.length > 0) {\r\n\t\t\t\t\t// 回退到顺序加载\r\n\t\t\t\t\tconst pageNum = remainingPages.shift();\r\n\t\t\t\t\tif (!this.renderedPages.has(pageNum) && !this.renderingPages.has(pageNum)) {\r\n\t\t\t\t\t\tawait this.renderPage(pageNum);\r\n\t\t\t\t\t\tawait this.sleep(30);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 继续渲染下一页，分片模式下间隔更短\r\n\t\t\t\tsetTimeout(renderNextPage, 200);\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 延迟开始渲染剩余页面，分片模式下延迟更短\r\n\t\t\tsetTimeout(renderNextPage, 300);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 获取当前页面附近的未渲染页面\r\n\t\t */\r\n\t\tgetNearbyUnrenderedPages() {\r\n\t\t\tconst nearbyPages = [];\r\n\t\t\tconst currentPage = this.curPage;\r\n\t\t\tconst range = 2; // 加载当前页前后2页\r\n\t\t\t\r\n\t\t\tfor (let i = Math.max(1, currentPage - range); i <= Math.min(this.totalPages, currentPage + range); i++) {\r\n\t\t\t\tif (!this.renderedPages.has(i) && !this.renderingPages.has(i)) {\r\n\t\t\t\t\tnearbyPages.push(i);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn nearbyPages;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 重置组件状态\r\n\t\t */\r\n\t\tresetState() {\r\n\t\t\tthis.totalPages = 0;\r\n\t\t\tthis.curPage = 1;\r\n\t\t\tthis.allPagesRendered = false; // 重置渲染完成状态\r\n\t\t\tthis.pageCache.clear();\r\n\t\t\tthis.renderedPages.clear();\r\n\t\t\tthis.renderingPages.clear();\r\n\t\t\tthis.loadingDetail = \"\"; // 清空加载详情\r\n\t\t\t\r\n\t\t\t// 通知父组件渲染状态变化\r\n\t\t\tthis.$emit('render-status-change', false);\r\n\t\t\t\r\n\t\t\t// 清理定时器\r\n\t\t\tif (this._retryTimer) {\r\n\t\t\t\tclearTimeout(this._retryTimer);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 关键：在重置状态时立即滚动到顶部\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst container = this.$refs.pdfContainer;\r\n\t\t\t\tif (container) {\r\n\t\t\t\t\tcontainer.scrollTop = 0;\r\n\t\t\t\t\tcontainer.scrollTo(0, 0);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t\r\n\t\t/**\r\n\t\t * 处理滚动事件\r\n\t\t */\r\n\t\thandleScroll(event) {\r\n\t\t\tconst container = event.target;\r\n\t\t\tconst canvases = container.querySelectorAll('canvas[data-page]');\r\n\t\t\t\r\n\t\t\tfor (let i = 0; i < canvases.length; i++) {\r\n\t\t\t\tconst canvas = canvases[i];\r\n\t\t\t\tconst rect = canvas.getBoundingClientRect();\r\n\t\t\t\tconst containerRect = container.getBoundingClientRect();\r\n\t\t\t\t\r\n\t\t\t\t// 如果canvas顶部在容器视口内\r\n\t\t\t\tif (rect.top >= containerRect.top && rect.top <= containerRect.bottom) {\r\n\t\t\t\t\tthis.curPage = parseInt(canvas.getAttribute('data-page'));\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t/**\r\n\t\t * 渲染所有页面（取消虚拟滚动，解决显示问题）\r\n\t\t */\r\n\t\tasync renderAllPages() {\r\n\t\t\tif (!this.pdfDocument || this.totalPages === 0) return;\r\n\t\t\t\r\n\t\t\tconsole.log(`开始渲染 ${this.totalPages} 页PDF...`);\r\n\t\t\t\r\n\t\t\t// 清空之前的渲染状态\r\n\t\t\tthis.renderedPages.clear();\r\n\t\t\tthis.renderingPages.clear();\r\n\t\t\tthis.allPagesRendered = false;\r\n\t\t\t\r\n\t\t\t// 通知父组件开始渲染\r\n\t\t\tthis.$emit('render-status-change', false);\r\n\t\t\t\r\n\t\t\t// 创建所有页面的占位符\r\n\t\t\tthis.renderedPages = new Set();\r\n\t\t\t\r\n\t\t\t// 批量渲染页面，每批2个页面避免阻塞\r\n\t\t\tconst batchSize = 2;\r\n\t\t\tfor (let i = 1; i <= this.totalPages; i += batchSize) {\r\n\t\t\t\tconst end = Math.min(i + batchSize - 1, this.totalPages);\r\n\t\t\t\tconst promises = [];\r\n\t\t\t\t\r\n\t\t\t\tfor (let pageNum = i; pageNum <= end; pageNum++) {\r\n\t\t\t\t\tpromises.push(this.renderPage(pageNum));\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tawait Promise.all(promises);\r\n\t\t\t\t\r\n\t\t\t\t// 给用户界面更新机会\r\n\t\t\t\tawait this.sleep(50);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查是否所有页面都已渲染完成\r\n\t\t\tthis.checkAllPagesRendered();\r\n\t\t\t\r\n\t\t\tconsole.log('所有页面渲染完成');\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 渲染单个页面（简化版）\r\n\t\t */\r\n\t\tasync renderPage(pageNum, isEnlarge = false) {\r\n\t\t\tif (!this.pdfDocument || pageNum < 1 || pageNum > this.totalPages) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.renderingPages.has(pageNum)) return;\r\n\t\t\t\r\n\t\t\tthis.renderingPages.add(pageNum);\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tconst cacheKey = `${pageNum}-${isEnlarge ? 'enlarge' : 'normal'}`;\r\n\t\t\t\t\r\n\t\t\t\t// 检查缓存\r\n\t\t\t\tif (this.pageCache.has(cacheKey)) {\r\n\t\t\t\t\tthis.renderCanvas(pageNum, this.pageCache.get(cacheKey), isEnlarge);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取页面\r\n\t\t\t\tconst page = await this.pdfDocument.getPage(pageNum);\r\n\t\t\t\tconst scale = isEnlarge ? 2 : 1.2;\r\n\t\t\t\tconst viewport = page.getViewport({ scale });\r\n\t\t\t\t\r\n\t\t\t\t// 创建canvas进行渲染\r\n\t\t\t\tconst canvas = document.createElement('canvas');\r\n\t\t\t\tconst context = canvas.getContext('2d');\r\n\t\t\t\t\r\n\t\t\t\tcanvas.height = viewport.height;\r\n\t\t\t\tcanvas.width = viewport.width;\r\n\t\t\t\t\r\n\t\t\t\tawait page.render({\r\n\t\t\t\t\tcanvasContext: context,\r\n\t\t\t\t\tviewport: viewport\r\n\t\t\t\t}).promise;\r\n\t\t\t\t\r\n\t\t\t\t// 缓存渲染结果\r\n\t\t\t\tthis.pageCache.set(cacheKey, canvas);\r\n\t\t\t\t\r\n\t\t\t\t// 渲染到实际canvas\r\n\t\t\t\tthis.renderCanvas(pageNum, canvas, isEnlarge);\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(`渲染第${pageNum}页失败:`, error);\r\n\t\t\t} finally {\r\n\t\t\t\tthis.renderingPages.delete(pageNum);\r\n\t\t\t\tthis.renderedPages.add(pageNum);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 渲染到实际canvas\r\n\t\t */\r\n\t\trenderCanvas(pageNum, sourceCanvas, isEnlarge = false) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst refName = isEnlarge ? `enlarge-canvas-${pageNum}` : `canvas-${pageNum}`;\r\n\t\t\t\tconst canvases = this.$refs[refName];\r\n\t\t\t\t\r\n\t\t\t\t// 确保canvas存在\r\n\t\t\t\tif (!canvases || canvases.length === 0) {\r\n\t\t\t\t\tconsole.warn(`Canvas for page ${pageNum} not found, retrying...`);\r\n\t\t\t\t\tsetTimeout(() => this.renderCanvas(pageNum, sourceCanvas, isEnlarge), 100);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst canvas = canvases[0];\r\n\t\t\t\tif (!canvas) {\r\n\t\t\t\t\tconsole.warn(`Canvas element for page ${pageNum} is null`);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst context = canvas.getContext('2d');\r\n\t\t\t\tif (!context) {\r\n\t\t\t\t\tconsole.warn(`Canvas context for page ${pageNum} is null`);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tcanvas.height = sourceCanvas.height;\r\n\t\t\t\tcanvas.width = sourceCanvas.width;\r\n\t\t\t\t\r\n\t\t\t\tcontext.clearRect(0, 0, canvas.width, canvas.height);\r\n\t\t\t\tcontext.drawImage(sourceCanvas, 0, 0);\r\n\t\t\t\t\r\n\t\t\t\t// 确保页面标记为已渲染\r\n\t\t\t\tthis.renderedPages.add(pageNum);\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否所有页面都已渲染完成\r\n\t\t\t\tthis.checkAllPagesRendered();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 检查所有页面是否渲染完成\r\n\t\t */\r\n\t\tcheckAllPagesRendered() {\r\n\t\t\tif (this.totalPages > 0 && this.renderedPages.size >= this.totalPages) {\r\n\t\t\t\tthis.allPagesRendered = true;\r\n\t\t\t\tconsole.log('所有PDF页面渲染完成，启用点击事件');\r\n\t\t\t\t// 通知父组件所有页面渲染完成\r\n\t\t\t\tthis.$emit('render-status-change', true);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 管理缓存大小\r\n\t\t */\r\n\t\tmanageCacheSize() {\r\n\t\t\tif (this.pageCache.size > this.maxCacheSize) {\r\n\t\t\t\tconst keys = Array.from(this.pageCache.keys());\r\n\t\t\t\tconst toRemove = keys.slice(0, keys.length - this.maxCacheSize);\r\n\t\t\t\t\r\n\t\t\t\ttoRemove.forEach(key => {\r\n\t\t\t\t\tthis.pageCache.delete(key);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 跳转到指定页面\r\n\t\t */\r\n\t\tasync skipPage(pageNum) {\r\n\t\t\tif (pageNum < 1) pageNum = 1;\r\n\t\t\tif (pageNum > this.totalPages) pageNum = this.totalPages;\r\n\t\t\t\r\n\t\t\tthis.curPage = pageNum;\r\n\t\t\t\r\n\t\t\t// 滚动到指定页面 - 使用精确的DOM查找\r\n\t\t\tconst container = document.getElementById('pdf-container');\r\n\t\t\tif (!container) {\r\n\t\t\t\tconsole.error('PDF容器未找到');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用ref查找canvas元素\r\n\t\t\tconst refName = `canvas-${pageNum}`;\r\n\t\t\tconst targetCanvas = this.$refs[refName];\r\n\t\t\t\r\n\t\t\tif (targetCanvas && targetCanvas[0]) {\r\n\t\t\t\t// 直接滚动到目标canvas\r\n\t\t\t\ttargetCanvas[0].scrollIntoView({\r\n\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\tblock: 'start',\r\n\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// 使用querySelector作为备选方案\r\n\t\t\t\tconst canvasSelector = `canvas[data-page=\"${pageNum}\"]`;\r\n\t\t\t\tconst fallbackCanvas = container.querySelector(canvasSelector);\r\n\t\t\t\t\r\n\t\t\t\tif (fallbackCanvas) {\r\n\t\t\t\t\tfallbackCanvas.scrollIntoView({\r\n\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\tblock: 'start',\r\n\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果找不到canvas，直接滚动到对应位置\r\n\t\t\t\t\tconst pageHeight = 800; // 估计的页面高度\r\n\t\t\t\t\tconst targetTop = (pageNum - 1) * pageHeight;\r\n\t\t\t\t\tcontainer.scrollTo({\r\n\t\t\t\t\t\ttop: targetTop,\r\n\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 放大显示\r\n\t\t * @param {Number} pageNum - 要放大的页码\r\n\t\t */\r\n\t\tasync enlarge(pageNum) {\r\n\t\t\t// 确保当前页码被更新为要放大的页码\r\n\t\t\tthis.curPage = pageNum;\r\n\t\t\t\r\n\t\t\tthis.display_enlarge = true;\r\n\t\t\tdocument.body.style.overflow = \"hidden\";\r\n\t\t\tdocument.documentElement.style.overflow = \"hidden\";\r\n\t\t\t\r\n\t\t\t// 预渲染放大版本（只渲染当前页）\r\n\t\t\tawait this.renderPage(pageNum, true);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 获取放大模式可见页面\r\n\t\t * 只返回当前页码，实现只放大当前预览的页面\r\n\t\t */\r\n\t\tgetEnlargeVisiblePages() {\r\n\t\t\tif (!this.totalPages) return [];\r\n\t\t\t\r\n\t\t\t// 只返回当前页码\r\n\t\t\treturn [this.curPage];\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 关闭放大模式\r\n\t\t */\r\n\t\tclose() {\r\n\t\t\tthis.display_enlarge = false;\r\n\t\t\tdocument.body.style.overflow = \"auto\";\r\n\t\t\tdocument.documentElement.style.overflow = \"auto\";\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 工具方法：延时\r\n\t\t */\r\n\t\tsleep(ms) {\r\n\t\t\treturn new Promise(resolve => setTimeout(resolve, ms));\r\n\t\t}\r\n\t},\r\n\t\r\n\tmounted() {\r\n\t\t// 添加窗口大小变化监听\r\n\t\twindow.addEventListener('resize', this.handleResize);\r\n\t},\r\n\t\r\n\tbeforeDestroy() {\r\n\t\t// 清理资源\r\n\t\tthis.pageCache.clear();\r\n\t\tthis.renderedPages.clear();\r\n\t\tthis.renderingPages.clear();\r\n\t\tclearTimeout(this._retryTimer);\r\n\t\twindow.removeEventListener('resize', this.handleResize);\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.pdf-loading-container {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100vh;\r\n\tbackground-color: rgba(255, 255, 255, 0.9);\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tz-index: 1000;\r\n}\r\n\r\n.loading-content {\r\n\tbackground: white;\r\n\tpadding: 40px;\r\n\tborder-radius: 12px;\r\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n\ttext-align: center;\r\n\tmin-width: 400px;\r\n\tmax-width: 500px;\r\n}\r\n\r\n.loading-text {\r\n\tfont-size: 18px;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20px;\r\n}\r\n\r\n.loading-progress {\r\n\tmargin: 20px 0;\r\n}\r\n\r\n.loading-detail {\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n\tmargin-top: 15px;\r\n\tmin-height: 20px;\r\n}\r\n\r\n.pdf-page {\r\n\tposition: relative;\r\n\tmargin-bottom: 20px;\r\n\ttext-align: center;\r\n}\r\n\r\n.canvas-enlarge {\r\n\tz-index: 999;\r\n\tbackground-color: rgba(0, 0, 0, 0.8);\r\n\tposition: fixed;\r\n\twidth: 100%;\r\n\theight: 100vh;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n#enlarge {\r\n\tbackground: white;\r\n\tborder-radius: 8px;\r\n\tpadding: 20px;\r\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n</style>"]}]}