{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\suppliersRoom.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\suppliersRoom.vue", "mtime": 1753956770122}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgU2lnbiBmcm9tICIuL3N1cHBsaWVyQ29tcG9uZW50L3NpZ24udnVlIjsNCmltcG9ydCBSZWFkeSBmcm9tICIuL3N1cHBsaWVyQ29tcG9uZW50L3JlYWR5LnZ1ZSI7DQppbXBvcnQgcHVibGljaXR5IGZyb20gIi4vc3VwcGxpZXJDb21wb25lbnQvcHVibGljaXR5LnZ1ZSI7DQppbXBvcnQgZGVjcnlwdGlvbiBmcm9tICIuL3N1cHBsaWVyQ29tcG9uZW50L2RlY3J5cHRpb24udnVlIjsNCmltcG9ydCBiaWRBbm5vdW5jZW1lbnQgZnJvbSAiLi9zdXBwbGllckNvbXBvbmVudC9iaWRBbm5vdW5jZW1lbnQudnVlIjsNCmltcG9ydCBlbmQgZnJvbSAiLi9zdXBwbGllckNvbXBvbmVudC9lbmQudnVlIjsNCg0KaW1wb3J0IHsNCiAgZm9ybWF0RGF0ZU9wdGlvbiwNCiAgZ2V0VG9kYXlTdGFydFdpdGhEYXRlLA0KICBnZXRUb2RheUVuZFdpdGhEYXRlLA0KfSBmcm9tICJAL3V0aWxzL2luZGV4IjsNCmltcG9ydCB7IGJpZEluZm8sIGNoYXRIaXN0b3J5IH0gZnJvbSAiQC9hcGkvb25saW5lQmlkT3BlbmluZy9pbmZvIjsNCmltcG9ydCB7IGdldFVzZXJQcm9maWxlIH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOw0KaW1wb3J0IHsgY29udmVydFRvQ2hpbmVzZUN1cnJlbmN5IH0gZnJvbSAiQC91dGlscy9hbW91bnQiOw0KaW1wb3J0IHsgbGlzdEluZm8gfSBmcm9tICJAL2FwaS9ldmFsdWF0aW9uL2luZm8iOw0KaW1wb3J0IHsgbGlzdFF1b3RlLCBhZGRRdW90ZSB9IGZyb20gIkAvYXBpL2FnYWluL3F1b3RlIjsNCmltcG9ydCB7IGV4cGVydEluZm9CeUlkIH0gZnJvbSAiQC9hcGkvZXhwZXJ0L3JldmlldyI7DQppbXBvcnQgeyBhcHByb3ZhbFByb2Nlc3MsaW5xdWlyaW5nQmlkTGlzdCB9IGZyb20gIkAvYXBpL2V4cGVydC9yZXZpZXciOw0KDQpjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSTsNCmNvbnN0IHNvY2tldFVybCA9IGJhc2VVcmwucmVwbGFjZSgiaHR0cCIsICJ3cyIpLnJlcGxhY2UoIi9wcm9kLWFwaSIsICIiKTsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogeyBTaWduLCBSZWFkeSwgcHVibGljaXR5LCBkZWNyeXB0aW9uLCBiaWRBbm5vdW5jZW1lbnQsIGVuZCB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBub2RlOiAic2lnbiIsDQogICAgICB1c2VySW5mbzoge30sDQogICAgICBwcm9qZWN0SW5mbzogbnVsbCwNCg0KICAgICAgLy8g5LqM5qyh5oql5Lu36KGo5Y2VDQogICAgICBmb3JtOiB7DQogICAgICAgIHF1b3RlQW1vdW50OiAiIiwNCiAgICAgICAgcHJvamVjdEV2YWx1YXRpb25JZDogIiIsDQogICAgICAgIHF1b3RlQW1vdW50U3RyOiAiIiwNCiAgICAgIH0sDQogICAgICAvLyDkuozmrKHmiqXku7fph5Hpop0NCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHF1b3RlQW1vdW50OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpemHkeminSIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBxdW90ZUFtb3VudFN0cjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXph5Hpop3lpKflhpkiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgICBwcm9ncmVzczogWw0KICAgICAgICB7DQogICAgICAgICAgaXRlbU5hbWU6ICLotYTmoLzmgKfor4TlrqEiLA0KICAgICAgICAgIHN0YXR1czogMCwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW1OYW1lOiAi56ym5ZCI5oCn6K+E5a6hIiwNCiAgICAgICAgICBzdGF0dXM6IDAsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtTmFtZTogIuaKgOacr+agh+ivhOWuoSIsDQogICAgICAgICAgc3RhdHVzOiAwLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbU5hbWU6ICLllYbliqHmoIfor4TlrqEiLA0KICAgICAgICAgIHN0YXR1czogMCwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW1OYW1lOiAi5oqV5qCH5oql5Lu35omT5YiGIiwNCiAgICAgICAgICBzdGF0dXM6IDAsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgLy8g5LqM5qyh5oql5Lu35Y6G5Y+y5oql5Lu3DQogICAgICBxdW90ZUxpc3Q6IFtdLA0KICAgICAgLy8g5pi+56S65LqM5qyh5oql5Lu35by55qGGDQogICAgICBzZWNvbmRRdW90ZVZpc2libGU6IGZhbHNlLA0KDQogICAgICBkZWNyeXB0aW9uRGVhZGxpbmU6ICIiLA0KDQogICAgICB1cmw6ICIiLA0KICAgICAgbWVzc2FnZTogIiIsDQogICAgICB0ZXh0X2NvbnRlbnQ6ICIiLA0KICAgICAgd3M6IG51bGwsDQoNCiAgICAgIHJlY29yZENvbnRlbnQ6IFtdLA0KICAgICAgZXZhbHVhdGlvbkluZm86IHt9LA0KICAgICAgdGltZXI6IG51bGwsIC8vIOWtmOWCqOWumuaXtuWZqOW8leeUqA0KICAgICAgaXNDb3VudGRvd25FbmRlZDogZmFsc2UsDQogICAgICAvLyDkuJPlrrbkv6Hmga8NCiAgICAgIGV4cGVydExpc3Q6IHt9LA0KICAgICAgaW50ZXJ2YWxJZDogbnVsbCwNCiAgICAgIGNvdW50ZG93bjogIiIsDQogICAgICBudW06IDAsDQogICAgICBpc0xpbms6IGZhbHNlLA0KICAgICAgYW5vbnltb3VzOiB0cnVlLA0KICAgICAgc3luY2VkSGVpZ2h0OiAnNDUwcHgnLCAvLyDliJ3lp4vpq5jluqYNCiAgICAgIGN1cnJlbnRUaW1lOm51bGwNCg0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgc2hvdWxkUmVuZGVyU2lnbigpIHsNCiAgICAgIHJldHVybiAoDQogICAgICAgIHRoaXMubm9kZSA9PT0gInNpZ25JbiIgJiYNCiAgICAgICAgdGhpcy5wcm9qZWN0SW5mbyAmJg0KICAgICAgICB0aGlzLnVzZXJJbmZvICYmDQogICAgICAgIE9iamVjdC5rZXlzKHRoaXMudXNlckluZm8pLmxlbmd0aCA+IDANCiAgICAgICk7DQogICAgfSwNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBub2RlOiB7DQogICAgICBoYW5kbGVyKCkgew0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB2YXIgZWxlbWVudCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdtYWluJyk7DQogICAgICAgICAgY29uc29sZS5sb2coJ2VsZW1lbnQuY2xpZW50SGVpZ2h0JywgZWxlbWVudC5vZmZzZXRIZWlnaHQpOw0KICAgICAgICAgIHRoaXMuc3luY2VkSGVpZ2h0ID0gZWxlbWVudC5vZmZzZXRIZWlnaHQgLSAxMjAgKyAncHgnDQogICAgICAgIH0sIDEwKTsNCg0KICAgICAgfSwNCiAgICAgIGRlZXA6IHRydWUNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBmaW5pc2hTZWNvbmRRdW90ZShpc09rKXsNCiAgICAgIC8vIHRoaXMuaXNDb3VudGRvd25FbmRlZCA9IGlzT2s7DQogICAgfSwNCiAgICAvLyDliJ3lp4vljJYNCiAgICBpbml0KCkgew0KICAgICAgLy8g6I635Y+W5byA5qCH6aG555uu5L+h5oGvDQogICAgICBjb25zdCBwcm9taXNlMSA9IGJpZEluZm8oew0KICAgICAgICBiaWRPcGVuaW5nVGltZTogZ2V0VG9kYXlTdGFydFdpdGhEYXRlKCksDQogICAgICAgIGJpZE9wZW5pbmdFbmRUaW1lOiBnZXRUb2RheUVuZFdpdGhEYXRlKCksDQogICAgICAgIHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLA0KICAgICAgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5wcm9qZWN0SW5mbyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnd2FybmluZyhyZXNwb25zZS5tc2cpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIC8vIOiOt+WPlueUqOaIt+S/oeaBrw0KICAgICAgY29uc3QgcHJvbWlzZTIgPSBnZXRVc2VyUHJvZmlsZSgpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMudXNlckluZm8gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgidXNlckluZm8iLCBKU09OLnN0cmluZ2lmeSh0aGlzLnVzZXJJbmZvKSk7DQogICAgICB9KTsNCiAgICAgIC8vIOiOt+W<PERSON><PERSON>hu<PERSON>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"}, {"version": 3, "sources": ["suppliersRoom.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4GA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "suppliersRoom.vue", "sourceRoot": "src/views/bidOpeningHall", "sourcesContent": ["<template>\r\n  <div>\r\n    <BidHeadone ref=\"head\" @updateStatus=\"handleStatus\"></BidHeadone>\r\n    <div class=\"bidOpeningHall\">\r\n      <el-card id=\"main\" class=\"box-card\">\r\n        <div style=\"height: 10px;\">\r\n        <div style=\"padding: 5px 0 0 20px; float: left;\">平台当前时间：<span >{{ currentTime }}</span></div>\r\n        <div style=\"padding: 5px 20px 0 0; float: right;\">\r\n          连接状态：<span :style=\"`color:${isLink ? 'green' : 'red'}`\">{{\r\n            isLink ? \"已连接\" : \"已断连，请刷新重连\"\r\n          }}</span>\r\n        </div></div>\r\n        <Sign ref=\"signIn\" v-if=\"shouldRenderSign\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" @sendMessage=\"operateSend\"></Sign>\r\n        <ready ref=\"ready\" v-if=\"node == 'ready' && projectInfo\" :projectInfo=\"projectInfo\"></ready>\r\n        <publicity ref=\"publicity\" v-if=\"node == 'publicity'\"></publicity>\r\n        <decryption ref=\"decryption\" v-if=\"node == 'decryption' && projectInfo\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" :deadline=\"decryptionDeadline\" @sendMessage=\"operateSend\"></decryption>\r\n        <bidAnnouncement ref=\"bidAnnouncement\" v-if=\"node == 'bidAnnouncement'\"></bidAnnouncement>\r\n        <end ref=\"end\" v-if=\"node == 'end'\" @sendData=\"handleData\"></end>\r\n\r\n      </el-card>\r\n      <el-card class=\"box-card\" style=\"width: 15%;\">\r\n        <div class=\"im\">\r\n          <div class=\"im-title\">{{ userInfo.ent?userInfo.ent.entName :  ''}}</div>\r\n          <div ref=\"messagesContainer\" class=\"im-content\" :style=\"{height: syncedHeight }\">\r\n            <div v-for=\"(itemc, indexc) in recordContent\" :key=\"indexc\">\r\n              <div class=\"sysMessage\" v-if=\"itemc.type == 0\">\r\n              </div>\r\n              <div v-else>\r\n                <div class=\"word\" v-if=\"itemc.sendId !== userInfo.entId\">\r\n                  <div class=\"info\">\r\n                    <div class=\"message_time\">\r\n                      {{anonymous? \"*******\":itemc.sendName }}\r\n                    </div>\r\n                    <div class=\"info-content\">{{ itemc.content }}</div>\r\n                    <div class=\"message_time\">\r\n                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"word-my\" v-else>\r\n                  <div class=\"info\">\r\n                    <div class=\"info-content\">{{ itemc.content }}</div>\r\n                    <div class=\"Sender_time\">\r\n                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"im-operation\">\r\n            <div style=\"margin-right:5px\">\r\n              <el-input v-model=\"message\" placeholder=\"输入内容\"></el-input>\r\n            </div>\r\n            <el-button style=\"height: 36px;background: #176ADB;color:#fff\" @click=\"send\">发送</el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <Foot></Foot>\r\n\r\n    <el-dialog :visible.sync=\"secondQuoteVisible\" width=\"60%\">\r\n      <!-- 显示倒计时 -->\r\n      <el-table :data=\"quoteList\" border style=\"width: 100%;\" :cell-style=\"cellStyle\" :header-cell-style=\"headStyle\">\r\n        <el-table-column label=\"二次报价\">\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"100\">\r\n          </el-table-column>\r\n          <el-table-column label=\"历史报价\" prop=\"quoteAmount\" align=\"center\">\r\n          </el-table-column>\r\n          <el-table-column label=\"报价大写\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatAmount(scope.row.quoteAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"报价时间\" prop=\"createTime\" align=\"center\">\r\n          </el-table-column>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"decryption-countdown\">\r\n        <el-statistic @finish=\"finishSecondQuote\" format=\"二次报价结束倒计时：HH小时mm分ss秒\" :value=\"countdown\" time-indices>\r\n        </el-statistic>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"90px\">\r\n          <el-col :span=\"6\" :offset=\"3\">\r\n            <el-form-item label=\"报价金额：\">\r\n              <el-input v-model=\"form.quoteAmount\" placeholder=\"请输入报价金额\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"大写金额：\">\r\n              <el-input :disabled=\"true\" :value=\"returnConvertToChineseCurrency(form.quoteAmount)\" placeholder=\"请输入报价金额大写\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n        <el-col :span=\"5\">\r\n          <div style=\"display:flex;justify-content: center;align-items: center;\">\r\n            <el-button class=\"quote-button\" :disabled=\"isCountdownEnded\"  @click=\"submitQuote\">确 定</el-button>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Sign from \"./supplierComponent/sign.vue\";\r\nimport Ready from \"./supplierComponent/ready.vue\";\r\nimport publicity from \"./supplierComponent/publicity.vue\";\r\nimport decryption from \"./supplierComponent/decryption.vue\";\r\nimport bidAnnouncement from \"./supplierComponent/bidAnnouncement.vue\";\r\nimport end from \"./supplierComponent/end.vue\";\r\n\r\nimport {\r\n  formatDateOption,\r\n  getTodayStartWithDate,\r\n  getTodayEndWithDate,\r\n} from \"@/utils/index\";\r\nimport { bidInfo, chatHistory } from \"@/api/onlineBidOpening/info\";\r\nimport { getUserProfile } from \"@/api/system/user\";\r\nimport { convertToChineseCurrency } from \"@/utils/amount\";\r\nimport { listInfo } from \"@/api/evaluation/info\";\r\nimport { listQuote, addQuote } from \"@/api/again/quote\";\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { approvalProcess,inquiringBidList } from \"@/api/expert/review\";\r\n\r\nconst baseUrl = process.env.VUE_APP_BASE_API;\r\nconst socketUrl = baseUrl.replace(\"http\", \"ws\").replace(\"/prod-api\", \"\");\r\nexport default {\r\n  components: { Sign, Ready, publicity, decryption, bidAnnouncement, end },\r\n  data() {\r\n    return {\r\n      node: \"sign\",\r\n      userInfo: {},\r\n      projectInfo: null,\r\n\r\n      // 二次报价表单\r\n      form: {\r\n        quoteAmount: \"\",\r\n        projectEvaluationId: \"\",\r\n        quoteAmountStr: \"\",\r\n      },\r\n      // 二次报价金额\r\n      rules: {\r\n        quoteAmount: [\r\n          { required: true, message: \"请输入金额\", trigger: \"blur\" },\r\n        ],\r\n        quoteAmountStr: [\r\n          { required: true, message: \"请输入金额大写\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      progress: [\r\n        {\r\n          itemName: \"资格性评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"符合性评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"技术标评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"商务标评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"投标报价打分\",\r\n          status: 0,\r\n        },\r\n      ],\r\n      // 二次报价历史报价\r\n      quoteList: [],\r\n      // 显示二次报价弹框\r\n      secondQuoteVisible: false,\r\n\r\n      decryptionDeadline: \"\",\r\n\r\n      url: \"\",\r\n      message: \"\",\r\n      text_content: \"\",\r\n      ws: null,\r\n\r\n      recordContent: [],\r\n      evaluationInfo: {},\r\n      timer: null, // 存储定时器引用\r\n      isCountdownEnded: false,\r\n      // 专家信息\r\n      expertList: {},\r\n      intervalId: null,\r\n      countdown: \"\",\r\n      num: 0,\r\n      isLink: false,\r\n      anonymous: true,\r\n      syncedHeight: '450px', // 初始高度\r\n      currentTime:null\r\n\r\n    };\r\n  },\r\n  computed: {\r\n    shouldRenderSign() {\r\n      return (\r\n        this.node === \"signIn\" &&\r\n        this.projectInfo &&\r\n        this.userInfo &&\r\n        Object.keys(this.userInfo).length > 0\r\n      );\r\n    },\r\n  },\r\n  watch: {\r\n    node: {\r\n      handler() {\r\n        setTimeout(() => {\r\n          var element = document.getElementById('main');\r\n          console.log('element.clientHeight', element.offsetHeight);\r\n          this.syncedHeight = element.offsetHeight - 120 + 'px'\r\n        }, 10);\r\n\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    finishSecondQuote(isOk){\r\n      // this.isCountdownEnded = isOk;\r\n    },\r\n    // 初始化\r\n    init() {\r\n      // 获取开标项目信息\r\n      const promise1 = bidInfo({\r\n        bidOpeningTime: getTodayStartWithDate(),\r\n        bidOpeningEndTime: getTodayEndWithDate(),\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.projectInfo = response.data;\r\n        } else {\r\n          this.$modal.msgwarning(response.msg);\r\n        }\r\n      });\r\n      // 获取用户信息\r\n      const promise2 = getUserProfile().then((response) => {\r\n        this.userInfo = response.data;\r\n        localStorage.setItem(\"userInfo\", JSON.stringify(this.userInfo));\r\n      });\r\n      // 获取项目评审信息\r\n      const promise3 = listInfo({\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          if (response.rows && response.rows.length > 0) {\r\n            this.evaluationInfo = response.rows[response.rows.length - 1];\r\n          } else {\r\n            this.evaluationInfo = {};\r\n            // this.$message.error(\"未查询到项目评审信息\");\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg);\r\n        }\r\n      });\r\n      // 获取专家信息\r\n      const promise4 = expertInfoById({\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.expertList = response.data;\r\n          localStorage.setItem(\"expertList\", JSON.stringify(this.expertList));\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n\r\n      Promise.all([promise1, promise2, promise3, promise4]).then((result) => {\r\n        this.join();\r\n        if (this.node == \"end\") {\r\n          this.$refs.end.getExpertReviewProgress();\r\n        }\r\n      });\r\n    },\r\n    // 获取当前开标室流程\r\n    handleStatus(data) {\r\n      this.anonymous = this.$store.getters.supplierBidOpenStatus >= 2 ? false : true;\r\n      switch (data) {\r\n        case \"签到\":\r\n          this.node = \"signIn\";\r\n          break;\r\n        case \"开标准备\":\r\n          this.node = \"ready\";\r\n          break;\r\n        case \"投标人公示\":\r\n          this.node = \"publicity\";\r\n          break;\r\n        case \"标书解密\":\r\n          this.node = \"decryption\";\r\n          break;\r\n        case \"唱标\":\r\n          this.node = \"bidAnnouncement\";\r\n          break;\r\n        case \"开标结束\":\r\n          this.node = \"end\";\r\n          break;\r\n      }\r\n    },\r\n    // 节点更新通知\r\n    updateStatus() {\r\n      this.$refs.head.getBidStatus();\r\n    },\r\n\r\n    // 转换大写的报价金额\r\n    returnConvertToChineseCurrency(money) {\r\n      return convertToChineseCurrency(money);\r\n    },\r\n    // 提交报价\r\n    submitQuote() {\r\n      if (this.isCountdownEnded) {\r\n        this.$message.warning(\"倒计时结束，禁止报价\");\r\n        this.secondQuoteVisible = false;\r\n        return;\r\n      }\r\n      var previousPrice = 0;\r\n\r\n      if(this.quoteList.length===0){\r\n        previousPrice = this.projectInfo.project.budgetAmount;\r\n      }else{\r\n        previousPrice = this.quoteList[this.quoteList.length-1].quoteAmount;\r\n      }\r\n      if(this.form.quoteAmount>previousPrice){\r\n        this.$confirm(\"本次报价超过上次报价 \"+ previousPrice + \" 元，是否继续提交？\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          // 用户确认后执行提交报价\r\n          this.doSubmitQuote();\r\n        }).catch(() => {\r\n          // 用户取消，不执行任何操作\r\n          return;\r\n        });\r\n        return;\r\n      }\r\n      //提交报价\r\n      this.doSubmitQuote();\r\n    },\r\n    doSubmitQuote() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          if (this.evaluationInfo.projectEvaluationId == null) {\r\n            listInfo({\r\n              projectId: this.$route.query.projectId,\r\n            }).then((response) => {\r\n              if (response.code === 200) {\r\n                if (response.rows && response.rows.length > 0) {\r\n                  this.evaluationInfo = response.rows[response.rows.length - 1];\r\n                  this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;\r\n                } else {\r\n                  this.$message.error(\"没有评审信息！\");\r\n                  return\r\n                }\r\n              } else {\r\n                this.$message.success(response.msg);\r\n              }\r\n            });\r\n          } else {\r\n            this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;\r\n          }\r\n          addQuote(this.form)\r\n            .then((result) => {\r\n              if (result.code === 200) {\r\n                this.$message.success(result.msg);\r\n                this.getQuoteList();\r\n                this.secondQuoteVisible = false;\r\n              } else {\r\n                this.$message.error(result.msg);\r\n              }\r\n            })\r\n            .catch((err) => { });\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n      // 关闭弹窗时，清除定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n    },\r\n    // 转换大写的报价金额\r\n    formatAmount(amount) {\r\n      return convertToChineseCurrency(amount);\r\n    },\r\n\r\n    // 格式化开标时间显示\r\n    formatBidOpeningTime(time) {\r\n      return formatDateOption(time, \"date\");\r\n    },\r\n    // 格式化开标时间显示 时-分-秒\r\n    formatBidOpeningTimeTwo(time) {\r\n      return formatDateOption(time, \"time\");\r\n    },\r\n    // 获取历史报价\r\n    getQuoteList() {\r\n      const queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 999,\r\n        params: {\r\n          projectId: this.$route.query.projectId,\r\n          entId: this.userInfo.entId,\r\n        },\r\n      };\r\n      listQuote(queryParams)\r\n        .then((result) => {\r\n          //存入数据\r\n          this.quoteList = result.rows;\r\n          console.log(this.quoteList.length + 1 == this.num);\r\n          if (this.quoteList.length + 1 == this.num) {\r\n            this.secondQuoteVisible = true;\r\n            // this.isCountdownEnded = true;\r\n          } else {\r\n            // this.isCountdownEnded = false;\r\n            this.secondQuoteVisible = false;\r\n          }\r\n        })\r\n        .catch((err) => { });\r\n    },\r\n    getExpertReviewProgress() {\r\n      approvalProcess(this.$route.query.projectId, this.expertList[0].resultId).then(\r\n        (response) => {\r\n          if (response.code == 200 && response.data.scoringMethodUinfo.scoringMethodItems) {\r\n            \r\n            const existingItemNames = response.data.scoringMethodUinfo.scoringMethodItems.map(item => item.itemName);\r\n            this.progress = this.progress.filter(item => existingItemNames.includes(item.itemName));\r\n\r\n            const evalProjectEvaluationProcess = this.progress.find((item) => {\r\n              return item.itemName == \"投标报价打分\";\r\n            }).evalProjectEvaluationProcess;\r\n            if (evalProjectEvaluationProcess) {\r\n              let startTime = new Date(\r\n                evalProjectEvaluationProcess.startTime.replace(\" \", \"T\") + \"Z\"\r\n              );\r\n              // 将 Date 对象加上30秒\r\n              startTime.setMinutes(startTime.getMinutes() + evalProjectEvaluationProcess.minutes);\r\n              var endTime = startTime\r\n                .toISOString()\r\n                .replace(\"T\", \" \")\r\n                .replace(\"Z\", \"\")\r\n                .split(\".\")[0];\r\n              // 将更新后的时间转换回字符串格式\r\n              this.num = evalProjectEvaluationProcess.num;\r\n              this.handleData({\r\n                startTime: evalProjectEvaluationProcess.startTime,\r\n                endTime: endTime,\r\n                num: this.num,\r\n                isAbandonedBid: 0\r\n              });\r\n            }\r\n          } else {\r\n\r\n          }\r\n        }\r\n      );\r\n\r\n    },\r\n    // 判断是否打开二次报价弹窗\r\n    handleData({ startTime, endTime, num, isAbandonedBid }) {\r\n      if (this.secondQuoteVisible == true) {\r\n        return;\r\n      } else {\r\n        const now = new Date(); // 当前时间\r\n        const start = new Date(startTime); // 开始时间\r\n        const end = new Date(endTime); // 结束时间\r\n        console.log(\"isAbandonedBid\",isAbandonedBid);\r\n        \r\n        if (now >= start && now <= end && isAbandonedBid==0) {\r\n          this.$nextTick(() => {\r\n            this.countdown = new Date(endTime);\r\n          });\r\n          this.num = num;\r\n          console.log(\"this.num\", this.num);\r\n          this.form = {};\r\n          this.getQuoteList();\r\n        } else {\r\n          this.secondQuoteVisible = false;\r\n        }\r\n      }\r\n    },\r\n    // 连接websocket\r\n    join() {\r\n      // this.url = `${socketUrl}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n      this.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n      const wsurl = this.url;\r\n      this.ws = new WebSocket(wsurl);\r\n      const self = this;\r\n      // 心跳检测函数\r\n      const ws_heartCheck = {\r\n        timeout: 5000, // 5秒\r\n        timeoutObj: null,\r\n        serverTimeoutObj: null,\r\n        start: function () {\r\n          this.timeoutObj = setTimeout(() => {\r\n            // 这里发送一个心跳包\r\n            self.ws.send(\"ping\");\r\n            this.serverTimeoutObj = setTimeout(() => {\r\n              self.ws.close(); // 如果超过一定时间还没重置，视为断开连接\r\n            }, this.timeout);\r\n          }, this.timeout);\r\n        },\r\n        reset: function () {\r\n          clearTimeout(this.timeoutObj);\r\n          clearTimeout(this.serverTimeoutObj);\r\n          this.start();\r\n        },\r\n        stop: function () {\r\n          clearTimeout(this.timeoutObj);\r\n          clearTimeout(this.serverTimeoutObj);\r\n        }\r\n      };\r\n      this.ws.onopen = function (event) {\r\n        ws_heartCheck.start();\r\n        self.text_content = self.text_content + \"已经打开开标室连接!\" + \"\\n\";\r\n        self.isLink = true;\r\n\r\n        console.log(self.text_content);\r\n      };\r\n      this.ws.onmessage = function (event) {\r\n        console.log(event.data);\r\n        if(event.data == \"ping\"){\r\n          // 心跳响应\r\n          ws_heartCheck.reset();\r\n        }else if (event.data == \"连接成功\") {\r\n          console.log(\"socketUrl\", socketUrl);\r\n        } else if (event.data == \"signIn\") {\r\n          self.updateStatus();\r\n        } else if (event.data == \"bidPublicity\") {\r\n          self.updateStatus();\r\n        } else if (event.data.includes(\"decryption\")) {\r\n          self.updateStatus();\r\n        } else if (event.data == \"supDecrytion\") {\r\n          self.$refs.decryption.initdataList();\r\n        } else if (event.data == \"nextStep\") {\r\n          // 不做任何操作\r\n        } else if (\r\n          event.data == \"bidAnnouncement\" ||\r\n          event.data == \"flowLabel\"\r\n        ) {\r\n          self.updateStatus();\r\n        } else if (event.data == \"end\") {\r\n          self.updateStatus();\r\n        } else if (event.data == \"evalAgainQuote\") {\r\n          self.getExpertReviewProgress();\r\n          // //打开二次报价弹窗\r\n          // self.form = {};\r\n          // //加载报价记录\r\n          // self.getQuoteList();\r\n        } else {\r\n          self.initChat();\r\n        }\r\n      };\r\n      this.ws.onclose = function (event) {\r\n        self.text_content = self.text_content + \"已经关闭开标室连接!\" + \"\\n\";\r\n        self.isLink = false;\r\n        console.log(self.text_content);\r\n        //断开后自动重连\r\n        ws_heartCheck.stop();\r\n        self.join();\r\n      };\r\n    },\r\n    // 断开websocket连接\r\n    exit() {\r\n      if (this.ws) {\r\n        this.ws.close();\r\n        this.ws = null;\r\n      }\r\n    },\r\n    // 发送消息\r\n    send() {\r\n      if (this.ws) {\r\n        this.ws.send(this.message);\r\n        this.message = \"\";\r\n        this.scrollToBottom();\r\n      } else {\r\n        alert(\"未连接到开标室服务器\");\r\n      }\r\n    },\r\n    // 发送消息\r\n    operateSend(message) {\r\n      if (this.ws) {\r\n        this.ws.send(message);\r\n      } else {\r\n        alert(\"未连接到开标室服务器\");\r\n      }\r\n    },\r\n    // 初始化聊天记录\r\n    initChat() {\r\n      chatHistory(this.$route.query.projectId).then((response) => {\r\n        this.recordContent = response.data;\r\n      });\r\n    },\r\n    // 处理滚动\r\n    scrollToBottom() {\r\n      this.$nextTick(() => {\r\n        const container = this.$refs.messagesContainer;\r\n        container.scrollTop = container.scrollHeight;\r\n      });\r\n    },\r\n    // 表格头颜色\r\n    headStyle({ row, column, rowIndex, columnIndex }) {\r\n      console.log(row, column, rowIndex, columnIndex);\r\n      if (rowIndex === 0 && columnIndex === 0) {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          background: \"#1C57A7\",\r\n          color: \"#fff\",\r\n          \"font-size\": \"16px\",\r\n          \"font-weight\": \"700\",\r\n        };\r\n      } else {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          background: \"#176ADB\",\r\n          color: \"#fff\",\r\n          \"font-size\": \"16px\",\r\n          \"font-weight\": \"700\",\r\n          border: \"0px\",\r\n        };\r\n      }\r\n    },\r\n    // 表格样式\r\n    cellStyle({ row, column, rowIndex, columnIndex }) {\r\n      if (rowIndex % 2 === 0) {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          \"font-weight\": \"700\",\r\n          color: \"#000\",\r\n          \"font-size\": \"14px\",\r\n          background: \"#FFFFFF\",\r\n        };\r\n      } else {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          \"font-weight\": \"700\",\r\n          color: \"#000\",\r\n          \"font-size\": \"14px\",\r\n          background: \"#F5F5F5\",\r\n        };\r\n      }\r\n    },\r\n    startProgressInterval() {\r\n      this.intervalId = setInterval(() => {\r\n        if (this.node == \"end\") {\r\n          this.$refs.end.getExpertReviewProgress();\r\n          //在这里增加 供应商消息刷新页面接口\r\n\r\n        }\r\n      }, 5000); // 每隔 5 秒调用一次\r\n    },\r\n    stopProgressInterval() {\r\n      clearInterval(this.intervalId);\r\n    },\r\n    updateTime() {\r\n      var ct = new Date();\r\n      var _this = this;\r\n\r\n      setInterval(function(){\r\n        ct.setSeconds(ct.getSeconds() + 1);\r\n        _this.currentTime = formatDateOption(ct, \"cdatetime\");\r\n      }, 1000); // 每秒更新时间\r\n    }\r\n  },\r\n  created() { },\r\n  mounted() {\r\n    this.init();\r\n    this.initChat();\r\n    // 在组件挂载后启动定时器\r\n    this.startProgressInterval();\r\n    this.updateTime();\r\n  },\r\n  updated() {\r\n    this.scrollToBottom();\r\n  },\r\n  beforeDestroy() {\r\n    // 在组件销毁前清除定时器\r\n    this.stopProgressInterval();\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .el-card__body {\r\n  padding: 0;\r\n}\r\n</style>\r\n\r\n<style scoped lang=\"scss\">\r\n.bidOpeningHall {\r\n  position: relative;\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  justify-content: center;\r\n  align-content: flex-start;\r\n  align-items: flex-start;\r\n}\r\n.box-card {\r\n  min-height: 600px;\r\n  width: 50%;\r\n  margin: 15px 5px;\r\n}\r\n.im {\r\n  .im-title {\r\n    width: 100%;\r\n    height: 50px;\r\n    background: #176adb;\r\n\r\n    font-weight: 500;\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    letter-spacing: 0;\r\n\r\n    line-height: 50px;\r\n    text-align: center;\r\n  }\r\n  .im-content {\r\n    margin: 10px;\r\n    background: #f5f5f5;\r\n    // height: 450px;\r\n    overflow-y: auto;\r\n  }\r\n  .im-operation {\r\n    display: flex;\r\n    margin: 0 10px;\r\n    margin-bottom: 10px;\r\n    overflow: auto;\r\n  }\r\n}\r\n.im-content {\r\n  .word {\r\n    display: flex;\r\n    margin-bottom: 30px;\r\n\r\n    img {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n    }\r\n    .info {\r\n      width: 47%;\r\n      margin-left: 10px;\r\n      .Sender_time {\r\n        padding-right: 12px;\r\n        padding-top: 5px;\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n      }\r\n      .message_time {\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n        line-height: 20px;\r\n        margin-top: -5px;\r\n        margin-top: 5px;\r\n      }\r\n      .info-content {\r\n        word-break: break-all;\r\n        // max-width: 45%;\r\n        display: inline-block;\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        background: #fff;\r\n        position: relative;\r\n        margin-top: 8px;\r\n        background: #dbdbdb;\r\n        border-radius: 4px;\r\n      }\r\n      //小三角形\r\n      .info-content::before {\r\n        position: absolute;\r\n        left: -8px;\r\n        top: 8px;\r\n        content: \"\";\r\n        border-right: 10px solid #dbdbdb;\r\n        border-top: 8px solid transparent;\r\n        border-bottom: 8px solid transparent;\r\n      }\r\n    }\r\n  }\r\n\r\n  .word-my {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    // margin-bottom: 30px;\r\n    img {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n    }\r\n    .info {\r\n      width: 90%;\r\n      // margin-left: 10px;\r\n      text-align: right;\r\n      // position: relative;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      flex-wrap: wrap;\r\n      flex-direction: column;\r\n      .info-content {\r\n        word-break: break-all;\r\n        max-width: 45%;\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        // float: right;\r\n        margin-right: 10px;\r\n        position: relative;\r\n        margin-top: 8px;\r\n        background: #a3c3f6;\r\n        text-align: left;\r\n        border-radius: 4px;\r\n      }\r\n      .Sender_time {\r\n        padding-right: 12px;\r\n        padding-top: 5px;\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n      }\r\n      //小三角形\r\n      .info-content::after {\r\n        position: absolute;\r\n        right: -8px;\r\n        top: 8px;\r\n        content: \"\";\r\n        border-left: 10px solid #a3c3f6;\r\n        border-top: 8px solid transparent;\r\n        border-bottom: 8px solid transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n.countdown-timer {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #fff;\r\n  margin: 30px 0;\r\n\r\n  width: 30%;\r\n  height: 70px;\r\n\r\n  background: #176adb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.quote-button {\r\n  width: 100px;\r\n  background: #176adb;\r\n\r\n  color: #fff;\r\n  font-weight: 700;\r\n\r\n  border-radius: 5px;\r\n}\r\n.exper-title {\r\n  height: 45px;\r\n  background: #176adb;\r\n  display: flex;\r\n  justify-content: center;\r\n\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  letter-spacing: 0;\r\n  align-items: center;\r\n}\r\n.expert-title-second {\r\n  height: 40px;\r\n  background: #1c57a7;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n.decryption-countdown {\r\n  width: 50%;\r\n  height: 105px;\r\n  margin: 10px 25%;\r\n\r\n  // background: #176adb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  ::v-deep .number {\r\n    color: #000;\r\n    font-weight: 700;\r\n  }\r\n}\r\n</style>\r\n"]}]}