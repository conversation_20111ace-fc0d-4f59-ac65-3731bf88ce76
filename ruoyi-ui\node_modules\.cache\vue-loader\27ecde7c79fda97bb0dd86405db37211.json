{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidder\\notice\\add.vue?vue&type=template&id=46da06bb&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidder\\notice\\add.vue", "mtime": 1753957830693}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750996951484}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}