{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\three.vue?vue&type=style&index=0&id=267b81ab&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\three.vue", "mtime": 1753948114148}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnRocmVlIHsNCiAgcGFkZGluZzogMjBweCA0MHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KfQ0KLmVsLWhlYWRlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGNvbG9yOiAjMzMzOw0KICBmb250LXNpemU6IDI2cHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbGluZS1oZWlnaHQ6IDEwMHB4Ow0KICBib3JkZXItYm90dG9tOiAjMzMzIDFweCBzb2xpZDsNCn0NCi5lbC1tYWluIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgY29sb3I6ICMzMzM7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbGluZS1oZWlnaHQ6IDYwcHg7DQp9DQouaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBmb250LXNpemU6IDE4cHg7DQogIG1hcmdpbi1ib3R0b206IDgwcHg7DQogIC5pdGVtLXRpdGxlIHsNCiAgICB3aWR0aDogMTIwcHg7DQogICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgIHRleHQtYWxpZ246IGxlZnQ7DQogIH0NCn0NCi5pdGVtLWJ1dHRvbiB7DQogIHdpZHRoOiAxNTBweDsNCiAgaGVpZ2h0OiA0MHB4Ow0KICBtYXJnaW46IDIwcHggMjhweDsNCiAgY29sb3I6ICNmZmY7DQogIGJhY2tncm91bmQtY29sb3I6ICMxNzZhZGI7DQogIGJvcmRlcjogMDsNCiAgJjpob3ZlciB7DQogICAgY29sb3I6ICNmZmY7DQogIH0NCn0NCi5pdGVtLWJ1dHRvbi1yZWQgew0KICB3aWR0aDogMTUwcHg7DQogIGhlaWdodDogNDBweDsNCiAgbWFyZ2luOiAyMHB4IDI4cHg7DQogIGNvbG9yOiAjZmZmOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTkyOTAwOw0KICBib3JkZXI6IDA7DQogICY6aG92ZXIgew0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQp9DQoucmVzdWx0IHsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCiAgbWFyZ2luLWxlZnQ6IDIwcHg7DQp9DQoub3BlcmF0aW9uIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoudGV4dCB7DQogIDo6di1kZWVwIC5lbC10ZXh0YXJlYV9faW5uZXIgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7DQogICAgYm9yZGVyLXJhZGl1czogMDsNCiAgICBib3JkZXI6IDFweCBzb2xpZCAjZjVmNWY1Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["three.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAolBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "three.vue", "sourceRoot": "src/views/expertReview/qualification", "sourcesContent": ["<template>\r\n  <div class=\"three\">\r\n    <div style=\"width:70%\">\r\n      <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px\">资格性评审</div>\r\n\r\n      <el-table :data=\"tableData\" border style=\"width: 100%\" :header-cell-style=\"headStyle\" :cell-style=\"cellStyle\">\r\n        <el-table-column prop=\"供应商名称\" width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column v-for=\"(item, index) in columns\" :key=\"index\" :prop=\"item.xm\" :label=\"item.xm\">\r\n          <template slot-scope=\"scope\">\r\n\t          <span v-if=\"scope.row[item.xm] == '/'\">未提交</span>\r\n            <span v-if=\"scope.row[item.xm] == '1'\">通过</span>\r\n            <span v-if=\"scope.row[item.xm] == '0'\">不通过</span>\r\n            <!-- <i v-else style=\"color:#176ADB;font-size:20px\" :class=\"getIconClass(scope.row[item.xm])\"></i> -->\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\t    \r\n      <div class=\"result\">\r\n        <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;margin-bottom:20px\">评审结果：</div>\r\n        <div style=\"display: flex;margin-left:30px\">\r\n          <div style=\"margin-right:30px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;\" v-for=\"(item,index) in (Array.isArray(result) ? result : [])\" :key=\"index\">\r\n            {{ item.gys }}：\r\n            <span v-if=\"item.result\" style=\"color:green\">\r\n              通过\r\n            </span>\r\n            <span v-else style=\"color:red\">\r\n              不通过\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"operation\" v-if=\"!finish\">\r\n        <el-button\r\n          class=\"item-button\"\r\n          style=\"background-color: #f5f5f5;color: #176adb;\"\r\n          @click=\"reviewed\"\r\n        >重新评审</el-button>\r\n        <el-button class=\"item-button\" v-if=\"passedSupplierCount >= 3\" @click=\"completed\">节点评审完成</el-button>\r\n        <el-button class=\"item-button-red\"\r\n                   v-if=\"!hasIncompleteExpert\"\r\n                   :disabled=\"passedSupplierCount >= 3\"\r\n                   :style=\"passedSupplierCount >= 3 ? 'background-color: #ccc; color: #fff; cursor: not-allowed;' : ''\"\r\n                   @click=\"flowLabel\">流标</el-button>\r\n      </div>\r\n      <div v-else class=\"operation\">\r\n        <el-button class=\"item-button\" @click=\"back\">返回</el-button>\r\n      </div>\r\n    </div>\r\n    <div style=\"width:30%\">\r\n      <div class=\"result\">\r\n        <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px\">表决结果</div>\r\n        <el-input disabled class=\"text\" type=\"textarea\" :rows=\"20\" placeholder=\"请输入表决结果\" v-model=\"votingResults\">\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n    <el-dialog title=\"流标情况说明\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-input type=\"textarea\" :rows=\"4\" placeholder=\"请输入内容\" v-model=\"reasonFlowBid\">\r\n      </el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmflow\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\n// 导入专家评审相关API接口\r\nimport {\r\n  leaderSummaryQuery,  // 组长汇总查询接口\r\n  reEvaluate,         // 重新评审接口\r\n  expertInfoById,     // 根据ID获取专家信息接口\r\n} from \"@/api/expert/review\";\r\n\r\n// 导入评审流程相关API接口\r\nimport { updateProcess } from \"@/api/evaluation/process\";        // 更新评审流程接口\r\nimport { abortiveTenderNotice } from \"@/api/bidder/notice\";      // 流标通知接口\r\nimport { reEvaluationTwo } from \"@/api/evaluation/expertStatus\"; // 重新评审状态接口\r\n\r\nexport default {\r\n  // 组件属性定义\r\n  props: {\r\n    // 是否完成评审的标识\r\n    finish: {\r\n      type: Boolean,    // 布尔类型\r\n      default: false,   // 默认值为false，表示未完成\r\n    },\r\n  },\r\n  // 组件数据定义\r\n  data() {\r\n    return {\r\n      // 表格数据数组，存储评审表格的行数据\r\n      tableData: [],\r\n\r\n      // 表格列配置对象，存储表格列的定义信息\r\n      columns: {},\r\n\r\n      // 评审结果数组，存储每个供应商的评审结果\r\n      result: [], // 修改：初始化为数组而不是对象\r\n\r\n      // 表决结果文本，存储专家组的表决意见\r\n      votingResults: \"\",\r\n\r\n      // 流标原因，当需要流标时填写的原因\r\n      reasonFlowBid: \"\",\r\n\r\n      // 对话框显示状态控制\r\n      dialogVisible: false,\r\n\r\n      // 未评审专家列表，存储尚未完成评审的专家信息\r\n      unreviewedExperts: [],\r\n\r\n      // 组长信息对象\r\n      leader: {},\r\n\r\n      // 定时器ID，用于清除定时器\r\n      intervalId: null,\r\n\r\n      // 表格头部样式配置\r\n      headStyle: {\r\n        \"text-align\": \"center\",                    // 文字居中对齐\r\n        \"font-family\": \"SourceHanSansSC-Bold\",     // 字体家族\r\n        background: \"#176ADB\",                     // 背景色（蓝色）\r\n        color: \"#fff\",                             // 文字颜色（白色）\r\n        \"font-size\": \"16px\",                       // 字体大小\r\n        \"font-weight\": \"700\",                      // 字体粗细（加粗）\r\n        border: \"0\",                               // 边框设置\r\n      },\r\n\r\n      // 表格单元格样式配置\r\n      cellStyle: {\r\n        \"text-align\": \"center\",                    // 文字居中对齐\r\n        \"font-family\": \"SourceHanSansSC-Bold\",     // 字体家族\r\n        height: \"60px\",                            // 单元格高度\r\n        color: \"#000\",                             // 文字颜色（黑色）\r\n        \"font-size\": \"14px\",                       // 字体大小\r\n        \"font-weight\": \"700\",                      // 字体粗细（加粗）\r\n      },\r\n    };\r\n  },\r\n  // 组件方法定义\r\n  methods: {\r\n    /**\r\n     * 初始化方法\r\n     * 获取评审数据并处理显示\r\n     */\r\n    init() {\r\n      // 构建请求参数\r\n      const data = {\r\n        projectId: this.$route.query.projectId,           // 项目ID（从路由参数获取）\r\n        itemId: this.$route.query.scoringMethodItemId,    // 评分方法项目ID（从路由参数获取）\r\n      };\r\n\r\n      // 调用组长汇总查询接口\r\n      leaderSummaryQuery(data).then((response) => {\r\n        if (response.code == 200) {\r\n          // 请求成功，处理返回数据\r\n\t        \r\n          // 设置表决结果文本\r\n          this.votingResults = response.data.bjjgsb;\r\n\r\n          // 设置未评审专家列表\r\n          this.unreviewedExperts = response.data.wpszj;\r\n\r\n          // 转换并设置表格数据\r\n          this.tableData = this.transformData(\r\n            response.data.tableColumns,    // 表格列配置\r\n            response.data.busiBidderInfos, // 投标人信息\r\n            response.data.tableData        // 原始表格数据\r\n          );\r\n\r\n          // 过滤掉已废标的数据（isAbandonedBid == 0 表示未废标）\r\n          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)\r\n\r\n          // 设置表格列配置\r\n          this.columns = response.data.tableColumns;\r\n\r\n          // 生成评审结果表\r\n          this.result = this.generateResultTable(\r\n            response.data.tableColumns,    // 表格列配置\r\n            response.data.busiBidderInfos, // 投标人信息\r\n            response.data.tableData        // 原始表格数据\r\n          );\r\n\r\n          console.log(this.result)\r\n\r\n          // 添加安全检查：确保 result 是数组后再进行过滤\r\n          if (Array.isArray(this.result)) {\r\n            // 过滤掉已废标的评审结果\r\n            this.result = this.result.filter(item => item.isAbandonedBid == 0)\r\n          } else {\r\n            console.error(\"generateResultTable did not return an array:\", this.result);\r\n            this.result = []; // 设置为空数组作为后备\r\n          }\r\n\r\n          console.log(this.tableData)\r\n\t        \r\n        } else {\r\n          // 请求失败，显示警告信息\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    /**\r\n     * 数据转换函数\r\n     * 将原始数据转换为表格显示所需的格式\r\n     * @param {Array} tableColumns - 表格列配置数组\r\n     * @param {Array} busiBidderInfos - 投标人信息数组\r\n     * @param {Array} tableData - 原始表格数据数组\r\n     * @returns {Array} 转换后的表格数据\r\n     */\r\n    transformData(tableColumns, busiBidderInfos, tableData) {\r\n      // 创建投标人ID到投标人信息的映射\r\n      // 用于快速查找投标人名称和废标状态\r\n      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {\r\n        acc[info.bidderId] = {\r\n          bidderName: info.bidderName,                    // 投标人名称\r\n          isAbandonedBid: info.isAbandonedBid || 0        // 是否废标（默认为0，表示未废标）\r\n        };\r\n        return acc;\r\n      }, {});\r\n\t\t\t\r\n      // 创建结果ID到项目名称的映射（虽然当前未使用，但保留以备后用）\r\n      const columnIdToName = tableColumns.reduce((acc, column) => {\r\n        acc[column.resultId] = column.xm;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 转换原始数据为表格显示格式\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;  // 供应商ID\r\n        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];\r\n\r\n        // 初始化转换后的行数据，包含供应商名称和废标状态\r\n        const transformedRow = {\r\n          供应商名称: bidderName,\r\n          isAbandonedBid: isAbandonedBid\r\n        };\r\n\r\n        // 遍历表格列配置，将对应的评估结果添加到行数据中\r\n        tableColumns.forEach((column) => {\r\n          const itemId = column.resultId;  // 评估项ID\r\n          // 设置评估结果，如果没有找到对应值则默认为'/'(没有评完)\r\n          transformedRow[column.xm] = row[itemId] || \"/\";\r\n        });\r\n\r\n        return transformedRow;\r\n      });\r\n    },\r\n    /**\r\n     * 组装评审结果方法\r\n     * 根据评审数据生成最终的评审结果，采用少数服从多数的原则\r\n     * @param {Array} tableColumns - 表格列配置数组\r\n     * @param {Array} busiBidderInfos - 投标人信息数组\r\n     * @param {Array} tableData - 原始表格数据数组\r\n     * @returns {Array} 评审结果数组，每个元素包含投标人信息和评审结果\r\n     */\r\n    generateResultTable(tableColumns, busiBidderInfos, tableData) {\r\n      // 添加安全检查：确保输入参数都是数组\r\n      if (!Array.isArray(tableColumns) || !Array.isArray(busiBidderInfos) || !Array.isArray(tableData)) {\r\n        console.error(\"generateResultTable: Invalid input parameters\", {\r\n          tableColumns: tableColumns,\r\n          busiBidderInfos: busiBidderInfos,\r\n          tableData: tableData\r\n        });\r\n        return []; // 返回空数组作为后备\r\n      }\r\n\r\n      // 提取所有评估项的ID\r\n      const entMethodItemIds = tableColumns.map((item) => {\r\n        return item.resultId;\r\n      });\r\n\r\n      // 创建投标人ID到投标人信息的映射\r\n      // 使用Map数据结构提高查找效率\r\n      const bidderMap = new Map(\r\n        busiBidderInfos.map((bidder) => [bidder.bidderId, {\r\n          bidderName: bidder.bidderName,                    // 投标人名称\r\n          isAbandonedBid: bidder.isAbandonedBid || 0        // 是否废标状态\r\n        }])\r\n      );\r\n\r\n      // 生成结果表，按照少数服从多数规则判断是否通过\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;  // 获取供应商ID\r\n\r\n        // 从映射中获取投标人信息\r\n        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);\r\n\r\n        // 计算评审结果\r\n        const totalItems = entMethodItemIds.length;  // 总评估项数量\r\n        let passedCount = 0;  // 通过的评估项数量\r\n\r\n        // 遍历所有评估项，统计通过数量\r\n        entMethodItemIds.forEach((key) => {\r\n          if (row[key] == \"1\") {  // \"1\" 表示该评估项通过\r\n            passedCount++;\r\n          }\r\n        });\r\n\r\n        // 少数服从多数原则：通过数量 >= 总数的一半（向上取整）则判定为通过\r\n        const result = passedCount >= Math.ceil(totalItems / 2);\r\n\r\n        // 返回评审结果对象\r\n        return {\r\n          bidder: supplierId,           // 投标人ID\r\n          gys: bidderName,              // 投标人名称\r\n          isAbandonedBid: isAbandonedBid, // 是否废标\r\n          result: result,               // 评审结果（true: 通过, false: 不通过）\r\n        };\r\n      });\r\n    },\r\n    /**\r\n     * 节点评审完成方法\r\n     * 处理评审完成的逻辑，包括验证和提交评审结果\r\n     */\r\n    completed() {\r\n      // 检查是否有未完成评审的专家\r\n      if (this.unreviewedExperts && this.unreviewedExperts.length > 0){\r\n        // 收集未评审专家的姓名\r\n        let result = [];\r\n        for(let i = 0; i < this.unreviewedExperts.length; i++){\r\n          result.push(this.unreviewedExperts[i].xm)\r\n        }\r\n        // 显示错误提示，列出未完成评审的专家\r\n        this.$message.error(`专家${result.join(\"、\")}未完成评审！`);\r\n        return\r\n      }\r\n\r\n      // 二次确认提示\r\n      this.$confirm('确认完成节点评审？提交后将无法修改评审结果。', '确认完成评审', {\r\n        confirmButtonText: '确定完成',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 所有专家都已完成评审，继续处理评审完成逻辑\r\n\r\n        // 从本地存储获取评审流程ID\r\n        const evaluationProcessId = JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        );\r\n\r\n        // 构建更新评审流程的请求数据\r\n        const data = {\r\n          evaluationProcessId: evaluationProcessId.evaluationProcessId,  // 评审流程ID\r\n          evaluationResult: JSON.stringify(this.result),                 // 评审结果（JSON字符串）\r\n          evaluationState: 2,                                            // 评审状态（2表示已完成）\r\n          evaluationResultRemark: this.votingResults,                    // 评审结果备注\r\n        };\r\n\r\n        // 调用更新评审流程接口\r\n        updateProcess(data).then((response) => {\r\n          if (response.code == 200) {\r\n            // 更新成功，跳转到专家信息页面\r\n            this.$router.push({\r\n              path: \"/expertInfo\",\r\n              query: {\r\n                projectId: this.$route.query.projectId,  // 项目ID\r\n                zjhm: this.$route.query.zjhm,            // 专家号码\r\n              },\r\n            });\r\n          } else {\r\n            // 更新失败，显示警告信息\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      }).catch(() => {\r\n        this.$message.info('已取消完成评审');\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 返回方法\r\n     * 返回到专家信息页面\r\n     */\r\n    back() {\r\n      this.$router.push({\r\n        path: \"/expertInfo\",\r\n        query: {\r\n          projectId: this.$route.query.projectId,  // 项目ID\r\n          zjhm: this.$route.query.zjhm,            // 专家号码\r\n        },\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 获取图标样式类名\r\n     * 根据评审结果值返回对应的图标类名\r\n     * @param {string} value - 评审结果值\r\n     * @returns {string} 图标类名\r\n     */\r\n    getIconClass(value) {\r\n      if (value == \"1\"){\r\n        return \"el-icon-check\"           // 通过：显示勾选图标\r\n      }\r\n\r\n      if (value == \"0\"){\r\n        return \"el-icon-circle-close\"    // 不通过：显示关闭图标\r\n      }\r\n\r\n      return value  // 其他情况直接返回原值\r\n    },\r\n\r\n    /**\r\n     * 重新评审方法\r\n     * 触发重新评审流程，重置评审状态\r\n     */\r\n    reviewed() {\r\n      // 从本地存储获取专家评分信息，构建查询参数\r\n      const query = {\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).projectEvaluationId,                                          // 项目评审ID\r\n        expertResultId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\"))\r\n          .expertResultId,                                              // 专家结果ID\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).scoringMethodItemId,                                          // 评分方法项目ID\r\n      };\r\n\r\n      // 调用重新评审接口\r\n      reEvaluationTwo(query).then((res) => {\r\n        if (res.code == 200) {\r\n          // 第一步成功，获取评审流程ID\r\n          const evaluationProcessId = JSON.parse(\r\n            localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n          );\r\n\r\n          // 调用重新评审接口\r\n          reEvaluate(evaluationProcessId.evaluationProcessId).then(\r\n            (response) => {\r\n              if (response.code == 200) {\r\n                // 触发重新评审通知，通知其他专家页面\r\n                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {\r\n                  this.$parent.triggerReEvaluationNotification();\r\n                }\r\n                // 重新评审成功，发送事件通知父组件切换到第一步\r\n                this.$emit(\"send\", \"one\");\r\n              } else {\r\n                // 重新评审失败，显示警告信息\r\n                this.$message.warning(response.msg);\r\n              }\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 流标方法\r\n     * 显示流标确认对话框\r\n     */\r\n    flowLabel() {\r\n      this.dialogVisible = true;\r\n    },\r\n    /**\r\n     * 确认流标方法\r\n     * 处理流标确认逻辑，发送流标通知\r\n     */\r\n    confirmflow() {\r\n      // 验证流标原因是否填写\r\n      if (this.reasonFlowBid == \"\") {\r\n        this.$message.warning(\"请完善情况说明\")\r\n        return;\r\n      }\r\n\r\n      // 构建流标通知请求数据\r\n      const data = {\r\n        projectId: this.$route.query.projectId,              // 项目ID\r\n        abortiveType: 3,                                     // 流标类型（3表示资格性评审流标）\r\n        remark: this.reasonFlowBid,                          // 流标原因说明\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项目ID\r\n      };\r\n\r\n      // 调用流标通知接口\r\n      abortiveTenderNotice(data).then((response) => {\r\n        if (response.code == 200) {\r\n          // 流标通知发送成功，跳转到汇总页面\r\n          const query = {\r\n            projectId: this.$route.query.projectId,          // 项目ID\r\n            zjhm: this.$route.query.zjhm,                    // 专家号码\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项目ID\r\n          };\r\n          this.$router.push({ path: \"/summary\", query: query });\r\n        } else {\r\n          // 流标通知发送失败，显示警告信息\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 清除定时器的通用方法\r\n     * 在多个生命周期钩子中调用，确保定时器被正确清除\r\n     */\r\n    clearTimer() {\r\n      if (this.intervalId) {\r\n        clearInterval(this.intervalId);\r\n        this.intervalId = null;\r\n        console.log(\"定时器已清除 - qualification/three.vue\");\r\n      }\r\n    },\r\n  },\r\n\r\n  // 计算属性\r\n  computed:{\r\n    /**\r\n     * 通过评审的供应商数量\r\n     * 统计评审结果中通过的供应商数量\r\n     * @returns {number} 通过评审的供应商数量\r\n     */\r\n    passedSupplierCount() {\r\n      console.log(\"this.result:\",this.result);\r\n      // 添加安全检查：确保 result 是数组\r\n      if (!Array.isArray(this.result)) {\r\n        console.warn(\"result is not an array:\", this.result);\r\n        return 0;\r\n      }\r\n      // 过滤出评审结果为通过的供应商，返回数量\r\n      return this.result.filter(item => item.result).length;\r\n    },\r\n\r\n    /**\r\n     * 检查是否有专家未完成评审\r\n     * 遍历tableData检查是否存在\"/\"状态（未评完）\r\n     * @returns {boolean} true表示有未完成评审的专家，false表示所有专家都已完成评审\r\n     */\r\n    hasIncompleteExpert() {\r\n      // 添加安全检查：确保 tableData 是数组\r\n      if (!Array.isArray(this.tableData)) {\r\n        console.warn(\"tableData is not an array:\", this.tableData);\r\n        return true; // 数据异常时，默认禁用流标按钮\r\n      }\r\n\r\n      // 遍历所有供应商的评审数据\r\n      const hasIncomplete = this.tableData.some(row => {\r\n        // 遍历每一行的所有属性，查找是否有\"/\"状态\r\n        return Object.keys(row).some(key => {\r\n          // 排除供应商名称和废标状态字段，只检查专家评审结果\r\n          if (key !== '供应商名称' && key !== 'isAbandonedBid') {\r\n            return row[key] === '/';\r\n          }\r\n          return false;\r\n        });\r\n      });\r\n\r\n      // 输出调试信息\r\n      console.log(\"hasIncompleteExpert:\", hasIncomplete, \"tableData:\", this.tableData);\r\n      return hasIncomplete;\r\n    }\r\n  },\r\n\r\n  // 生命周期钩子\r\n  /**\r\n   * 组件挂载完成后执行\r\n   * 初始化组件数据和定时刷新\r\n   */\r\n  mounted() {\r\n    // 初始化数据\r\n    this.init();\r\n\r\n    // 设置定时器，每5秒自动刷新数据\r\n    // 用于实时更新评审状态和结果\r\n    this.intervalId = setInterval(()=>{\r\n      this.init();\r\n    },5000)\r\n  },\r\n\r\n  /**\r\n   * 组件销毁前执行\r\n   * 清除定时器，防止内存泄漏\r\n   */\r\n  beforeDestroy() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 组件完全销毁后执行\r\n   * 作为额外的安全措施清除定时器\r\n   */\r\n  destroyed() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 如果父组件使用了keep-alive，在组件失活时清除定时器\r\n   */\r\n  deactivated() {\r\n    this.clearTimer();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.three {\r\n  padding: 20px 40px;\r\n  display: flex;\r\n}\r\n.el-header {\r\n  background-color: #fff;\r\n  color: #333;\r\n  font-size: 26px;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  border-bottom: #333 1px solid;\r\n}\r\n.el-main {\r\n  background-color: #fff;\r\n  color: #333;\r\n  text-align: center;\r\n  line-height: 60px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.item-button {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #176adb;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.item-button-red {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #e92900;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.result {\r\n  text-align: left;\r\n  margin-left: 20px;\r\n}\r\n.operation {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n</style>\r\n"]}]}