{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\three.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\three.vue", "mtime": 1753948158725}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["three.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "three.vue", "sourceRoot": "src/views/expertReview/compliance", "sourcesContent": ["<template>\r\n  <div class=\"three\">\r\n    <div style=\"width:70%\">\r\n      <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px\">符合性评审</div>\r\n      <el-table :data=\"tableData\" border style=\"width: 100%\" :header-cell-style=\"headStyle\" :cell-style=\"cellStyle\">\r\n        <el-table-column prop=\"供应商名称\" width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column v-for=\"(item, index) in columns\" :key=\"index\" :prop=\"item.xm\" :label=\"item.xm\">\r\n          <template slot-scope=\"scope\">\r\n            <!-- <span v-if=\"scope.row[item.xm] == '/'\"> -->\r\n<!--              {{ scope.row[item.xm] }}-->\r\n            <!-- 未提交 -->\r\n            <!-- </span> -->\r\n            <!-- <i v-else style=\"color:#176ADB;font-size:20px\" :class=\"getIconClass(scope.row[item.xm])\"></i> -->\r\n\r\n            <span v-if=\"scope.row[item.xm] == '/'\">未提交</span>\r\n            <span v-if=\"scope.row[item.xm] == '1'\">通过</span>\r\n            <span v-if=\"scope.row[item.xm] == '0'\">不通过</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"result\">\r\n        <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;margin:20px 0\">评审结果：</div>\r\n        <div style=\"display: flex;margin-left:30px\">\r\n          <div style=\"margin-right:30px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;\" v-for=\"(item,index) in (Array.isArray(result) ? result : [])\" :key=\"index\">\r\n            {{ item.gys }}：\r\n            <span v-if=\"item.result\" style=\"color:green\">\r\n              通过\r\n            </span>\r\n            <span v-else style=\"color:red\">\r\n              不通过\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"operation\" v-if=\"!finish\">\r\n        <el-button\r\n          class=\"item-button\"\r\n          style=\"background-color: #f5f5f5;color: #176adb;\"\r\n          @click=\"reviewed\"\r\n        >重新评审</el-button>\r\n        <el-button class=\"item-button\" v-if=\"passedSupplierCount >= 3\" @click=\"completed\">节点评审完成</el-button>\r\n        <el-button class=\"item-button-red\"\r\n                   v-if=\"!hasIncompleteExpert\"\r\n                   :disabled=\"passedSupplierCount >= 3\"\r\n                   :style=\"passedSupplierCount >= 3 ? 'background-color: #ccc; color: #fff; cursor: not-allowed;' : ''\"\r\n                   @click=\"flowLabel\">流标</el-button>\r\n      </div>\r\n      <div v-else class=\"operation\">\r\n        <el-button class=\"item-button\" @click=\"back\">返回</el-button>\r\n      </div>\r\n    </div>\r\n    <div style=\"width:30%\">\r\n      <div class=\"result\">\r\n        <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px\">表决结果</div>\r\n        <el-input disabled class=\"text\" type=\"textarea\" :rows=\"20\" placeholder=\"请输入表决结果\" v-model=\"votingResults\">\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n    <el-dialog title=\"流标情况说明\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-input type=\"textarea\" :rows=\"4\" placeholder=\"请输入内容\" v-model=\"reasonFlowBid\">\r\n      </el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmflow\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { leaderSummaryQuery, reEvaluate } from \"@/api/expert/review\";\r\nimport { updateProcess } from \"@/api/evaluation/process\";\r\nimport { abortiveTenderNotice } from \"@/api/bidder/notice\";\r\nimport { reEvaluationTwo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  props: {\r\n    finish: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      columns: {},\r\n      result: [], // 修改：初始化为数组而不是对象\r\n\r\n      votingResults: \"\",\r\n      reasonFlowBid: \"\",\r\n      dialogVisible: false,\r\n\r\n      // 定时器ID，用于清除定时器\r\n      intervalId: null,\r\n\r\n      leader: {},\r\n      headStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        background: \"#176ADB\",\r\n        color: \"#fff\",\r\n        \"font-size\": \"16px\",\r\n        \"font-weight\": \"700\",\r\n        border: \"0\",\r\n      },\r\n      cellStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        height: \"60px\",\r\n        color: \"#000\",\r\n        \"font-size\": \"14px\",\r\n        \"font-weight\": \"700\",\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const data = {\r\n        projectId: this.$route.query.projectId,\r\n        itemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      leaderSummaryQuery(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.votingResults = response.data.bjjgsb\r\n          this.tableData = this.transformData(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)\r\n          this.columns = response.data.tableColumns;\r\n          this.result = this.generateResultTable(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n          this.result = this.result.filter(item => item.isAbandonedBid == 0)\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    // 转换函数\r\n    transformData(tableColumns, busiBidderInfos, tableData) {\r\n      // 创建一个映射，用于将 bidderId 映射到 bidderName\r\n      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {\r\n        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };\r\n        return acc;\r\n      }, {});\r\n\r\n      // 创建一个映射，用于将 resultId 映射到 itemName\r\n      const columnIdToName = tableColumns.reduce((acc, column) => {\r\n        acc[column.resultId] = column.xm;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 转换数据\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;\r\n        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];\r\n        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };\r\n\r\n        // 只取 tableColumns 中定义的评估项\r\n        tableColumns.forEach((column) => {\r\n          const itemId = column.resultId;\r\n          transformedRow[column.xm] = row[itemId] || \"/\"; // 默认为'/'(没有评完)\r\n        });\r\n\r\n        return transformedRow;\r\n      });\r\n    },\r\n    // 组装评审结果\r\n    //少数服从多数\r\n    generateResultTable(tableColumns, busiBidderInfos, tableData) {\r\n      const entMethodItemIds = tableColumns.map((item) => {\r\n        return item.resultId;\r\n      });\r\n\r\n      // Create a map from bidderId to bidderName\r\n      const bidderMap = new Map(\r\n        busiBidderInfos.map((bidder) => [bidder.bidderId, {\r\n          bidderName: bidder.bidderName,\r\n          isAbandonedBid: bidder.isAbandonedBid || 0 // 如果 isAbandonedBid 不存在，则默认为 0\r\n        }])\r\n      );\r\n\r\n      // 生成结果表，按照少数服从多数规则判断是否通过\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;\r\n        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);\r\n        const totalItems = entMethodItemIds.length;\r\n        let passedCount = 0;\r\n        entMethodItemIds.forEach((key) => {\r\n          if (row[key] == \"1\") {\r\n            passedCount++;\r\n          }\r\n        });\r\n        const result = passedCount >= Math.ceil(totalItems / 2);\r\n        return {\r\n          bidder: supplierId,\r\n          gys: bidderName,\r\n          isAbandonedBid: isAbandonedBid,\r\n          result: result,\r\n        };\r\n      });\r\n    },\r\n/*  只要有一个是不通过就算不同过\r\n    generateResultTable(tableColumns, busiBidderInfos, tableData) {\r\n      const entMethodItemIds = tableColumns.map((item) => {\r\n        return item.resultId;\r\n      });\r\n\r\n      // Create a map from bidderId to bidderName\r\n      const bidderMap = new Map(\r\n        busiBidderInfos.map((bidder) => [bidder.bidderId, {\r\n          bidderName: bidder.bidderName,\r\n          isAbandonedBid: bidder.isAbandonedBid || 0 // 如果 isAbandonedBid 不存在，则默认为 0\r\n        }])\r\n      );\r\n\r\n      // Generate the result table、\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;\r\n        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);\r\n        var result = true;\r\n        const temp = entMethodItemIds.every((key) => {\r\n          return row[key] == \"1\";\r\n        });\r\n        if (!temp) {\r\n          result = false;\r\n        }\r\n        return {\r\n          bidder: supplierId,\r\n          gys: bidderName,\r\n          isAbandonedBid: isAbandonedBid,\r\n          result: result,\r\n        };\r\n      });\r\n    },\r\n*/\r\n    // 节点评审完成\r\n    completed() {\r\n\t\t\t\r\n\t\t\t\r\n      const evaluationProcessId = JSON.parse(\r\n        localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n      );\r\n      const data = {\r\n        evaluationProcessId: evaluationProcessId.evaluationProcessId,\r\n        evaluationResult: JSON.stringify(this.result),\r\n        evaluationState: 2,\r\n        evaluationResultRemark: this.votingResults,\r\n      };\r\n      updateProcess(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$router.push({\r\n            path: \"/expertInfo\",\r\n            query: {\r\n              projectId: this.$route.query.projectId,\r\n              zjhm: this.$route.query.zjhm,\r\n              tips: true,\r\n\t            tenderMode:1\r\n            },\r\n          });\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.$router.push({\r\n        path: \"/expertInfo\",\r\n        query: {\r\n          projectId: this.$route.query.projectId,\r\n          zjhm: this.$route.query.zjhm,\r\n        },\r\n      });\r\n    },\r\n    getIconClass(value) {\r\n\t    if (value == \"1\"){\r\n\t\t    return \"el-icon-check\"           // 通过：显示勾选图标\r\n\t    }\r\n\t    \r\n\t    if (value == \"0\"){\r\n\t\t    return \"el-icon-circle-close\"    // 不通过：显示关闭图标\r\n\t    }\r\n\t    \r\n\t    return value  // 其他情况直接返回原值\r\n    },\r\n    // 重新评审\r\n    reviewed() {\r\n      const query = {\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).projectEvaluationId,\r\n        expertResultId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\"))\r\n          .expertResultId,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).scoringMethodItemId,\r\n      };\r\n      reEvaluationTwo(query).then((res) => {\r\n        if (res.code == 200) {\r\n          const evaluationProcessId = JSON.parse(\r\n            localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n          );\r\n\r\n          reEvaluate(evaluationProcessId.evaluationProcessId).then(\r\n            (response) => {\r\n              if (response.code == 200) {\r\n                // 触发重新评审通知，通知其他专家页面\r\n                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {\r\n                  this.$parent.triggerReEvaluationNotification();\r\n                }\r\n                this.$emit(\"send\", \"one\");\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n              }\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n    flowLabel() {\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确认流标\r\n    confirmflow() {\r\n      if (this.reasonFlowBid == \"\") {\r\n        this.$message.warning(\"请完善情况说明\");\r\n        return;\r\n      }\r\n      // if (this.reasonFlowBid == \"\") {\r\n      const data = {\r\n        projectId: this.$route.query.projectId,\r\n        abortiveType: 3,\r\n        remark: this.reasonFlowBid,\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      abortiveTenderNotice(data).then((response) => {\r\n        if (response.code == 200) {\r\n          const query = {\r\n            projectId: this.$route.query.projectId,\r\n            zjhm: this.$route.query.zjhm,\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n          };\r\n          this.$router.push({ path: \"/summary\", query: query });\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // } else {\r\n      // }\r\n    },\r\n\r\n    /**\r\n     * 清除定时器的通用方法\r\n     * 在多个生命周期钩子中调用，确保定时器被正确清除\r\n     */\r\n    clearTimer() {\r\n      if (this.intervalId) {\r\n        clearInterval(this.intervalId);\r\n        this.intervalId = null;\r\n        console.log(\"定时器已清除 - compliance/three.vue\");\r\n      }\r\n    },\r\n  },\r\n  computed:{\r\n    passedSupplierCount() {\r\n      console.log(\"this.result:\",this.result);\r\n      // 添加安全检查：确保 result 是数组\r\n      if (!Array.isArray(this.result)) {\r\n        console.warn(\"result is not an array:\", this.result);\r\n        return 0;\r\n      }\r\n      return this.result.filter(item => item.result).length;\r\n    },\r\n\r\n    /**\r\n     * 检查是否有专家未完成评审\r\n     * 遍历tableData检查是否存在\"/\"状态（未评完）\r\n     * @returns {boolean} true表示有未完成评审的专家，false表示所有专家都已完成评审\r\n     */\r\n    hasIncompleteExpert() {\r\n      // 添加安全检查：确保 tableData 是数组\r\n      if (!Array.isArray(this.tableData)) {\r\n        console.warn(\"tableData is not an array:\", this.tableData);\r\n        return true; // 数据异常时，默认禁用流标按钮\r\n      }\r\n\r\n      // 遍历所有供应商的评审数据\r\n      const hasIncomplete = this.tableData.some(row => {\r\n        // 遍历每一行的所有属性，查找是否有\"/\"状态\r\n        return Object.keys(row).some(key => {\r\n          // 排除供应商名称和废标状态字段，只检查专家评审结果\r\n          if (key !== '供应商名称' && key !== 'isAbandonedBid') {\r\n            return row[key] === '/';\r\n          }\r\n          return false;\r\n        });\r\n      });\r\n\r\n      // 输出调试信息\r\n      console.log(\"hasIncompleteExpert:\", hasIncomplete, \"tableData:\", this.tableData);\r\n      return hasIncomplete;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * 组件挂载完成后执行\r\n   * 初始化组件数据和定时刷新\r\n   */\r\n  mounted() {\r\n    // 初始化数据\r\n    this.init();\r\n\r\n    // 设置定时器，每5秒自动刷新数据\r\n    // 用于实时更新评审状态和结果\r\n    this.intervalId = setInterval(()=>{\r\n      this.init();\r\n    },5000)\r\n  },\r\n\r\n  /**\r\n   * 组件销毁前执行\r\n   * 清除定时器，防止内存泄漏\r\n   */\r\n  beforeDestroy() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 组件完全销毁后执行\r\n   * 作为额外的安全措施清除定时器\r\n   */\r\n  destroyed() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 如果父组件使用了keep-alive，在组件失活时清除定时器\r\n   */\r\n  deactivated() {\r\n    this.clearTimer();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.three {\r\n  padding: 20px 40px;\r\n  display: flex;\r\n}\r\n.el-header {\r\n  background-color: #fff;\r\n  color: #333;\r\n  font-size: 26px;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  border-bottom: #333 1px solid;\r\n}\r\n.el-main {\r\n  background-color: #fff;\r\n  color: #333;\r\n  text-align: center;\r\n  line-height: 60px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.item-button {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #176adb;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.item-button-red {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #e92900;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.result {\r\n  text-align: left;\r\n  margin-left: 20px;\r\n}\r\n.operation {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n</style>\r\n"]}]}