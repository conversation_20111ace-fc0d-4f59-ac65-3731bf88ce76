{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\one.vue", "mtime": 1753952513005}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQovLyDluLjph4/lrprkuYkNCmNvbnN0IFBBU1MgPSAnMSc7IC8vIOmAmui/hw0KY29uc3QgRkFJTCA9ICcwJzsgLy8g5LiN6YCa6L+HDQpjb25zdCBDSEVDS19QQVNTID0gJ+ezu+e7n+WInemqjOmAmui/hyc7IC8vIOezu+e7n+WInemqjOmAmui/h+aWh+acrA0KY29uc3QgQ0hFQ0tfRkFJTCA9ICfns7vnu5/liJ3pqozmnKrpgJrov4cnOyAvLyDns7vnu5/liJ3pqozmnKrpgJrov4fmlofmnKwNCg0KaW1wb3J0IHsNCiAgc3VwcGxpZXJJbmZvLCAvLyDojrflj5bkvpvlupTllYbkv6Hmga9BUEkNCiAgYXBwcm92YWxQcm9jZXNzLCAvLyDojrflj5bor4TliIbmlrnms5VBUEkNCiAgc2NvcmluZ0ZhY3RvcnMsIC8vIOaPkOS6pOivhOWIhuWboOWtkEFQSQ0KICBjaGVja1Jldmlld1N1bW1hcnksIC8vIOajgOafpeivhOWuoeaxh+aAu0FQSQ0KICBmaWxlc0J5SWQsIC8vIOiOt+WPlumhueebruebuOWFs+aWh+S7tkFQSQ0KfSBmcm9tICJAL2FwaS9leHBlcnQvcmV2aWV3IjsgLy8g5a+85YWl5LiT5a626K+E5a6h55u45YWzQVBJDQppbXBvcnQgeyBnZXREZXRhaWxCeVBzeHggfSBmcm9tICJAL2FwaS9ldmFsdWF0aW9uL2RldGFpbC8iOyAvLyDojrflj5bor4TliIbor6bmg4VBUEkNCmltcG9ydCB7IGVkaXRFdmFsRXhwZXJ0U2NvcmVJbmZvIH0gZnJvbSAiQC9hcGkvZXZhbHVhdGlvbi9leHBlcnRTdGF0dXMiOyAvLyDnvJbovpHkuJPlrrbor4TliIbnirbmgIFBUEkNCmltcG9ydCB7IHJlc0RvY1Jldmlld0ZhY3RvcnNEZWNpc2lvbiB9IGZyb20gIkAvYXBpL2RvY1Jlc3BvbnNlL2VudEluZm8iOyAvLyDojrflj5blk43lupTmlofku7bor4TlrqHlm6DlrZDlhrPnrZZBUEkNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzdXBwbGllck9wdGlvbnM6IFtdLCAvLyDkvpvlupTllYbkuIvmi4npgInpobnliJfooagNCiAgICAgIHNjb3JpbmdNZXRob2Q6IG51bGwsIC8vIOW9k+WJjeivhOWIhuaWueazleWvueixoQ0KICAgICAgc2VsZWN0ZWRGYWN0b3JOb2RlOiB7fSwgLy8g5b2T5YmN6YCJ5Lit55qE6K+E5YiG5Zug5a2Q6IqC54K5DQogICAgICBzZWxlY3RlZFN1cHBsaWVyTmFtZTogJycsIC8vIOW9k+WJjemAieS4reeahOS+m+W6lOWVhuWQjeensA0KICAgICAgc2VsZWN0ZWRTdXBwbGllcjoge30sIC8vIOW9k+WJjemAieS4reeahOS+m+W6lOWVhuWvueixoQ0KICAgICAgZXhwZXJ0SW5mbzoge30sIC8vIOW9k+WJjeS4k+WutuS/oeaBrw0KICAgICAgcmF0aW5nU3RhdGVNYXA6IHt9LCAvLyDor4TliIbpobnnirbmgIHmmKDlsITvvIhrZXnkuLror4TliIbpoblJRO+8jHZhbHVl5Li6e3N0YXRlLCByZWFzb25977yJDQogICAgICBwcm9qZWN0RmlsZXM6IHt9LCAvLyDpobnnm67nm7jlhbPmlofku7blr7nosaENCiAgICAgIGlzU2hvd1Jlc3BvbnNlOiBmYWxzZSwgLy8g5piv5ZCm5pi+56S65ZON5bqU5paH5Lu2DQogICAgICBpc1Nob3dQcm9jdXJlbWVudDogZmFsc2UsIC8vIOaYr+WQpuaYvuekuumHh+i0reaWh+S7tg0KICAgICAgaXNEb3VibGVWaWV3OiBmYWxzZSwgLy8g5piv5ZCm5Y+M5paH5Lu25a+55q+U5qih5byPDQogICAgICBmYWN0b3JEZXRhaWxMaXN0OiBbXSwgLy8g6K+E5YiG5Zug5a2Q6K+m57uG5YiX6KGoDQogICAgICBlbnREb2NSZXNwb25zZVBhZ2U6IG51bGwsIC8vIOS8geS4muWTjeW6lOaWh+S7tumhteeggeS/oeaBrw0KICAgICAgZmFjdG9yc1BhZ2VNYXA6IG51bGwsIC8vIOS+m+W6lOWVhuWboOWtkOmhteeggeaYoOWwhA0KICAgICAgc3VwcGxpZXJGYWN0b3JQYWdlOiBudWxsLCAvLyDlvZPliY3kvpvlupTllYblm6DlrZDpobXnoIENCiAgICAgIHJlc3BvbnNlUGRmVXJsOiBudWxsLCAvLyDlk43lupTmlofku7ZQREblnLDlnYANCiAgICAgIHByb2N1cmVtZW50UGRmVXJsOiBudWxsLCAvLyDph4fotK3mlofku7ZQREblnLDlnYANCg0KICAgICAgLy8g5oyJ6ZKu54q25oCB566h55CGDQogICAgICBhY3RpdmVCdXR0b246ICdyZXNwb25zZScsIC8vIOW9k+WJjea/gOa0u+eahOaMiemSru+8midyZXNwb25zZSfjgIEncHJvY3VyZW1lbnQn44CBJ2NvbnRyYXN0Jw0KDQogICBlbnREb2NQcm9jdXJlbWVudFBhZ2U6IHt9LCAvLyDph4fotK3mlofku7bpobXnoIHkv6Hmga8NCiAgICBwYWdlUHJvY3VyZW1lbnQ6W10sIC8vIOmHh+i0reaWh+S7tueahOivhOWIhumhuQ0KICAgIGF0dGFjaG1lbnRzTGlzdDpbXSwgLy8g5paH5Lu25YiX6KGoDQogICANCiAgIC8vIFBERua4suafk+eKtuaAgeeuoeeQhg0KICAgcmVzcG9uc2VQZGZSZW5kZXJlZDogZmFsc2UsIC8vIOWTjeW6lOaWh+S7tlBERuaYr+WQpua4suafk+WujOaIkA0KICAgcHJvY3VyZW1lbnRQZGZSZW5kZXJlZDogZmFsc2UsIC8vIOmHh+i0reaWh+S7tlBERuaYr+WQpua4suafk+WujOaIkA0KDQogICBoZWxwSW1nTGlzdDogWyIvZXZhbHV0aW9uL2hlbHAuanBnIl0sIC8vIOaTjeS9nOW4ruWKqeWbvueJh+WIl+ihqA0KDQogICAgLy8gUERG5riy5p+T54q25oCB5o6n5Yi2DQogICAgaXNQZGZSZW5kZXJpbmc6IGZhbHNlLCAvLyBQREbmmK/lkKbmraPlnKjmuLLmn5MNCiAgICAgIC8vIOivhOWIhumhueWQjeensOS4juWQjuerr+Wtl+auteaYoOWwhA0KICAgICAgZmFjdG9yQ29kZU1hcDogew0KICAgICAgICAi54m55a6a6LWE5qC86KaB5rGCIjogInpnenMiLA0KICAgICAgICAi5ZON5bqU5YaF5a65IjogImpzcGxiIiwNCiAgICAgICAgIumHh+i0remcgOaxgiI6ICJqc3BsYiIsDQogICAgICAgICLkvpvotKfmnJ/pmZAiOiAiZ2hxeCIsDQogICAgICAgICLmipXmoIfmiqXku7ciOiAidGJiaiINCiAgICAgIH0sDQogICAgICBjaGVja1Jlc3VsdDoge30sIC8vIOezu+e7n+WInemqjOe7k+aenOWvueixoQ0KICAgICAgLy8g57O757uf5Yid6aqM57uT5p6c5ZCN56ew5pig5bCEDQogICAgICBjaGVja1Jlc3VsdE5hbWVNYXA6IHsNCiAgICAgICAgIuespuWQiOOAiuS4reWNjuS6uuawkeWFseWSjOWbveaUv+W6nOmHh+i0reazleOAi+esrOS6jOWNgeS6jOadoeinhOWumiI6IENIRUNLX1BBU1MsDQogICAgICAgICLnibnlrprotYTmoLzopoHmsYIiOiBDSEVDS19QQVNTLA0KICAgICAgICAi5L+h55So5p+l6K+iIjogQ0hFQ0tfUEFTUywNCiAgICAgICAgIuWTjeW6lOS6uuWQjeensCI6IENIRUNLX1BBU1MsDQogICAgICAgICLlk43lupTlhoXlrrkiOiBDSEVDS19QQVNTLA0KICAgICAgICAi6YeH6LSt6ZyA5rGCIjogQ0hFQ0tfUEFTUywNCiAgICAgICAgIuS+m+i0p+acn+mZkCI6IENIRUNLX1BBU1MsDQogICAgICAgICLmipXmoIfmiqXku7ciOiBDSEVDS19QQVNTDQogICAgICB9LA0KICAgICAgLy8g5pys5Zyw57yT5a2Y5pWw5o2uDQogICAgICBsb2NhbEV4cGVydEluZm86IG51bGwsIC8vIOacrOWcsOS4k+WutuS/oeaBrw0KICAgICAgbG9jYWxFbnREb2NSZXNwb25zZVBhZ2U6IG51bGwsIC8vIOacrOWcsOWTjeW6lOaWh+S7tumhteeggQ0KICAgICAgbG9jYWxGYWN0b3JzUGFnZU1hcDogbnVsbCwgLy8g5pys5Zyw5Zug5a2Q6aG156CB5pig5bCEDQoJICAgIA0KCSAgICAvLyDmgqzlgZznirbmgIHnrqHnkIYNCgkgICAgaG92ZXJlZEZhY3Rvck5vZGU6IG51bGwsIC8vIOaCrOWBnOaXtueahOivhOWIhumhuQ0KCSAgICB0b29sdGlwVGltZXI6IG51bGwsIC8vIOaCrOa1ruahhuaYvuekuuWumuaXtuWZqA0KICAgIH07DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKioNCiAgICAgKiDmoKHpqozmiYDmnInor4TliIbpobnmmK/lkKbloavlhpnlrozmlbQNCiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5piv5ZCm5YWo6YOo5aGr5YaZDQogICAgICovDQogICAgdmFsaWRhdGVSYXRpbmdzKCkgew0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMuc2NvcmluZ01ldGhvZC51aXRlbXMpIHsgLy8g6YGN5Y6G5omA5pyJ6K+E5YiG6aG5DQogICAgICAgIGNvbnN0IHN0YXRlID0gdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtLmVudE1ldGhvZEl0ZW1JZF0uc3RhdGU7IC8vIOiOt+WPluivhOWIhueKtuaAgQ0KICAgICAgICBjb25zdCByZWFzb24gPSB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5yZWFzb247IC8vIOiOt+WPluivhOWIhuWOn+WboA0KICAgICAgICAvLyDor4TliIbnu5PmnpzmnKrloavlhpkNCiAgICAgICAgaWYgKHN0YXRlID09PSBudWxsIHx8IHN0YXRlID09PSAnJykgew0KICAgICAgICAgIC8vIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhg6K+35aGr5YaZ6K+E5YiG6aG577yaJHtpdGVtLml0ZW1OYW1lfSDnmoTor4TliIbnu5PmnpxgKTsgLy8g5o+Q56S65pyq5aGr5YaZDQogICAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICAgIH0NCiAgICAgICAgLy8g5LiN6YCa6L+H5L2G5pyq5aGr5YaZ5Y6f5ZugIC0g5bCG6K+E5a6h6aG56K6+572u5Li656m677yM54S25ZCO57un57ut5omn6KGM5ZCO57ut5rWB56iLDQogICAgICAgIGlmIChzdGF0ZSA9PT0gRkFJTCAmJiAoIXJlYXNvbiB8fCByZWFzb24udHJpbSgpID09PSAnJykpIHsNCiAgICAgICAgICAvLyDlsIbmraTor4TlrqHpobnorr7nva7kuLrnqbrvvIjmnKror4TlrqHnirbmgIHvvIkNCiAgICAgICAgICB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5zdGF0ZSA9IG51bGw7DQogICAgICAgICAgdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtLmVudE1ldGhvZEl0ZW1JZF0ucmVhc29uID0gIiI7DQogICAgICAgICAgY29uc29sZS5sb2coYCR7aXRlbS5pdGVtTmFtZX3or4TlrqHkuI3pgJrov4fkvYbmnKrloavlhpnlpIfms6jvvIzlt7LlsIbor6Xor4TlrqHpobnorr7nva7kuLrnqbpgKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHRydWU7IC8vIOWFqOmDqOWhq+WGmei/lOWbnnRydWUNCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOiOt+WPluezu+e7n+WInemqjOe7k+aenO+8iOmAmui/hy/mnKrpgJrov4fvvIkNCiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZmFjdG9yTmFtZSDor4TliIbpobnlkI3np7ANCiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfSAxLemAmui/hyAwLeacqumAmui/hw0KICAgICAqLw0KICAgIGdldENoZWNrUmVzdWx0U3RhdGUoZmFjdG9yTmFtZSkgew0KICAgICAgaWYgKCF0aGlzLmNoZWNrUmVzdWx0IHx8IE9iamVjdC5rZXlzKHRoaXMuY2hlY2tSZXN1bHQpLmxlbmd0aCA9PT0gMCkgcmV0dXJuICcnOyAvLyDmsqHmnInliJ3pqoznu5Pmnpznm7TmjqXov5Tlm57nqboNCiAgICAgIGxldCBjb2RlID0gdGhpcy5mYWN0b3JDb2RlTWFwW2ZhY3Rvck5hbWVdOyAvLyDojrflj5bor4TliIbpobnlr7nlupTnmoTlkI7nq6/lrZfmrrUNCiAgICAgIGxldCBjaGVjayA9IFBBU1M7IC8vIOm7mOiupOmAmui/hw0KICAgICAgaWYgKGNvZGUpIHsNCiAgICAgICAgY2hlY2sgPSB0aGlzLmNoZWNrUmVzdWx0W2NvZGVdOyAvLyDojrflj5bliJ3pqoznu5PmnpwNCiAgICAgICAgLy8g5oqV5qCH5oql5Lu354m55q6K5aSE55CGDQogICAgICAgIGlmIChmYWN0b3JOYW1lID09PSAi5oqV5qCH5oql5Lu3IiAmJiBjaGVjayA9PT0gUEFTUykgew0KICAgICAgICAgIGNoZWNrID0gdGhpcy5jaGVja1Jlc3VsdFsnbXhiamInXTsgLy8g5piO57uG5oql5Lu36KGoDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC8vIOiuvue9ruWInemqjOe7k+aenOWQjeensA0KICAgICAgaWYgKGNoZWNrID09PSBGQUlMKSB7DQogICAgICAgIHRoaXMuY2hlY2tSZXN1bHROYW1lTWFwW2ZhY3Rvck5hbWVdID0gQ0hFQ0tfRkFJTDsgLy8g5pyq6YCa6L+HDQogICAgICB9IGVsc2Ugew0KICAgICAgICBjaGVjayA9IFBBU1M7DQogICAgICAgIHRoaXMuY2hlY2tSZXN1bHROYW1lTWFwW2ZhY3Rvck5hbWVdID0gQ0hFQ0tfUEFTUzsgLy8g6YCa6L+HDQogICAgICB9DQogICAgICByZXR1cm4gY2hlY2s7IC8vIOi/lOWbnuWInemqjOe7k+aenA0KICAgIH0sDQogICAgLyoqDQogICAgICog6YeN572u5omA5pyJ6K+E5YiG6aG555qE54q25oCBDQogICAgICovDQogICAgcmVzZXRSYXRpbmdTdGF0ZU1hcCgpIHsNCiAgICAgIGlmICghdGhpcy5zY29yaW5nTWV0aG9kKSByZXR1cm47IC8vIOayoeacieivhOWIhuaWueazleebtOaOpei/lOWbng0KICAgICAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXModGhpcy5yYXRpbmdTdGF0ZU1hcCkpIHsgLy8g6YGN5Y6G5omA5pyJ6K+E5YiG6aG5DQogICAgICAgIHRoaXMucmF0aW5nU3RhdGVNYXBba2V5XS5zdGF0ZSA9IG51bGw7IC8vIOmHjee9rueKtuaAgQ0KICAgICAgICB0aGlzLnJhdGluZ1N0YXRlTWFwW2tleV0ucmVhc29uID0gJyc7IC8vIOmHjee9ruWOn+WboA0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqDQogICAgICog5Li05pe25L+d5a2Y6K+E5YiG57uT5p6c5Yiw5ZCO56uvDQogICAgICog5qCh6aqM6YCa6L+H5ZCO5omN5Lya5L+d5a2YDQogICAgICogQHJldHVybnMge2Jvb2xlYW59IOS/neWtmOaYr+WQpuaIkOWKnw0KICAgICAqLw0KICAgIGFzeW5jIHNhdmVUZW1wUmF0aW5nKCkgew0KICAgICAgaWYgKCF0aGlzLnZhbGlkYXRlUmF0aW5ncygpKSByZXR1cm4gZmFsc2U7IC8vIOagoemqjOS4jemAmui/h+S4jeS/neWtmO+8jOi/lOWbnmZhbHNlDQogICAgICAvLyDmnoTpgKDmj5DkuqTmlbDmja4NCiAgICAgIGNvbnN0IGRhdGEgPSB0aGlzLnNjb3JpbmdNZXRob2QudWl0ZW1zLm1hcChpdGVtID0+IHsNCiAgICAgICAgY29uc3QgaXRlbUlkID0gaXRlbS5lbnRNZXRob2RJdGVtSWQ7IC8vIOiOt+WPluivhOWIhumhuUlEDQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgc2NvcmluZ01ldGhvZFVpdGVtSWQ6IGl0ZW1JZCwgLy8g6K+E5YiG6aG5SUQNCiAgICAgICAgICBleHBlcnRSZXN1bHRJZDogdGhpcy5leHBlcnRJbmZvLnJlc3VsdElkLCAvLyDkuJPlrrbnu5PmnpxJRA0KICAgICAgICAgIGVudElkOiB0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWQsIC8vIOS+m+W6lOWVhklEDQogICAgICAgICAgZXZhbHVhdGlvblJlc3VsdDogdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtSWRdLnN0YXRlLCAvLyDor4TliIbnu5PmnpwNCiAgICAgICAgICBldmFsdWF0aW9uUmVtYXJrOiB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW1JZF0ucmVhc29uIHx8ICcnIC8vIOivhOWIhuWOn+WboA0KICAgICAgICB9Ow0KICAgICAgfSkuZmlsdGVyKGQgPT4gZC5ldmFsdWF0aW9uUmVzdWx0ICE9PSBudWxsICYmIGQuZXZhbHVhdGlvblJlc3VsdCAhPT0gJycpOyAvLyDov4fmu6TmnKrloavlhpnnmoTpobkNCiAgICAgIGlmIChkYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNjb3JpbmdGYWN0b3JzKGRhdGEpOyAvLyDmj5DkuqTor4TliIblm6DlrZANCiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/neWtmOaIkOWKnyIpOyAvLyDkv53lrZjmiJDlip/mj5DnpLoNCiAgICAgICAgICAgIHJldHVybiB0cnVlOyAvLyDkv53lrZjmiJDlip/ov5Tlm550cnVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXNwb25zZS5tc2cpOyAvLyDkv53lrZjlpLHotKXmj5DnpLoNCiAgICAgICAgICAgIHJldHVybiBmYWxzZTsgLy8g5L+d5a2Y5aSx6LSl6L+U5ZueZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkv53lrZjlpLHotKUiKTsgLy8g5byC5bi45o+Q56S6DQogICAgICAgICAgcmV0dXJuIGZhbHNlOyAvLyDlvILluLjov5Tlm55mYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gdHJ1ZTsgLy8g5rKh5pyJ5pWw5o2u6ZyA6KaB5L+d5a2Y5pe25Lmf6L+U5ZuedHJ1ZQ0KICAgIH0sDQogICAgLyoqDQogICAgICog5L6b5bqU5ZWG5YiH5o2i5LqL5Lu277yM5YiH5o2i5pe26Ieq5Yqo5L+d5a2Y5LiK5LiA5Liq5L6b5bqU5ZWG6K+E5YiG77yM5bm25bm25Y+R6I635Y+W5paw5L6b5bqU5ZWG55qE6K+E5YiG6K+m5oOF5ZKM57O757uf5Yid6aqMDQogICAgICogQHBhcmFtIHtzdHJpbmd9IHN1cHBsaWVyTmFtZSDkvpvlupTllYblkI3np7ANCiAgICAgKi8NCiAgICBhc3luYyBoYW5kbGVTdXBwbGllckNoYW5nZShzdXBwbGllck5hbWUpIHsNCiAgICAgIC8vIOWIh+aNouWJjeS/neWtmOS4iuS4gOS4quS+m+W6lOWVhuivhOWIhg0KICAgICAgaWYgKE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0ZWRTdXBwbGllcikubGVuZ3RoICE9PSAwKSB7DQogICAgICAgIGF3YWl0IHRoaXMuc2F2ZVRlbXBSYXRpbmcoKTsgLy8g5L+d5a2Y6K+E5YiGDQogICAgICB9DQogICAgICAvLyDmn6Xmib7lvZPliY3pgInkuK3nmoTkvpvlupTllYblr7nosaENCiAgICAgIHRoaXMuc2VsZWN0ZWRTdXBwbGllciA9IHRoaXMuc3VwcGxpZXJPcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLmJpZGRlck5hbWUgPT09IHN1cHBsaWVyTmFtZSk7IC8vIOafpeaJvuS+m+W6lOWVhg0KICAgICAgLy8g6I635Y+W5b2T5YmN5L6b5bqU5ZWG5Zug5a2Q6aG156CBDQogICAgICB0aGlzLnN1cHBsaWVyRmFjdG9yUGFnZSA9IHRoaXMuZmFjdG9yc1BhZ2VNYXBbdGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkXTsgLy8g6I635Y+W6aG156CBDQogICAgICAvLyDlubblj5Hojrflj5bor4TliIbor6bmg4Xlkozns7vnu5/liJ3pqowNCiAgICAgIC8vIOS9v+eUqCBQcm9taXNlLmFsbFNldHRsZWQg6K6p5Lik5Liq6K+35rGC54us56uL5omn6KGM77yM5LqS5LiN5b2x5ZONDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBbZGV0YWlsUmVzdWx0LCBjaGVja1Jlc3VsdF0gPSBhd2FpdCBQcm9taXNlLmFsbFNldHRsZWQoWw0KICAgICAgICAgIGdldERldGFpbEJ5UHN4eCh7DQogICAgICAgICAgICBleHBlcnRSZXN1bHRJZDogdGhpcy5leHBlcnRJbmZvLnJlc3VsdElkLCAvLyDkuJPlrrbnu5PmnpxJRA0KICAgICAgICAgICAgcHJvamVjdElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsIC8vIOmhueebrklEDQogICAgICAgICAgICBzY29yaW5nTWV0aG9kSXRlbUlkOiB0aGlzLiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkLCAvLyDor4TliIbmlrnms5XpoblJRA0KICAgICAgICAgIH0pLA0KICAgICAgICAgIHJlc0RvY1Jldmlld0ZhY3RvcnNEZWNpc2lvbih7DQogICAgICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwgLy8g6aG555uuSUQNCiAgICAgICAgICAgIGVudElkOiB0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWQsIC8vIOS+m+W6lOWVhklEDQogICAgICAgICAgfSkNCiAgICAgICAgXSk7DQoNCiAgICAgICAgLy8g5aSE55CG6K+E5YiG6K+m5oOF6K+35rGC57uT5p6cDQogICAgICAgIGlmIChkZXRhaWxSZXN1bHQuc3RhdHVzID09PSAnZnVsZmlsbGVkJykgew0KICAgICAgICAgIGNvbnN0IGRldGFpbFJlcyA9IGRldGFpbFJlc3VsdC52YWx1ZTsNCiAgICAgICAgICBpZiAoZGV0YWlsUmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgdGhpcy5mYWN0b3JEZXRhaWxMaXN0ID0gZGV0YWlsUmVzLmRhdGE7IC8vIOivhOWIhuivpuaDheWIl+ihqA0KICAgICAgICAgICAgY29uc3QgZmFjdG9yID0gdGhpcy5mYWN0b3JEZXRhaWxMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLmJpZGRlck5hbWUgPT09IHN1cHBsaWVyTmFtZSk/LmV2YWxFeHBlcnRFdmFsdWF0aW9uRGV0YWlsczsgLy8g5b2T5YmN5L6b5bqU5ZWG6K+E5YiG6K+m5oOFDQogICAgICAgICAgICB0aGlzLnJlc2V0UmF0aW5nU3RhdGVNYXAoKTsgLy8g6YeN572u6K+E5YiG54q25oCBDQogICAgICAgICAgICBpZiAoZmFjdG9yKSB7DQogICAgICAgICAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBmYWN0b3IpIHsNCiAgICAgICAgICAgICAgICB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uc2NvcmluZ01ldGhvZFVpdGVtSWRdLnJlYXNvbiA9IGl0ZW0uZXZhbHVhdGlvblJlbWFyazsgLy8g6K6+572u6K+E5YiG5Y6f5ZugDQogICAgICAgICAgICAgICAgdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtLnNjb3JpbmdNZXRob2RVaXRlbUlkXS5zdGF0ZSA9IGl0ZW0uZXZhbHVhdGlvblJlc3VsdDsgLy8g6K6+572u6K+E5YiG57uT5p6cDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGRldGFpbFJlcy5tc2cpOyAvLyDor4TliIbor6bmg4Xojrflj5blpLHotKUNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W6K+E5YiG6K+m5oOF5aSx6LSlOiIsIGRldGFpbFJlc3VsdC5yZWFzb24pOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluivhOWIhuivpuaDheWksei0pSIpOyAvLyDor4TliIbor6bmg4Xor7fmsYLlvILluLgNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWkhOeQhuezu+e7n+WInemqjOivt+axgue7k+aenA0KICAgICAgICBpZiAoY2hlY2tSZXN1bHQuc3RhdHVzID09PSAnZnVsZmlsbGVkJykgew0KICAgICAgICAgIGNvbnN0IGNoZWNrUmVzID0gY2hlY2tSZXN1bHQudmFsdWU7DQogICAgICAgICAgaWYgKGNoZWNrUmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgdGhpcy5jaGVja1Jlc3VsdCA9IGNoZWNrUmVzLmRhdGE7IC8vIOiuvue9ruWInemqjOe7k+aenA0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bns7vnu5/liJ3pqoznu5PmnpzlpLHotKU6IiwgY2hlY2tSZXMubXNnKTsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6I635Y+W57O757uf5Yid6aqM57uT5p6c5aSx6LSlIik7IC8vIOezu+e7n+WInemqjOiOt+WPluWksei0pQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLns7vnu5/liJ3pqozor7fmsYLlpLHotKU6IiwgY2hlY2tSZXN1bHQucmVhc29uKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLns7vnu5/liJ3pqozor7fmsYLlpLHotKUiKTsgLy8g57O757uf5Yid6aqM6K+35rGC5byC5bi4DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6K+35rGC5aSE55CG5byC5bi4OiIsIGUpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5bkvpvlupTllYbor6bmg4XlpLHotKUiKTsgLy8g5byC5bi45o+Q56S6DQogICAgICB9DQogICAgICAvLyDpu5jorqTmmL7npLrlk43lupTmlofku7YNCiAgICAgIHRoaXMuc2hvd1Jlc3BvbnNlRmlsZSgpOw0KICAgIH0sDQogICAgLyoqDQogICAgICog5pi+56S65ZON5bqU5paH5Lu2UERGDQogICAgICovDQogICAgc2hvd1Jlc3BvbnNlRmlsZSgpIHsNCiAgICAgIGlmICghdGhpcy5zZWxlY3RlZFN1cHBsaWVyIHx8IE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0ZWRTdXBwbGllcikubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5L6b5bqU5ZWGIik7IC8vIOacqumAieS+m+W6lOWVhuaPkOekug0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdyZXNwb25zZSc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KICAgICAgdGhpcy5pc0RvdWJsZVZpZXcgPSBmYWxzZTsgLy8g5Y2V5paH5Lu25qih5byPDQogICAgICB0aGlzLmlzU2hvd1Byb2N1cmVtZW50ID0gZmFsc2U7IC8vIOS4jeaYvuekuumHh+i0reaWh+S7tg0KICAgICAgdGhpcy5pc1Nob3dSZXNwb25zZSA9IHRydWU7IC8vIOaYvuekuuWTjeW6lOaWh+S7tg0KICAgICAgdGhpcy5yZXNwb25zZVBkZlVybCA9IHRoaXMucHJvamVjdEZpbGVzLmZpbGVbdGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkXTsgLy8g6K6+572u5ZON5bqU5paH5Lu2UERG5Zyw5Z2ADQogICAgICB0aGlzLmlzUGRmUmVuZGVyaW5nID0gdHJ1ZTsgLy8g5byA5aeL5riy5p+TUERGDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDmlofku7blr7nmr5TvvIjlj4zmlofku7bmqKHlvI/vvIkNCiAgICAgKi8NCiAgICBzaG93RmlsZUNvbnRyYXN0KCkgew0KICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkU3VwcGxpZXIgfHwgT2JqZWN0LmtleXModGhpcy5zZWxlY3RlZFN1cHBsaWVyKS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nkvpvlupTllYYiKTsgLy8g5pyq6YCJ5L6b5bqU5ZWG5o+Q56S6DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuYWN0aXZlQnV0dG9uID0gJ2NvbnRyYXN0JzsgLy8g6K6+572u5b2T5YmN5r+A5rS75oyJ6ZKuDQogICAgICB0aGlzLmlzRG91YmxlVmlldyA9IHRydWU7IC8vIOWPjOaWh+S7tuaooeW8jw0KICAgICAgdGhpcy5pc1Nob3dQcm9jdXJlbWVudCA9IHRydWU7IC8vIOaYvuekuumHh+i0reaWh+S7tg0KICAgICAgdGhpcy5pc1Nob3dSZXNwb25zZSA9IHRydWU7IC8vIOaYvuekuuWTjeW6lOaWh+S7tg0KICAgICAgdGhpcy5yZXNwb25zZVBkZlVybCA9IHRoaXMucHJvamVjdEZpbGVzLmZpbGVbdGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkXTsgLy8g6K6+572u5ZON5bqU5paH5Lu2UERG5Zyw5Z2ADQogICAgICB0aGlzLmlzUGRmUmVuZGVyaW5nID0gdHJ1ZTsgLy8g5byA5aeL5riy5p+TUERGDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDngrnlh7vor4TliIbpobnlkI3np7DvvIzot7PovazliLDlr7nlupRQREbpobXnoIENCiAgICAgKiBAcGFyYW0ge09iamVjdH0gZmFjdG9ySXRlbSDlvZPliY3or4TliIblm6DlrZDpobkNCiAgICAgKi8NCiAgICBoYW5kbGVTaG93RmFjdG9ySW5mbyhmYWN0b3JJdGVtKSB7DQoJICAgIC8vIOajgOafpVBERuaYr+WQpua4suafk+WujOaIkA0KCSAgICBpZiAoIXRoaXMuY2FuSnVtcFRvUGFnZSgpKSB7DQoJCSAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIlBERumhtemdouato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeivlSIpOw0KCQkgICAgcmV0dXJuOw0KCSAgICB9DQoJCQkNCiAgICAgIHRoaXMuc2VsZWN0ZWRGYWN0b3JOb2RlID0gZmFjdG9ySXRlbTsgLy8g6K6+572u5b2T5YmN6YCJ5Lit5Zug5a2QDQoNCiAgICAgIC8vIOWmguaenOWPquaYvuekuumHh+i0reaWh+S7tu+8jOS9v+eUqOmHh+i0reaWh+S7tumhteeggeS/oeaBrw0KICAgICAgaWYgKHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgJiYgIXRoaXMuaXNTaG93UmVzcG9uc2UpIHsNCgkgICAgICBpZiAoIXRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZCkgew0KCQkgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIumHh+i0reaWh+S7tuato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeivlSIpOw0KCQkgICAgICByZXR1cm47DQoJICAgICAgfQ0KCQkJCQ0KICAgICAgICBpZiAoZmFjdG9ySXRlbS5qdW1wVG9QYWdlKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5wcm9jdXJlbWVudC5za2lwUGFnZShmYWN0b3JJdGVtLmp1bXBUb1BhZ2UpOyAvLyDph4fotK3mlofku7bot7PpobUNCiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZSAmJiB0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtmYWN0b3JJdGVtLml0ZW1OYW1lXSkgew0KICAgICAgICAgIHRoaXMuJHJlZnMucHJvY3VyZW1lbnQuc2tpcFBhZ2UodGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbZmFjdG9ySXRlbS5pdGVtTmFtZV0pOyAvLyDph4fotK3mlofku7bot7PpobUNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOaYvuekuuWTjeW6lOaWh+S7tuaIluWvueavlOaooeW8j++8jOmcgOimgemAieaLqeS+m+W6lOWVhg0KICAgICAgaWYgKCF0aGlzLnN1cHBsaWVyRmFjdG9yUGFnZSB8fCBPYmplY3Qua2V5cyh0aGlzLnN1cHBsaWVyRmFjdG9yUGFnZSkubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI6YCJ5oup5L6b5bqU5ZWGIik7IC8vIOacqumAieS+m+W6lOWVhuaPkOekug0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOi3s+i9rOWIsOWTjeW6lOaWh+S7tuWvueW6lOmhteeggQ0KICAgICAgaWYgKHRoaXMuaXNTaG93UmVzcG9uc2UgJiYgdGhpcy4kcmVmcy5yZXNwb25zZSkgew0KCSAgICAgIGlmICghdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkKSB7DQoJCSAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5ZON5bqU5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7DQoJCSAgICAgIHJldHVybjsNCgkgICAgICB9DQogICAgICAgIHRoaXMuJHJlZnMucmVzcG9uc2Uuc2tpcFBhZ2UodGhpcy5zdXBwbGllckZhY3RvclBhZ2VbdGhpcy5zZWxlY3RlZEZhY3Rvck5vZGUuaXRlbU5hbWVdKTsgLy8g5ZON5bqU5paH5Lu26Lez6aG1DQogICAgICB9DQoNCiAgICAgIC8vIOi3s+i9rOWIsOmHh+i0reaWh+S7tuWvueW6lOmhteeggQ0KICAgICAgaWYgKHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgJiYgdGhpcy4kcmVmcy5wcm9jdXJlbWVudCkgew0KCSAgICAgIGlmICghdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkKSB7DQoJCSAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6YeH6LSt5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN6K+VIik7DQoJCSAgICAgIHJldHVybjsNCgkgICAgICB9DQoJCQkJDQogICAgICAgIC8vIOWcqOWvueavlOaooeW8j+S4i++8jOmHh+i0reaWh+S7tuW6lOivpei3s+i9rOWIsOmHh+i0reaWh+S7tueahOWvueW6lOmhteegge+8jOiAjOS4jeaYr+S+m+W6lOWVhueahOmhteeggQ0KICAgICAgICBpZiAodGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2UgJiYgdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbZmFjdG9ySXRlbS5pdGVtTmFtZV0pIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnByb2N1cmVtZW50LnNraXBQYWdlKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2ZhY3Rvckl0ZW0uaXRlbU5hbWVdKTsgLy8g6YeH6LSt5paH5Lu26Lez6aG1DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5Zyo5a+55q+U5qih5byP5LiL77yM5aaC5p6c5rKh5pyJ6YeH6LSt5paH5Lu26aG156CB5L+h5oGv77yM5YiZ5Y+q6Lez6L2s5ZON5bqU5paH5Lu255qE6aG156CB77yM5LiN6Lez6L2s6YeH6LSt5paH5Lu2DQogICAgICAgICAgLy8g6L+Z5qC35Y+v5Lul6YG/5YWN6YeH6LSt5paH5Lu25ZKM5ZON5bqU5paH5Lu25pi+56S65LiN5ZCM55qE5YaF5a656YCg5oiQ5re35reGDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KCSAgDQoJICAvKioNCgkgICAqIOajgOafpeaYr+WQpuWPr+S7pei3s+i9rOmhtemdog0KCSAgICogQHJldHVybnMge2Jvb2xlYW59IOaYr+WQpuWPr+S7pei3s+i9rA0KCSAgICovDQoJICBjYW5KdW1wVG9QYWdlKCkgew0KCQkgIC8vIOWmguaenOWPquaYvuekuumHh+i0reaWh+S7tg0KCQkgIGlmICh0aGlzLmlzU2hvd1Byb2N1cmVtZW50ICYmICF0aGlzLmlzU2hvd1Jlc3BvbnNlKSB7DQoJCQkgIHJldHVybiB0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQ7DQoJCSAgfQ0KCQkgIC8vIOWmguaenOWPquaYvuekuuWTjeW6lOaWh+S7tg0KCQkgIGlmICh0aGlzLmlzU2hvd1Jlc3BvbnNlICYmICF0aGlzLmlzU2hvd1Byb2N1cmVtZW50KSB7DQoJCQkgIHJldHVybiB0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQ7DQoJCSAgfQ0KCQkgIC8vIOWmguaenOWvueavlOaooeW8j++8iOS4pOS4qumDveaYvuekuu+8iQ0KCQkgIGlmICh0aGlzLmlzU2hvd1Jlc3BvbnNlICYmIHRoaXMuaXNTaG93UHJvY3VyZW1lbnQpIHsNCgkJCSAgcmV0dXJuIHRoaXMucmVzcG9uc2VQZGZSZW5kZXJlZCAmJiB0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQ7DQoJCSAgfQ0KCQkgIHJldHVybiBmYWxzZTsNCgkgIH0sDQoJICAvKioNCgkgICAqIOWkhOeQhlBERua4suafk+eKtuaAgeWPmOWMlg0KCSAgICogQHBhcmFtIHtib29sZWFufSBpc1JlbmRlcmVkIOaYr+WQpua4suafk+WujOaIkA0KCSAgICogQHBhcmFtIHtzdHJpbmd9IHBkZlR5cGUgUERG57G75Z6L77yaJ3Jlc3BvbnNlJyDmiJYgJ3Byb2N1cmVtZW50Jw0KCSAgICovDQoJICBoYW5kbGVQZGZSZW5kZXJTdGF0dXNDaGFuZ2UoaXNSZW5kZXJlZCwgcGRmVHlwZSkgew0KCSAgIGlmIChwZGZUeXBlID09PSAncmVzcG9uc2UnKSB7DQoJICAgIHRoaXMucmVzcG9uc2VQZGZSZW5kZXJlZCA9IGlzUmVuZGVyZWQ7DQoJICAgfSBlbHNlIGlmIChwZGZUeXBlID09PSAncHJvY3VyZW1lbnQnKSB7DQoJICAgIHRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZCA9IGlzUmVuZGVyZWQ7DQoJICAgfQ0KCSAgIA0KCSAgIGlmIChpc1JlbmRlcmVkKSB7DQoJICAgIGNvbnNvbGUubG9nKGAke3BkZlR5cGUgPT09ICdyZXNwb25zZScgPyAn5ZON5bqUJyA6ICfph4fotK0nfeaWh+S7tua4suafk+WujOaIkO+8jOWPr+S7pei/m+ihjOmhtemdoui3s+i9rGApOw0KCSAgIH0NCg0KCSAgICAgLy8g5qOA5p+l5omA5pyJ5pi+56S655qEUERG5piv5ZCm6YO95riy5p+T5a6M5oiQDQoJICAgICBsZXQgYWxsUmVuZGVyZWQgPSB0cnVlOw0KCSAgICAgaWYgKHRoaXMuaXNTaG93UmVzcG9uc2UgJiYgIXRoaXMucmVzcG9uc2VQZGZSZW5kZXJlZCkgew0KCSAgICAgICBhbGxSZW5kZXJlZCA9IGZhbHNlOw0KCSAgICAgfQ0KCSAgICAgaWYgKHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgJiYgIXRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZCkgew0KCSAgICAgICBhbGxSZW5kZXJlZCA9IGZhbHNlOw0KCSAgICAgfQ0KCSAgICAgDQoJICAgICBpZiAoYWxsUmVuZGVyZWQpIHsNCgkgICAgICAgdGhpcy5pc1BkZlJlbmRlcmluZyA9IGZhbHNlOyAvLyDmiYDmnIlQREbmuLLmn5PlrozmiJDvvIzop6PpmaTnpoHnlKjnirbmgIENCgkgICAgIH0NCgkgIH0sDQoJICANCiAgICAvKioNCiAgICAgKiDmj5DkuqTor4TliIblubbkv67mlLnkuJPlrrbov5vluqYNCiAgICAgKi8NCiAgICBhc3luYyBzdWJtaXQoKSB7DQogICAgICAvLyDlhYjkv53lrZjor4TliIbvvIzlpoLmnpzkv53lrZjlpLHotKXliJnkuI3nu6fnu63mj5DkuqQNCiAgICAgIGNvbnN0IHNhdmVSZXN1bHQgPSBhd2FpdCB0aGlzLnNhdmVUZW1wUmF0aW5nKCk7DQogICAgICBpZiAoIXNhdmVSZXN1bHQpIHsNCiAgICAgICAgLy8g5L+d5a2Y5aSx6LSl77yM5LiN57un57ut5o+Q5Lqk5rWB56iLDQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgcHJvamVjdElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsIC8vIOmhueebrklEDQogICAgICAgIGV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsIC8vIOS4k+Wutue7k+aenElEDQogICAgICAgIHNjb3JpbmdNZXRob2RJdGVtSWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnNjb3JpbmdNZXRob2RJdGVtSWQsIC8vIOivhOWIhuaWueazlemhuUlEDQogICAgICB9Ow0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjaGVja1Jldmlld1N1bW1hcnkoZGF0YSk7IC8vIOajgOafpeivhOWuoeaxh+aAuw0KDQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAvLyDkv67mlLnkuJPlrrbov5vluqYNCiAgICAgICAgICBjb25zdCBzdGF0dXMgPSB7DQogICAgICAgICAgICBldmFsRXhwZXJ0U2NvcmVJbmZvSWQ6IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImV2YWxFeHBlcnRTY29yZUluZm8iKSkuZXZhbEV4cGVydFNjb3JlSW5mb0lkLCAvLyDkuJPlrrbor4TliIbkv6Hmga9JRA0KICAgICAgICAgICAgZXZhbFN0YXRlOiAxLCAvLyDov5vluqbnirbmgIENCiAgICAgICAgICB9Ow0KICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGVkaXRFdmFsRXhwZXJ0U2NvcmVJbmZvKHN0YXR1cyk7IC8vIOe8lui+keS4k+WutuivhOWIhueKtuaAgQ0KICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaPkOS6pOaIkOWKnyIpOyAvLyDmj5DkuqTmiJDlip/mj5DnpLoNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy4kZW1pdCgic2VuZCIsICJ0d28iKTsgLy8g5Y+R6YCB5LqL5Lu2DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7IC8vIOaPkOS6pOWksei0peaPkOekug0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaPkOS6pOWksei0pSIpOyAvLyDlvILluLjmj5DnpLoNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOaYvuekuumHh+i0reaWh+S7tlBERg0KICAgICAqLw0KICAgIHZpZXdQdXJjaGFzaW5nKCkgew0KICAgICAgdGhpcy5hY3RpdmVCdXR0b24gPSAncHJvY3VyZW1lbnQnOyAvLyDorr7nva7lvZPliY3mv4DmtLvmjInpkq4NCiAgICAgIHRoaXMuaXNEb3VibGVWaWV3ID0gZmFsc2U7IC8vIOWNleaWh+S7tuaooeW8jw0KICAgICAgdGhpcy5pc1Nob3dSZXNwb25zZSA9IGZhbHNlOyAvLyDkuI3mmL7npLrlk43lupTmlofku7YNCiAgICAgIHRoaXMuaXNTaG93UHJvY3VyZW1lbnQgPSB0cnVlOyAvLyDmmL7npLrph4fotK3mlofku7YNCiAgICAgIHRoaXMuaXNQZGZSZW5kZXJpbmcgPSB0cnVlOyAvLyDlvIDlp4vmuLLmn5NQREYNCiAgICAgIA0KICAgICAgLy8g6K6+572u6YeH6LSt5paH5Lu2UERG5Zyw5Z2ADQogICAgICBpZiAodGhpcy5wcm9qZWN0RmlsZXMudGVuZGVyTm90aWNlRmlsZVBhdGgpIHsNCiAgICAgICAgdGhpcy5wcm9jdXJlbWVudFBkZlVybCA9IHRoaXMucHJvamVjdEZpbGVzLnRlbmRlck5vdGljZUZpbGVQYXRoOw0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDlj7Pkvqfor4TliIbpobnmmL7npLrkuLrph4fotK3mlofku7bnmoTor4TliIbpobkNCiAgICAgIGxldCBwYWdlUHJvY3VyZW1lbnRBcnIgPSBbXTsgLy8g6YeH6LSt5paH5Lu26K+E5YiG6aG55pWw57uEDQoNCiAgICAgIGZvciAobGV0IGl0ZW0gaW4gdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2Upew0KICAgICAgICBwYWdlUHJvY3VyZW1lbnRBcnIucHVzaCh7DQogICAgICAgICAgaXRlbU5hbWU6IGl0ZW0sDQogICAgICAgICAganVtcFRvUGFnZTogdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbaXRlbV0NCiAgICAgICAgfSkNCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2codGhpcy5zY29yaW5nTWV0aG9kLnVpdGVtcyk7DQogICAgICBjb25zb2xlLmxvZyhwYWdlUHJvY3VyZW1lbnRBcnIpDQogICAgICB0aGlzLnBhZ2VQcm9jdXJlbWVudCA9IFtdOw0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnNjb3JpbmdNZXRob2QudWl0ZW1zLmxlbmd0aDtpKyspew0KICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IHBhZ2VQcm9jdXJlbWVudEFyci5sZW5ndGg7aisrKXsNCiAgICAgICAgICBpZiAodGhpcy5zY29yaW5nTWV0aG9kLnVpdGVtc1tpXS5pdGVtTmFtZSA9PSBwYWdlUHJvY3VyZW1lbnRBcnJbal0uaXRlbU5hbWUpew0KICAgICAgICAgICAgdGhpcy5wYWdlUHJvY3VyZW1lbnQucHVzaCh7Li4udGhpcy5zY29yaW5nTWV0aG9kLnVpdGVtc1tpXSwuLi5wYWdlUHJvY3VyZW1lbnRBcnJbal19KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKHRoaXMucGFnZVByb2N1cmVtZW50KQ0KICAgIH0sDQogICAgLyoqDQogICAgICog6Lez6L2s5Yiw5LqM5qyh5oql5Lu36aG16Z2iDQogICAgICovDQogICAgc2Vjb25kT2ZmZXIoKSB7DQogICAgICBjb25zdCBxdWVyeSA9IHsNCiAgICAgICAgcHJvamVjdElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsIC8vIOmhueebrklEDQogICAgICAgIHpqaG06IHRoaXMuJHJvdXRlLnF1ZXJ5LnpqaG0sIC8vIOS4k+WutuivgeS7tuWPt+eggQ0KICAgICAgICBzY29yaW5nTWV0aG9kSXRlbUlkOiBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJ0ZW5kZXJPZmZlclNjb3JpbmdNZXRob2RJdGVtcyIpKSwgLy8g5LqM5qyh5oql5Lu36K+E5YiG5pa55rOV6aG5SUQNCiAgICAgIH07DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICIvc2Vjb25kT2ZmZXIiLCBxdWVyeSB9KTsgLy8g6Lez6L2s6aG16Z2iDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDot7PovazliLDor6LmoIfpobXpnaINCiAgICAgKi8NCiAgICBiaWRJbnF1aXJ5KCkgew0KICAgICAgY29uc3QgcXVlcnkgPSB7DQogICAgICAgIHByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLCAvLyDpobnnm65JRA0KICAgICAgICB6amhtOiB0aGlzLiRyb3V0ZS5xdWVyeS56amhtLCAvLyDkuJPlrrbor4Hku7blj7fnoIENCiAgICAgICAgc2NvcmluZ01ldGhvZEl0ZW1JZDogSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgidGVuZGVyT2ZmZXJTY29yaW5nTWV0aG9kSXRlbXMiKSksIC8vIOivouagh+ivhOWIhuaWueazlemhuUlEDQogICAgICB9Ow0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL2JpZElucXVpcnkiLCBxdWVyeSB9KTsgLy8g6Lez6L2s6aG16Z2iDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDojrflj5blm6DntKDlr7nlupTpobXnoIHvvIjku47mnKzlnLDnvJPlrZjvvIkNCiAgICAgKi8NCiAgICBnZXRGYWN0b3JzUGFnZSgpIHsNCiAgICAgIHRoaXMuZmFjdG9yc1BhZ2VNYXAgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJlbnREb2NSZXNwb25zZVBhZ2UiKSk7IC8vIOiOt+WPluWboOWtkOmhteeggeaYoOWwhA0KICAgIH0sDQogICAgLyoqDQogICAgICog5Yid5aeL5YyW5LiT5a625ZKM5pys5Zyw5pWw5o2u77yM5Y+q5ZyobW91bnRlZOaXtuiwg+eUqOS4gOasoQ0KICAgICAqLw0KICAgIGluaXRMb2NhbERhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICB0aGlzLmxvY2FsRXhwZXJ0SW5mbyA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImV4cGVydEluZm8iKSB8fCAie30iKTsgLy8g6I635Y+W5pys5Zyw5LiT5a625L+h5oGvDQogICAgICAgIHRoaXMubG9jYWxFbnREb2NSZXNwb25zZVBhZ2UgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJlbnREb2NSZXNwb25zZVBhZ2UiKSB8fCAie30iKTsgLy8g6I635Y+W5pys5Zyw5ZON5bqU5paH5Lu26aG156CBDQogICAgICAgIHRoaXMubG9jYWxGYWN0b3JzUGFnZU1hcCA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImVudERvY1Jlc3BvbnNlUGFnZSIpIHx8ICJ7fSIpOyAvLyDojrflj5bmnKzlnLDlm6DlrZDpobXnoIHmmKDlsIQNCiAgICAgICAgdGhpcy5leHBlcnRJbmZvID0gdGhpcy5sb2NhbEV4cGVydEluZm87IC8vIOiuvue9ruS4k+WutuS/oeaBrw0KICAgICAgICB0aGlzLmVudERvY1Jlc3BvbnNlUGFnZSA9IHRoaXMubG9jYWxFbnREb2NSZXNwb25zZVBhZ2U7IC8vIOiuvue9ruWTjeW6lOaWh+S7tumhteeggQ0KICAgICAgICB0aGlzLmZhY3RvcnNQYWdlTWFwID0gdGhpcy5sb2NhbEZhY3RvcnNQYWdlTWFwOyAvLyDorr7nva7lm6DlrZDpobXnoIHmmKDlsIQNCiAgICAgICAgY29uc29sZS5sb2coIuacrOWcsOaVsOaNruW3suWIneWni+WMliIsIHsgZXhwZXJ0SW5mbzogdGhpcy5leHBlcnRJbmZvIH0pOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5Yid5aeL5YyW5pys5Zyw5pWw5o2u5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5Yid5aeL5YyW5LiT5a625L+h5oGv77yI55So5LqO5ZON5bqU5LiT5a625L+h5oGv5pu05paw77yJDQogICAgICovDQogICAgaW5pdEV4cGVydEluZm8oKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBleHBlcnRJbmZvU3RyID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oImV4cGVydEluZm8iKTsNCiAgICAgICAgaWYgKGV4cGVydEluZm9TdHIpIHsNCiAgICAgICAgICB0aGlzLmxvY2FsRXhwZXJ0SW5mbyA9IEpTT04ucGFyc2UoZXhwZXJ0SW5mb1N0cik7DQogICAgICAgICAgdGhpcy5leHBlcnRJbmZvID0gdGhpcy5sb2NhbEV4cGVydEluZm87DQogICAgICAgICAgY29uc29sZS5sb2coIuS4k+WutuS/oeaBr+W3suWIt+aWsCIsIHRoaXMuZXhwZXJ0SW5mbyk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIt+aWsOS4k+WutuS/oeaBr+Wksei0pToiLCBlcnJvcik7DQogICAgICB9DQogICAgfSwNCiAgICAvKioNCiAgICAgKiDpobXpnaLliJ3lp4vljJbvvIzliqDovb3kvpvlupTllYbjgIHor4TliIbmlrnms5XjgIHmlofku7bnrYnvvIjlubblj5Hor7fmsYLvvIkNCiAgICAgKi8NCiAgICBhc3luYyBpbml0UGFnZSgpIHsNCiAgICAgIHRoaXMuaW5pdExvY2FsRGF0YSgpOyAvLyDliJ3lp4vljJbmnKzlnLDmlbDmja4NCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOW5tuWPkeiOt+WPluS+m+W6lOWVhuOAgeivhOWIhuaWueazleOAgeaWh+S7tg0KICAgICAgICBjb25zdCBbc3VwcGxpZXJSZXMsIGFwcHJvdmFsUmVzLCBmaWxlc1Jlc10gPSBhd2FpdCBQcm9taXNlLmFsbChbDQogICAgICAgICAgc3VwcGxpZXJJbmZvKHsgcHJvamVjdElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQgfSksIC8vIOiOt+WPluS+m+W6lOWVhg0KICAgICAgICAgIGFwcHJvdmFsUHJvY2Vzcyh0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsIHRoaXMuZXhwZXJ0SW5mby5yZXN1bHRJZCksIC8vIOiOt+WPluivhOWIhuaWueazlQ0KICAgICAgICAgIGZpbGVzQnlJZCh0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQpIC8vIOiOt+WPlumhueebruaWh+S7tg0KICAgICAgICBdKTsNCiAgICAgICAgLy8g5aSE55CG5L6b5bqU5ZWGDQogICAgICAgIGlmIChzdXBwbGllclJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnN1cHBsaWVyT3B0aW9ucyA9IHN1cHBsaWVyUmVzLnJvd3MuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pc0FiYW5kb25lZEJpZCA9PSAwKTsgLy8g6L+H5ruk5pyq5byD5qCH5L6b5bqU5ZWGDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKHN1cHBsaWVyUmVzLm1zZyk7IC8vIOiOt+WPluWksei0peaPkOekug0KICAgICAgICB9DQogICAgICAgIC8vIOWkhOeQhuivhOWIhuaWueazlQ0KICAgICAgICBpZiAoYXBwcm92YWxSZXMuY29kZSA9PT0gMjAwKSB7DQoJICAgICAgICB0aGlzLmF0dGFjaG1lbnRzTGlzdCA9IGFwcHJvdmFsUmVzLmRhdGEuYnVzaVRlbmRlck5vdGljZS5hdHRhY2htZW50cy5maWx0ZXIoaXRlbSA9PiBpdGVtLmZpbGVUeXBlID09ICIwIik7DQogICAgICAgICAgdGhpcy5zY29yaW5nTWV0aG9kID0gYXBwcm92YWxSZXMuZGF0YS5zY29yaW5nTWV0aG9kVWluZm8uc2NvcmluZ01ldGhvZEl0ZW1zLmZpbmQoDQogICAgICAgICAgICBpdGVtID0+IGl0ZW0uc2NvcmluZ01ldGhvZEl0ZW1JZCA9PSB0aGlzLiRyb3V0ZS5xdWVyeS5zY29yaW5nTWV0aG9kSXRlbUlkDQogICAgICAgICAgKTsgLy8g6I635Y+W5b2T5YmN6K+E5YiG5pa55rOVDQogICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oImV2YWxQcm9qZWN0RXZhbHVhdGlvblByb2Nlc3MiLCBKU09OLnN0cmluZ2lmeSh0aGlzLnNjb3JpbmdNZXRob2QuZXZhbFByb2plY3RFdmFsdWF0aW9uUHJvY2VzcykpOyAvLyDnvJPlrZjor4TliIbmtYHnqIsNCiAgICAgICAgICB0aGlzLnJhdGluZ1N0YXRlTWFwID0gdGhpcy5zY29yaW5nTWV0aG9kLnVpdGVtcy5yZWR1Y2UoKGFjYywgaXRlbSkgPT4gew0KICAgICAgICAgICAgYWNjW2l0ZW0uZW50TWV0aG9kSXRlbUlkXSA9IHsgc3RhdGU6IG51bGwsIHJlYXNvbjogJycgfTsgLy8g5Yid5aeL5YyW6K+E5YiG54q25oCBDQogICAgICAgICAgICByZXR1cm4gYWNjOw0KICAgICAgICAgIH0sIHt9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoYXBwcm92YWxSZXMubXNnKTsgLy8g6I635Y+W5aSx6LSl5o+Q56S6DQogICAgICAgIH0NCiAgICAgICAgLy8g5aSE55CG5paH5Lu2DQogICAgICAgIGlmIChmaWxlc1Jlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnByb2plY3RGaWxlcyA9IGZpbGVzUmVzLmRhdGE7IC8vIOiuvue9rumhueebruaWh+S7tg0KICAgICAgICAgIC8vIOazqOmHiuaOieiHquWKqOiuvue9rumHh+i0reaWh+S7tlBERu+8jOaUueS4uueCueWHu+aXtuaJjeiuvue9rg0KICAgICAgICAgIC8vIGlmICh0aGlzLnByb2plY3RGaWxlcy50ZW5kZXJOb3RpY2VGaWxlUGF0aCkgew0KICAgICAgICAgIC8vICAgdGhpcy5wcm9jdXJlbWVudFBkZlVybCA9IHRoaXMucHJvamVjdEZpbGVzLnRlbmRlck5vdGljZUZpbGVQYXRoOyAvLyDorr7nva7ph4fotK3mlofku7ZQREYNCiAgICAgICAgICAvLyB9DQogICAgICAgICAgLy8gaWYgKHRoaXMucHJvamVjdEZpbGVzLmZpbGUpIHsNCiAgICAgICAgICAvLyAgIHRoaXMucmVzcG9uc2VQZGZVcmwgPSB0aGlzLnByb2plY3RGaWxlcy5maWxlWzBdOyAvLyDorr7nva7lk43lupTmlofku7ZQREYNCiAgICAgICAgICAvLyB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGZpbGVzUmVzLm1zZyk7IC8vIOiOt+WPluWksei0peaPkOekug0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIumhtemdouWIneWni+WMluWksei0pSIpOyAvLyDlvILluLjmj5DnpLoNCiAgICAgIH0NCiAgICB9LA0KCSAgDQoJICBkb3dubG9hZEZpbGUoaXRlbSl7DQoJCSAgdGhpcy4kZG93bmxvYWQuemlwKGl0ZW0uZmlsZVBhdGgsaXRlbS5maWxlTmFtZSk7DQoJICB9LA0KCSAgDQoJICANCgkgIC8vID09PT09PT09PT0g5oKs5YGc55u45YWzID09PT09PT09PT0NCgkgIC8qKg0KCSAgICog5pi+56S66K+E5YiG6aG55oKs5rWu5qGGDQoJICAgKiBAcGFyYW0ge09iamVjdH0gZmFjdG9ySXRlbSDor4TliIbpobnlr7nosaENCgkgICAqLw0KCSAgc2hvd0ZhY3RvclRvb2x0aXAoZmFjdG9ySXRlbSkgew0KCQkgIGlmICghZmFjdG9ySXRlbS5pdGVtUmVtYXJrKSByZXR1cm47IC8vIOWmguaenOayoeacieivhOWuoeWGheWuueWImeS4jeaYvuekug0KCQkgIA0KCQkgIC8vIOa4hemZpOS5i+WJjeeahOWumuaXtuWZqA0KCQkgIGlmICh0aGlzLnRvb2x0aXBUaW1lcikgew0KCQkJICBjbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkgIH0NCgkJICANCgkJICAvLyDlu7bov5/mmL7npLrmgqzmta7moYbvvIzpgb/lhY3lv6vpgJ/np7vliqjml7bpopHnuYHmmL7npLoNCgkJICB0aGlzLnRvb2x0aXBUaW1lciA9IHNldFRpbWVvdXQoKCkgPT4gew0KCQkJICB0aGlzLmhvdmVyZWRGYWN0b3JOb2RlID0gZmFjdG9ySXRlbTsNCgkJICB9LCAzMDApOyAvLyAzMDBtc+W7tui/nw0KCSAgfSwNCgkgIA0KCSAgLyoqDQoJICAgKiDpmpDol4/or4TliIbpobnmgqzmta7moYYNCgkgICAqLw0KCSAgaGlkZUZhY3RvclRvb2x0aXAoKSB7DQoJCSAgLy8g5riF6Zmk5a6a5pe25ZmoDQoJCSAgaWYgKHRoaXMudG9vbHRpcFRpbWVyKSB7DQoJCQkgIGNsZWFyVGltZW91dCh0aGlzLnRvb2x0aXBUaW1lcik7DQoJCQkgIHRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJICB9DQoJCSAgDQoJCSAgLy8g5bu26L+f6ZqQ6JeP77yM57uZ55So5oi35pe26Ze056e75Yqo5Yiw5oKs5rWu5qGG5LiKDQoJCSAgc2V0VGltZW91dCgoKSA9PiB7DQoJCQkgIHRoaXMuaG92ZXJlZEZhY3Rvck5vZGUgPSBudWxsOw0KCQkgIH0sIDEwMCk7DQoJICB9LA0KCSAgDQoJICAvKioNCgkgICAqIOa4hemZpOaCrOa1ruahhuWumuaXtuWZqO+8iOW9k+m8oOagh+enu+WKqOWIsOaCrOa1ruahhuS4iuaXtu+8iQ0KCSAgICovDQoJICBjbGVhclRvb2x0aXBUaW1lcigpIHsNCgkJICBpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCSAgY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsNCgkJCSAgdGhpcy50b29sdGlwVGltZXIgPSBudWxsOw0KCQkgIH0NCgkgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCgkgIHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZW50RG9jUHJvY3VyZW1lbnRQYWdlIikpOyAvLyDliJ3lp4vljJbph4fotK3mlofku7bpobXnoIHkv6Hmga8NCiAgICB0aGlzLmluaXRQYWdlKCk7IC8vIOmhtemdouaMgui9veaXtuWIneWni+WMluaVsOaNrg0KICAgIHRoaXMuZ2V0RmFjdG9yc1BhZ2UoKTsgLy8g6I635Y+W5Zug5a2Q6aG156CBDQogIH0sDQoJYmVmb3JlRGVzdHJveSgpIHsNCgkJLy8g5riF55CG5a6a5pe25ZmoDQoJCWlmICh0aGlzLnRvb2x0aXBUaW1lcikgew0KCQkJY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsNCgkJCXRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJfQ0KCX0sDQp9Ow0K"}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8OA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "one.vue", "sourceRoot": "src/views/expertReview/compliance", "sourcesContent": ["<template>\r\n  <!-- 页面主容器，flex布局，分为左中右三部分 -->\r\n  <div class=\"compliance-main\">\r\n    <!-- 左侧内容区，包含标题、操作按钮、PDF预览区 -->\r\n    <div class=\"compliance-left\">\r\n      <!-- 顶部标题和操作按钮区 -->\r\n      <div class=\"compliance-header\">\r\n        <!-- 标题及操作步骤图片 -->\r\n        <div class=\"compliance-title-group\">\r\n          <div class=\"compliance-title\">符合性评审</div> <!-- 页面主标题 -->\r\n          <div class=\"compliance-step-img-group\">\r\n            <div class=\"compliance-step-text\">该页面操作说明</div> <!-- 操作步骤说明文字 -->\r\n            <el-image class=\"compliance-step-img\" :src=\"helpImgList[0]\" :preview-src-list=\"helpImgList\">\r\n            </el-image> <!-- 操作步骤图片，可点击放大 -->\r\n          </div>\r\n        </div>\r\n\t      \r\n\t      <!-- 文件列表 -->\r\n\t      <div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t      <div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t      <el-card\r\n\t\t\t      v-for=\"(item, index) in attachmentsList\"\r\n\t\t\t      :key=\"index\"\r\n\t\t\t      class=\"fileItem\"\r\n\t\t\t      shadow=\"hover\"\r\n\t\t\t      @click.native=\"downloadFile(item)\"\r\n\t\t\t      style=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t      >\r\n\t\t\t      <div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t      <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t      <span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t      <i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t      </div>\r\n\t\t      </el-card>\r\n\t      </div>\r\n\t      \r\n        <!-- 右侧操作按钮区 -->\r\n        <div class=\"compliance-header-btns\">\r\n          <el-button class=\"item-button\" @click=\"bidInquiry\" :disabled=\"isPdfRendering\">询标</el-button> <!-- 跳转到询标页面 -->\r\n          <!-- <el-button class=\"item-button\" @click=\"secondOffer\">发起二次报价</el-button> -->\r\n          <div class=\"compliance-header-btns-bottom\">\r\n           <el-button\r\n            :class=\"['item-button', activeButton === 'procurement' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n            @click=\"viewPurchasing\"\r\n              :disabled=\"isPdfRendering\">采购文件</el-button> <!-- 显示采购文件PDF -->\r\n           \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'response' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showResponseFile\"\r\n              :disabled=\"isPdfRendering\">响应文件</el-button> <!-- 显示响应文件PDF -->\r\n            \r\n            <el-button\r\n              :class=\"['item-button', activeButton === 'contrast' ? 'compliance-blue-btn-active' : 'compliance-blue-btn']\"\r\n              @click=\"showFileContrast\"\r\n              :disabled=\"isPdfRendering\">对比</el-button> <!-- 响应文件与采购文件对比 -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- PDF文件预览区，支持单文件和双文件对比 -->\r\n      <div style=\"height:82%\">\r\n        <!-- PDF预览区域 - 保持原始尺寸 -->\r\n        <div class=\"compliance-pdf-group\">\r\n         <!-- 采购文件PDF预览 -->\r\n         <div v-if=\"isShowProcurement\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-left' : '']\">\r\n          <!--            <pdfView ref=\"procurement\" :pdfurl=\"procurementPdfUrl\" :uni_key=\"'procurement'\"></pdfView>-->\r\n          \r\n          <PdfViewImproved ref=\"procurement\"  :pdfurl=\"procurementPdfUrl\"  :page-height=\"800\"\r\n                           :buffer-size=\"2\" @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"/>\r\n         \r\n         </div>\r\n         \r\n          <!-- 响应文件PDF预览 -->\r\n          <div v-if=\"isShowResponse\" :class=\"['compliance-pdf', isDoubleView ? 'compliance-pdf-border-right' : '']\">\r\n<!--            <pdfView ref=\"response\" :pdfurl=\"responsePdfUrl\" :uni_key=\"'response'\"></pdfView>-->\r\n           \r\n           <PdfViewImproved ref=\"response\"  :pdfurl=\"responsePdfUrl\"  :page-height=\"800\" :buffer-size=\"2\"\r\n                            @render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"/>\r\n           \r\n          </div>\r\n           \r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 中间分割线 -->\r\n    <div class=\"compliance-divider\"></div>\r\n    <!-- 右侧评分区 -->\r\n    <div class=\"compliance-right\">\r\n      <!-- 供应商选择下拉框 -->\r\n      <div class=\"compliance-select-group\">\r\n        <el-select class=\"compliance-select\" v-model=\"selectedSupplierName\" placeholder=\"请选择供应商\" @change=\"handleSupplierChange\" :disabled=\"isPdfRendering\">\r\n          <el-option v-for=\"item in supplierOptions\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n          </el-option>\r\n        </el-select>\r\n      </div>\r\n      <!-- 评分因子列表及操作区 -->\r\n      <div class=\"compliance-factors-group\" v-if=\"isShowResponse\">\r\n\t      <!-- PDF渲染状态提示 -->\r\n\t      <div v-if=\"responsePdfUrl && !responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t      <i class=\"el-icon-loading\"></i>\r\n\t\t      <span>响应文件正在渲染中，请稍候...</span>\r\n\t      </div>\r\n\t      <div v-else-if=\"responsePdfUrl && responsePdfRendered\" class=\"render-status-tip success\">\r\n\t\t      <i class=\"el-icon-success\"></i>\r\n\t\t      <span>响应文件渲染完成，可以点击跳转</span>\r\n\t      </div>\r\n\t      \r\n        <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n        <template v-if=\"scoringMethod && scoringMethod.uitems\">\r\n          <div v-for=\"(item, index) in scoringMethod.uitems\" :key=\"index\"\r\n               class=\"factor-item\" style=\"margin-bottom:10px\"\r\n               @mouseenter=\"showFactorTooltip(item)\"\r\n               @mouseleave=\"hideFactorTooltip\" >\r\n\t          <!-- 悬浮框 -->\r\n\t          <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t               class=\"factor-tooltip\"\r\n\t               @mouseenter=\"clearTooltipTimer\"\r\n\t               @mouseleave=\"hideFactorTooltip\">\r\n\t\t          <div class=\"tooltip-header\">\r\n\t\t\t          <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t          <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t          </div>\r\n\t\t          <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t          </div>\r\n\t          \r\n            <div>\r\n              <div class=\"factors\">\r\n                <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n                <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n                  <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n                    {{ item.itemName }}\r\n\t                  <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n                  </div>\r\n                </div>\r\n                <!-- 评分单选按钮（通过/不通过） -->\r\n                <div class=\"compliance-factor-radio-group\">\r\n                  <div>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n                    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 不通过时填写原因 -->\r\n              <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n              </el-input>\r\n              <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n              <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n              <div class=\"compliance-factor-divider\"></div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <!-- 提交按钮区 -->\r\n        <div class=\"compliance-submit-group\">\r\n          <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n          <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n        </div>\r\n        <!-- 当前选中评分因子的详细说明 -->\r\n        <div class=\"compliance-review-content\">\r\n          <div class=\"compliance-review-title\">评审内容：</div>\r\n          <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n        </div>\r\n      </div>\r\n\r\n\t    <div class=\"compliance-factors-group\" v-else>\r\n\t\t    <!-- PDF渲染状态提示 -->\r\n\t\t    <div v-if=\"procurementPdfUrl && !procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t    <i class=\"el-icon-loading\"></i>\r\n\t\t\t    <span>采购文件正在渲染中，请稍候...</span>\r\n\t\t    </div>\r\n\t\t    <div v-else-if=\"procurementPdfUrl && procurementPdfRendered\" class=\"render-status-tip success\">\r\n\t\t\t    <i class=\"el-icon-success\"></i>\r\n\t\t\t    <span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t    </div>\r\n\t\t    \r\n\t\t    <!-- 判空后再渲染评分因子项列表，防止scoringMethod为null时报错 -->\r\n\t\t    <template v-if=\"pageProcurement\">\r\n\t\t\t    <div v-for=\"(item, index) in pageProcurement\" :key=\"index\" class=\"factor-item\" style=\"margin-bottom:10px\"\r\n\t\t\t         @mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t         @mouseleave=\"hideFactorTooltip\" >\r\n\t\t\t\t    <!-- 悬浮框 -->\r\n\t\t\t\t    <div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t         class=\"factor-tooltip\"\r\n\t\t\t\t         @mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t         @mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t    <div class=\"tooltip-header\">\r\n\t\t\t\t\t\t    <div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t    <i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t\t    <div>\r\n\t\t\t\t\t    <div class=\"factors\">\r\n\t\t\t\t\t\t    <!-- 评分因子名称，点击可跳转PDF对应页码 -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-title-group factor-title\" :class=\"{ 'disabled': !canJumpToPage() }\">\r\n\t\t\t\t\t\t\t    <div class=\"compliance-factor-title\" @click=\"handleShowFactorInfo(item)\">\r\n\t\t\t\t\t\t\t\t    {{ item.itemName }}\r\n\t\t\t\t\t\t\t\t    <i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    <!-- 评分单选按钮（通过/不通过） -->\r\n\t\t\t\t\t\t    <div class=\"compliance-factor-radio-group\">\r\n\t\t\t\t\t\t\t    <div>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t    <el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t\t    </div>\r\n\t\t\t\t\t    </div>\r\n\t\t\t\t\t    <!-- 不通过时填写原因 -->\r\n\t\t\t\t\t    <el-input v-if=\"ratingStateMap[item.entMethodItemId].state == 0\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t    </el-input>\r\n\t\t\t\t\t    <!-- 系统初验结果展示，绿色为通过，红色为未通过 -->\r\n\t\t\t\t\t    <span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n                <i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n                <i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n                {{checkResultNameMap[item.itemName]}}</span>\r\n\t\t\t\t\t    <div class=\"compliance-factor-divider\"></div>\r\n\t\t\t\t    </div>\r\n\t\t\t    </div>\r\n\t\t    </template>\r\n\t\t    <!-- 提交按钮区 -->\r\n\t\t    <div class=\"compliance-submit-group\">\r\n\t\t\t    <!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"show\">保存</el-button></div> -->\r\n\t\t\t    <div><el-button class=\"item-button-little compliance-submit-btn\" @click=\"submit\">提交</el-button></div>\r\n\t\t    </div>\r\n\t\t    <!-- 当前选中评分因子的详细说明 -->\r\n\t\t    <div class=\"compliance-review-content\">\r\n\t\t\t    <div class=\"compliance-review-title\">评审内容：</div>\r\n\t\t\t    <div class=\"compliance-review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t    </div>\r\n\t    </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 常量定义\r\nconst PASS = '1'; // 通过\r\nconst FAIL = '0'; // 不通过\r\nconst CHECK_PASS = '系统初验通过'; // 系统初验通过文本\r\nconst CHECK_FAIL = '系统初验未通过'; // 系统初验未通过文本\r\n\r\nimport {\r\n  supplierInfo, // 获取供应商信息API\r\n  approvalProcess, // 获取评分方法API\r\n  scoringFactors, // 提交评分因子API\r\n  checkReviewSummary, // 检查评审汇总API\r\n  filesById, // 获取项目相关文件API\r\n} from \"@/api/expert/review\"; // 导入专家评审相关API\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\"; // 获取评分详情API\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\"; // 编辑专家评分状态API\r\nimport { resDocReviewFactorsDecision } from \"@/api/docResponse/entInfo\"; // 获取响应文件评审因子决策API\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      supplierOptions: [], // 供应商下拉选项列表\r\n      scoringMethod: null, // 当前评分方法对象\r\n      selectedFactorNode: {}, // 当前选中的评分因子节点\r\n      selectedSupplierName: '', // 当前选中的供应商名称\r\n      selectedSupplier: {}, // 当前选中的供应商对象\r\n      expertInfo: {}, // 当前专家信息\r\n      ratingStateMap: {}, // 评分项状态映射（key为评分项ID，value为{state, reason}）\r\n      projectFiles: {}, // 项目相关文件对象\r\n      isShowResponse: false, // 是否显示响应文件\r\n      isShowProcurement: false, // 是否显示采购文件\r\n      isDoubleView: false, // 是否双文件对比模式\r\n      factorDetailList: [], // 评分因子详细列表\r\n      entDocResponsePage: null, // 企业响应文件页码信息\r\n      factorsPageMap: null, // 供应商因子页码映射\r\n      supplierFactorPage: null, // 当前供应商因子页码\r\n      responsePdfUrl: null, // 响应文件PDF地址\r\n      procurementPdfUrl: null, // 采购文件PDF地址\r\n\r\n      // 按钮状态管理\r\n      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n   entDocProcurementPage: {}, // 采购文件页码信息\r\n    pageProcurement:[], // 采购文件的评分项\r\n    attachmentsList:[], // 文件列表\r\n   \r\n   // PDF渲染状态管理\r\n   responsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n   procurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n   helpImgList: [\"/evalution/help.jpg\"], // 操作帮助图片列表\r\n\r\n    // PDF渲染状态控制\r\n    isPdfRendering: false, // PDF是否正在渲染\r\n      // 评分项名称与后端字段映射\r\n      factorCodeMap: {\r\n        \"特定资格要求\": \"zgzs\",\r\n        \"响应内容\": \"jsplb\",\r\n        \"采购需求\": \"jsplb\",\r\n        \"供货期限\": \"ghqx\",\r\n        \"投标报价\": \"tbbj\"\r\n      },\r\n      checkResult: {}, // 系统初验结果对象\r\n      // 系统初验结果名称映射\r\n      checkResultNameMap: {\r\n        \"符合《中华人民共和国政府采购法》第二十二条规定\": CHECK_PASS,\r\n        \"特定资格要求\": CHECK_PASS,\r\n        \"信用查询\": CHECK_PASS,\r\n        \"响应人名称\": CHECK_PASS,\r\n        \"响应内容\": CHECK_PASS,\r\n        \"采购需求\": CHECK_PASS,\r\n        \"供货期限\": CHECK_PASS,\r\n        \"投标报价\": CHECK_PASS\r\n      },\r\n      // 本地缓存数据\r\n      localExpertInfo: null, // 本地专家信息\r\n      localEntDocResponsePage: null, // 本地响应文件页码\r\n      localFactorsPageMap: null, // 本地因子页码映射\r\n\t    \r\n\t    // 悬停状态管理\r\n\t    hoveredFactorNode: null, // 悬停时的评分项\r\n\t    tooltipTimer: null, // 悬浮框显示定时器\r\n    };\r\n  },\r\n  methods: {\r\n    /**\r\n     * 校验所有评分项是否填写完整\r\n     * @returns {boolean} 是否全部填写\r\n     */\r\n    validateRatings() {\r\n      for (const item of this.scoringMethod.uitems) { // 遍历所有评分项\r\n        const state = this.ratingStateMap[item.entMethodItemId].state; // 获取评分状态\r\n        const reason = this.ratingStateMap[item.entMethodItemId].reason; // 获取评分原因\r\n        // 评分结果未填写\r\n        if (state === null || state === '') {\r\n          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`); // 提示未填写\r\n          return true;\r\n        }\r\n        // 不通过但未填写原因 - 将评审项设置为空，然后继续执行后续流程\r\n        if (state === FAIL && (!reason || reason.trim() === '')) {\r\n          // 将此评审项设置为空（未评审状态）\r\n          this.ratingStateMap[item.entMethodItemId].state = null;\r\n          this.ratingStateMap[item.entMethodItemId].reason = \"\";\r\n          console.log(`${item.itemName}评审不通过但未填写备注，已将该评审项设置为空`);\r\n        }\r\n      }\r\n      return true; // 全部填写返回true\r\n    },\r\n    /**\r\n     * 获取系统初验结果（通过/未通过）\r\n     * @param {string} factorName 评分项名称\r\n     * @returns {string} 1-通过 0-未通过\r\n     */\r\n    getCheckResultState(factorName) {\r\n      if (!this.checkResult || Object.keys(this.checkResult).length === 0) return ''; // 没有初验结果直接返回空\r\n      let code = this.factorCodeMap[factorName]; // 获取评分项对应的后端字段\r\n      let check = PASS; // 默认通过\r\n      if (code) {\r\n        check = this.checkResult[code]; // 获取初验结果\r\n        // 投标报价特殊处理\r\n        if (factorName === \"投标报价\" && check === PASS) {\r\n          check = this.checkResult['mxbjb']; // 明细报价表\r\n        }\r\n      }\r\n      // 设置初验结果名称\r\n      if (check === FAIL) {\r\n        this.checkResultNameMap[factorName] = CHECK_FAIL; // 未通过\r\n      } else {\r\n        check = PASS;\r\n        this.checkResultNameMap[factorName] = CHECK_PASS; // 通过\r\n      }\r\n      return check; // 返回初验结果\r\n    },\r\n    /**\r\n     * 重置所有评分项的状态\r\n     */\r\n    resetRatingStateMap() {\r\n      if (!this.scoringMethod) return; // 没有评分方法直接返回\r\n      for (const key of Object.keys(this.ratingStateMap)) { // 遍历所有评分项\r\n        this.ratingStateMap[key].state = null; // 重置状态\r\n        this.ratingStateMap[key].reason = ''; // 重置原因\r\n      }\r\n    },\r\n    /**\r\n     * 临时保存评分结果到后端\r\n     * 校验通过后才会保存\r\n     * @returns {boolean} 保存是否成功\r\n     */\r\n    async saveTempRating() {\r\n      if (!this.validateRatings()) return false; // 校验不通过不保存，返回false\r\n      // 构造提交数据\r\n      const data = this.scoringMethod.uitems.map(item => {\r\n        const itemId = item.entMethodItemId; // 获取评分项ID\r\n        return {\r\n          scoringMethodUitemId: itemId, // 评分项ID\r\n          expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n          entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          evaluationResult: this.ratingStateMap[itemId].state, // 评分结果\r\n          evaluationRemark: this.ratingStateMap[itemId].reason || '' // 评分原因\r\n        };\r\n      }).filter(d => d.evaluationResult !== null && d.evaluationResult !== ''); // 过滤未填写的项\r\n      if (data.length > 0) {\r\n        try {\r\n          const response = await scoringFactors(data); // 提交评分因子\r\n          if (response.code === 200) {\r\n            this.$message.success(\"保存成功\"); // 保存成功提示\r\n            return true; // 保存成功返回true\r\n          } else {\r\n            this.$message.warning(response.msg); // 保存失败提示\r\n            return false; // 保存失败返回false\r\n          }\r\n        } catch (e) {\r\n          this.$message.error(\"保存失败\"); // 异常提示\r\n          return false; // 异常返回false\r\n        }\r\n      }\r\n      return true; // 没有数据需要保存时也返回true\r\n    },\r\n    /**\r\n     * 供应商切换事件，切换时自动保存上一个供应商评分，并并发获取新供应商的评分详情和系统初验\r\n     * @param {string} supplierName 供应商名称\r\n     */\r\n    async handleSupplierChange(supplierName) {\r\n      // 切换前保存上一个供应商评分\r\n      if (Object.keys(this.selectedSupplier).length !== 0) {\r\n        await this.saveTempRating(); // 保存评分\r\n      }\r\n      // 查找当前选中的供应商对象\r\n      this.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName); // 查找供应商\r\n      // 获取当前供应商因子页码\r\n      this.supplierFactorPage = this.factorsPageMap[this.selectedSupplier.bidderId]; // 获取页码\r\n      // 并发获取评分详情和系统初验\r\n      // 使用 Promise.allSettled 让两个请求独立执行，互不影响\r\n      try {\r\n        const [detailResult, checkResult] = await Promise.allSettled([\r\n          getDetailByPsxx({\r\n            expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n          }),\r\n          resDocReviewFactorsDecision({\r\n            projectId: this.$route.query.projectId, // 项目ID\r\n            entId: this.selectedSupplier.bidderId, // 供应商ID\r\n          })\r\n        ]);\r\n\r\n        // 处理评分详情请求结果\r\n        if (detailResult.status === 'fulfilled') {\r\n          const detailRes = detailResult.value;\r\n          if (detailRes.code === 200) {\r\n            this.factorDetailList = detailRes.data; // 评分详情列表\r\n            const factor = this.factorDetailList.find(item => item.bidderName === supplierName)?.evalExpertEvaluationDetails; // 当前供应商评分详情\r\n            this.resetRatingStateMap(); // 重置评分状态\r\n            if (factor) {\r\n              for (const item of factor) {\r\n                this.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark; // 设置评分原因\r\n                this.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult; // 设置评分结果\r\n              }\r\n            }\r\n          } else {\r\n            this.$message.warning(detailRes.msg); // 评分详情获取失败\r\n          }\r\n        } else {\r\n          console.error(\"获取评分详情失败:\", detailResult.reason);\r\n          this.$message.error(\"获取评分详情失败\"); // 评分详情请求异常\r\n        }\r\n\r\n        // 处理系统初验请求结果\r\n        if (checkResult.status === 'fulfilled') {\r\n          const checkRes = checkResult.value;\r\n          if (checkRes.code === 200) {\r\n            this.checkResult = checkRes.data; // 设置初验结果\r\n          } else {\r\n            console.error(\"获取系统初验结果失败:\", checkRes.msg);\r\n            this.$message.warning(\"获取系统初验结果失败\"); // 系统初验获取失败\r\n          }\r\n        } else {\r\n          console.error(\"系统初验请求失败:\", checkResult.reason);\r\n          this.$message.error(\"系统初验请求失败\"); // 系统初验请求异常\r\n        }\r\n      } catch (e) {\r\n        console.error(\"请求处理异常:\", e);\r\n        this.$message.error(\"获取供应商详情失败\"); // 异常提示\r\n      }\r\n      // 默认显示响应文件\r\n      this.showResponseFile();\r\n    },\r\n    /**\r\n     * 显示响应文件PDF\r\n     */\r\n    showResponseFile() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'response'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowProcurement = false; // 不显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n      this.isPdfRendering = true; // 开始渲染PDF\r\n    },\r\n    /**\r\n     * 文件对比（双文件模式）\r\n     */\r\n    showFileContrast() {\r\n      if (!this.selectedSupplier || Object.keys(this.selectedSupplier).length === 0) {\r\n        this.$message.warning(\"请选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n      this.activeButton = 'contrast'; // 设置当前激活按钮\r\n      this.isDoubleView = true; // 双文件模式\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      this.isShowResponse = true; // 显示响应文件\r\n      this.responsePdfUrl = this.projectFiles.file[this.selectedSupplier.bidderId]; // 设置响应文件PDF地址\r\n      this.isPdfRendering = true; // 开始渲染PDF\r\n    },\r\n    /**\r\n     * 点击评分项名称，跳转到对应PDF页码\r\n     * @param {Object} factorItem 当前评分因子项\r\n     */\r\n    handleShowFactorInfo(factorItem) {\r\n\t    // 检查PDF是否渲染完成\r\n\t    if (!this.canJumpToPage()) {\r\n\t\t    this.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t    return;\r\n\t    }\r\n\t\t\t\r\n      this.selectedFactorNode = factorItem; // 设置当前选中因子\r\n\r\n      // 如果只显示采购文件，使用采购文件页码信息\r\n      if (this.isShowProcurement && !this.isShowResponse) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        if (factorItem.jumpToPage) {\r\n          this.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页\r\n        } else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 如果显示响应文件或对比模式，需要选择供应商\r\n      if (!this.supplierFactorPage || Object.keys(this.supplierFactorPage).length === 0) {\r\n        this.$message.warning(\"请先选择供应商\"); // 未选供应商提示\r\n        return;\r\n      }\r\n\r\n      // 跳转到响应文件对应页码\r\n      if (this.isShowResponse && this.$refs.response) {\r\n\t      if (!this.responsePdfRendered) {\r\n\t\t      this.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n        this.$refs.response.skipPage(this.supplierFactorPage[this.selectedFactorNode.itemName]); // 响应文件跳页\r\n      }\r\n\r\n      // 跳转到采购文件对应页码\r\n      if (this.isShowProcurement && this.$refs.procurement) {\r\n\t      if (!this.procurementPdfRendered) {\r\n\t\t      this.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t      return;\r\n\t      }\r\n\t\t\t\t\r\n        // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n        if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n          this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n        } else {\r\n          // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n          // 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n        }\r\n      }\r\n    },\r\n\t  \r\n\t  /**\r\n\t   * 检查是否可以跳转页面\r\n\t   * @returns {boolean} 是否可以跳转\r\n\t   */\r\n\t  canJumpToPage() {\r\n\t\t  // 如果只显示采购文件\r\n\t\t  if (this.isShowProcurement && !this.isShowResponse) {\r\n\t\t\t  return this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  // 如果只显示响应文件\r\n\t\t  if (this.isShowResponse && !this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered;\r\n\t\t  }\r\n\t\t  // 如果对比模式（两个都显示）\r\n\t\t  if (this.isShowResponse && this.isShowProcurement) {\r\n\t\t\t  return this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t  }\r\n\t\t  return false;\r\n\t  },\r\n\t  /**\r\n\t   * 处理PDF渲染状态变化\r\n\t   * @param {boolean} isRendered 是否渲染完成\r\n\t   * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t   */\r\n\t  handlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t   if (pdfType === 'response') {\r\n\t    this.responsePdfRendered = isRendered;\r\n\t   } else if (pdfType === 'procurement') {\r\n\t    this.procurementPdfRendered = isRendered;\r\n\t   }\r\n\t   \r\n\t   if (isRendered) {\r\n\t    console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t   }\r\n\r\n\t     // 检查所有显示的PDF是否都渲染完成\r\n\t     let allRendered = true;\r\n\t     if (this.isShowResponse && !this.responsePdfRendered) {\r\n\t       allRendered = false;\r\n\t     }\r\n\t     if (this.isShowProcurement && !this.procurementPdfRendered) {\r\n\t       allRendered = false;\r\n\t     }\r\n\t     \r\n\t     if (allRendered) {\r\n\t       this.isPdfRendering = false; // 所有PDF渲染完成，解除禁用状态\r\n\t     }\r\n\t  },\r\n\t  \r\n    /**\r\n     * 提交评分并修改专家进度\r\n     */\r\n    async submit() {\r\n      // 先保存评分，如果保存失败则不继续提交\r\n      const saveResult = await this.saveTempRating();\r\n      if (!saveResult) {\r\n        // 保存失败，不继续提交流程\r\n        return;\r\n      }\r\n\r\n      const data = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        expertResultId: this.expertInfo.resultId, // 专家结果ID\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项ID\r\n      };\r\n      try {\r\n        const response = await checkReviewSummary(data); // 检查评审汇总\r\n\r\n        if (response.code === 200) {\r\n          // 修改专家进度\r\n          const status = {\r\n            evalExpertScoreInfoId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\")).evalExpertScoreInfoId, // 专家评分信息ID\r\n            evalState: 1, // 进度状态\r\n          };\r\n          const res = await editEvalExpertScoreInfo(status); // 编辑专家评分状态\r\n          if (res.code === 200) {\r\n            this.$message.success(\"提交成功\"); // 提交成功提示\r\n          }\r\n          this.$emit(\"send\", \"two\"); // 发送事件\r\n        } else {\r\n          this.$message.warning(response.msg); // 提交失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"提交失败\"); // 异常提示\r\n      }\r\n    },\r\n    /**\r\n     * 显示采购文件PDF\r\n     */\r\n    viewPurchasing() {\r\n      this.activeButton = 'procurement'; // 设置当前激活按钮\r\n      this.isDoubleView = false; // 单文件模式\r\n      this.isShowResponse = false; // 不显示响应文件\r\n      this.isShowProcurement = true; // 显示采购文件\r\n      this.isPdfRendering = true; // 开始渲染PDF\r\n      \r\n      // 设置采购文件PDF地址\r\n      if (this.projectFiles.tenderNoticeFilePath) {\r\n        this.procurementPdfUrl = this.projectFiles.tenderNoticeFilePath;\r\n      }\r\n      \r\n      // 右侧评分项显示为采购文件的评分项\r\n      let pageProcurementArr = []; // 采购文件评分项数组\r\n\r\n      for (let item in this.entDocProcurementPage){\r\n        pageProcurementArr.push({\r\n          itemName: item,\r\n          jumpToPage: this.entDocProcurementPage[item]\r\n        })\r\n      }\r\n\r\n      console.log(this.scoringMethod.uitems);\r\n      console.log(pageProcurementArr)\r\n      this.pageProcurement = [];\r\n      for (let i = 0; i < this.scoringMethod.uitems.length;i++){\r\n        for (let j = 0; j < pageProcurementArr.length;j++){\r\n          if (this.scoringMethod.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n            this.pageProcurement.push({...this.scoringMethod.uitems[i],...pageProcurementArr[j]});\r\n          }\r\n        }\r\n      }\r\n      console.log(this.pageProcurement)\r\n    },\r\n    /**\r\n     * 跳转到二次报价页面\r\n     */\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 二次报价评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 跳转到询标页面\r\n     */\r\n    bidInquiry() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId, // 项目ID\r\n        zjhm: this.$route.query.zjhm, // 专家证件号码\r\n        scoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")), // 询标评分方法项ID\r\n      };\r\n      this.$router.push({ path: \"/bidInquiry\", query }); // 跳转页面\r\n    },\r\n    /**\r\n     * 获取因素对应页码（从本地缓存）\r\n     */\r\n    getFactorsPage() {\r\n      this.factorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\")); // 获取因子页码映射\r\n    },\r\n    /**\r\n     * 初始化专家和本地数据，只在mounted时调用一次\r\n     */\r\n    initLocalData() {\r\n      try {\r\n        this.localExpertInfo = JSON.parse(localStorage.getItem(\"expertInfo\") || \"{}\"); // 获取本地专家信息\r\n        this.localEntDocResponsePage = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地响应文件页码\r\n        this.localFactorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\") || \"{}\"); // 获取本地因子页码映射\r\n        this.expertInfo = this.localExpertInfo; // 设置专家信息\r\n        this.entDocResponsePage = this.localEntDocResponsePage; // 设置响应文件页码\r\n        this.factorsPageMap = this.localFactorsPageMap; // 设置因子页码映射\r\n        console.log(\"本地数据已初始化\", { expertInfo: this.expertInfo });\r\n      } catch (error) {\r\n        console.error(\"初始化本地数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 初始化专家信息（用于响应专家信息更新）\r\n     */\r\n    initExpertInfo() {\r\n      try {\r\n        const expertInfoStr = localStorage.getItem(\"expertInfo\");\r\n        if (expertInfoStr) {\r\n          this.localExpertInfo = JSON.parse(expertInfoStr);\r\n          this.expertInfo = this.localExpertInfo;\r\n          console.log(\"专家信息已刷新\", this.expertInfo);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"刷新专家信息失败:\", error);\r\n      }\r\n    },\r\n    /**\r\n     * 页面初始化，加载供应商、评分方法、文件等（并发请求）\r\n     */\r\n    async initPage() {\r\n      this.initLocalData(); // 初始化本地数据\r\n      try {\r\n        // 并发获取供应商、评分方法、文件\r\n        const [supplierRes, approvalRes, filesRes] = await Promise.all([\r\n          supplierInfo({ projectId: this.$route.query.projectId }), // 获取供应商\r\n          approvalProcess(this.$route.query.projectId, this.expertInfo.resultId), // 获取评分方法\r\n          filesById(this.$route.query.projectId) // 获取项目文件\r\n        ]);\r\n        // 处理供应商\r\n        if (supplierRes.code === 200) {\r\n          this.supplierOptions = supplierRes.rows.filter(item => item.isAbandonedBid == 0); // 过滤未弃标供应商\r\n        } else {\r\n          this.$message.warning(supplierRes.msg); // 获取失败提示\r\n        }\r\n        // 处理评分方法\r\n        if (approvalRes.code === 200) {\r\n\t        this.attachmentsList = approvalRes.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n          this.scoringMethod = approvalRes.data.scoringMethodUinfo.scoringMethodItems.find(\r\n            item => item.scoringMethodItemId == this.$route.query.scoringMethodItemId\r\n          ); // 获取当前评分方法\r\n          localStorage.setItem(\"evalProjectEvaluationProcess\", JSON.stringify(this.scoringMethod.evalProjectEvaluationProcess)); // 缓存评分流程\r\n          this.ratingStateMap = this.scoringMethod.uitems.reduce((acc, item) => {\r\n            acc[item.entMethodItemId] = { state: null, reason: '' }; // 初始化评分状态\r\n            return acc;\r\n          }, {});\r\n        } else {\r\n          this.$message.warning(approvalRes.msg); // 获取失败提示\r\n        }\r\n        // 处理文件\r\n        if (filesRes.code === 200) {\r\n          this.projectFiles = filesRes.data; // 设置项目文件\r\n          // 注释掉自动设置采购文件PDF，改为点击时才设置\r\n          // if (this.projectFiles.tenderNoticeFilePath) {\r\n          //   this.procurementPdfUrl = this.projectFiles.tenderNoticeFilePath; // 设置采购文件PDF\r\n          // }\r\n          // if (this.projectFiles.file) {\r\n          //   this.responsePdfUrl = this.projectFiles.file[0]; // 设置响应文件PDF\r\n          // }\r\n        } else {\r\n          this.$message.warning(filesRes.msg); // 获取失败提示\r\n        }\r\n      } catch (e) {\r\n        this.$message.error(\"页面初始化失败\"); // 异常提示\r\n      }\r\n    },\r\n\t  \r\n\t  downloadFile(item){\r\n\t\t  this.$download.zip(item.filePath,item.fileName);\r\n\t  },\r\n\t  \r\n\t  \r\n\t  // ========== 悬停相关 ==========\r\n\t  /**\r\n\t   * 显示评分项悬浮框\r\n\t   * @param {Object} factorItem 评分项对象\r\n\t   */\r\n\t  showFactorTooltip(factorItem) {\r\n\t\t  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\t\t  \r\n\t\t  // 清除之前的定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t  this.tooltipTimer = setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = factorItem;\r\n\t\t  }, 300); // 300ms延迟\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 隐藏评分项悬浮框\r\n\t   */\r\n\t  hideFactorTooltip() {\r\n\t\t  // 清除定时器\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t\t  \r\n\t\t  // 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t  setTimeout(() => {\r\n\t\t\t  this.hoveredFactorNode = null;\r\n\t\t  }, 100);\r\n\t  },\r\n\t  \r\n\t  /**\r\n\t   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t   */\r\n\t  clearTooltipTimer() {\r\n\t\t  if (this.tooltipTimer) {\r\n\t\t\t  clearTimeout(this.tooltipTimer);\r\n\t\t\t  this.tooltipTimer = null;\r\n\t\t  }\r\n\t  }\r\n  },\r\n  mounted() {\r\n\t  this.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\")); // 初始化采购文件页码信息\r\n    this.initPage(); // 页面挂载时初始化数据\r\n    this.getFactorsPage(); // 获取因子页码\r\n  },\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.compliance-main {\r\n  min-height: 57vh;\r\n  display: flex;\r\n}\r\n.compliance-left {\r\n  min-height: 57vh;\r\n  width: 79%;\r\n}\r\n.compliance-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-title-group {\r\n  display: flex;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  color: #333;\r\n}\r\n.compliance-title {\r\n  // nothing extra\r\n}\r\n.compliance-step-img-group {\r\n  display: grid;\r\n  justify-items: center;\r\n  position: relative;\r\n  bottom: -30px;\r\n}\r\n.compliance-step-text {\r\n  font-size: 12px;\r\n}\r\n.compliance-step-img {\r\n  width: 80px;\r\n  height: 30px;\r\n  margin-right: 20px;\r\n}\r\n.compliance-header-btns {\r\n  text-align: right;\r\n}\r\n.compliance-header-btns-bottom {\r\n  margin-top: 20px;\r\n}\r\n.compliance-blue-btn {\r\n  background-color: #176ADB !important;\r\n  color: #fff !important;\r\n  border: 1px solid #176ADB !important;\r\n}\r\n.compliance-blue-btn-active {\r\n  background-color: #FF6B35 !important;\r\n  color: #fff !important;\r\n  border: 1px solid #FF6B35 !important;\r\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.compliance-pdf-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  height: 82%;\r\n}\r\n.compliance-pdf {\r\n  width: 49%;\r\n}\r\n.compliance-pdf-border-right {\r\n  border-right: 1px solid #176ADB;\r\n}\r\n.compliance-pdf-border-left {\r\n  border-left: 1px solid #176ADB;\r\n}\r\n.compliance-divider {\r\n  min-height: 57vh;\r\n  width: 1%;\r\n  background-color: #F5F5F5;\r\n}\r\n.compliance-right {\r\n  min-height: 57vh;\r\n  width: 20%;\r\n}\r\n.compliance-select-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 2px solid #176ADB;\r\n  padding: 15px 20px;\r\n}\r\n.compliance-select {\r\n  width: 100%;\r\n}\r\n\r\n// 禁用状态的下拉框样式\r\n::v-deep .el-select {\r\n  &.is-disabled {\r\n    .el-input__inner {\r\n      background-color: #f5f5f5;\r\n      border-color: #e4e7ed;\r\n      color: #c0c4cc;\r\n      cursor: not-allowed;\r\n    }\r\n  }\r\n}\r\n.compliance-factors-group {\r\n  padding: 15px 20px;\r\n}\r\n.compliance-factor-title-group {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  text-align: left;\r\n  width: 98%;\r\n}\r\n.compliance-factor-title {\r\n  cursor: pointer;\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #333;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-factor-radio-group {\r\n  display: flex;\r\n  width: 100%;\r\n  justify-content: flex-end;\r\n}\r\n.compliance-factor-divider {\r\n  height: 1px;\r\n  background-color: #DCDFE6;\r\n  margin-top: 10px;\r\n}\r\n.compliance-submit-group {\r\n  display: flex;\r\n  margin: 32px 0;\r\n  justify-content: space-evenly;\r\n}\r\n.compliance-submit-btn {\r\n  background-color: #176ADB;\r\n}\r\n.compliance-review-content {\r\n  text-align: left;\r\n  font-size: 14px;\r\n}\r\n.compliance-review-title {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 15px;\r\n  color: #176ADB;\r\n  letter-spacing: 0;\r\n}\r\n.compliance-review-html {\r\n  padding: 6px 30px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n\t  transition: all 0.3s ease;\r\n\t  padding: 4px 8px;\r\n\t  border-radius: 4px;\r\n\t  \r\n\t  &:hover {\r\n\t\t  background-color: #f0f8ff;\r\n\t\t  color: #176ADB;\r\n\t\t  transform: translateX(2px);\r\n\t  }\r\n  }\r\n}\r\n.item-button {\r\n  border: 1px solid #979797;\r\n  width: 150px;\r\n  height: 36px;\r\n  margin: 0 10px;\r\n  font-weight: 700;\r\n  font-size: 17px;\r\n  border-radius: 6px;\r\n  color: #333;\r\n  &:hover {\r\n    color: #333;\r\n  }\r\n  \r\n  // 禁用状态样式\r\n  &[disabled] {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    background-color: #f5f5f5 !important;\r\n    color: #c0c4cc !important;\r\n    border-color: #e4e7ed !important;\r\n  }\r\n}\r\n.item-button-little {\r\n  width: 124px;\r\n  height: 36px;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\t.fileItem {\r\n\t\ttransition: all 0.3s ease;\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\t\t\r\n\t\t::v-deep .el-card__body {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\t\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\t\t\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\t\t\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\t\t\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\t\t\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n\r\n</style>\r\n"]}]}