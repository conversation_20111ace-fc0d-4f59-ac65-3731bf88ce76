{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidder\\notice\\add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidder\\notice\\add.vue", "mtime": 1753957830693}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UHJvamVjdCB9IGZyb20gIkAvYXBpL3RlbmRlci9wcm9qZWN0IjsNCmltcG9ydCB7IGxpc3RJbmZvIH0gZnJvbSAiQC9hcGkvYmlkZGluZy9pbmZvIjsNCmltcG9ydCB7DQogIGFkZE5vdGljZSwNCiAgdXBkYXRlTm90aWNlLA0KICBjcmVhdGVOb3RpY2VDb250ZW50LA0KfSBmcm9tICJAL2FwaS9iaWRkZXIvbm90aWNlIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7fSwNCiAgZGljdHM6IFsiYnVzaV9iaWRkZXJfbm90aWNlX2F0dGFjaG1lbnQiLCAicHJpY2VfdW5pdCJdLA0KICBwcm9wczogW10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIGluaXRQcm9qZWN0OiBmYWxzZSwNCiAgICAgIGZvcm1EYXRhOiB7DQogICAgICAgIHByb2plY3RJZDogIiIsDQogICAgICAgIG5vdGljZU5hbWU6ICIiLA0KICAgICAgICBub3RpY2VDb2RlOiAiIiwNCiAgICAgICAgbm90aWNlQ29udGVudDogIiIsDQogICAgICAgIG5vdGljZVR5cGU6IDEsDQogICAgICAgIG5vdGljZVN0YXJ0VGltZTogIiIsDQogICAgICAgIGJpZGRlcklkOiAiIiwNCiAgICAgICAgYmlkZGVyQ29kZTogIiIsDQogICAgICAgIGJpZGRlck5hbWU6ICIiLA0KICAgICAgICBiaWRBbW91bnQ6ICIiLA0KICAgICAgICByYW5raW5nOiAiIiwNCiAgICAgICAgc2NvcmU6ICIiLA0KICAgICAgICBhdHRhY2htZW50czogW10sDQogICAgICB9LA0KICAgICAgYmlkZGVyVGFibGU6IFtdLA0KICAgICAgbm90aWNlTmFtZTogIiIsDQogICAgICBydWxlczogew0KICAgICAgICBwcm9qZWN0SWQ6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6kiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgbm90aWNlTmFtZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeWFrOWRiuWQjeensCIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgbm90aWNlU3RhcnRUaW1lOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup5pel5pyfIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIG5vdGljZUNvbnRlbnQ6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogZmFsc2UsDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl6aG555uu5ZCN56ewIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIHByb2plY3RJZE9wdGlvbnM6IFtdLA0KICAgICAgYXR0YWNobWVudHNNYXA6IHt9LA0KICAgICAgLy8g5YWs5ZGK5byA5aeL5pe26Ze05pel5pyf6YCJ5oup55qE56aB55So5pel5pyfDQogICAgICBwaWNrZXJPcHRpb25zT25lOiB7DQogICAgICAgIGRpc2FibGVkRGF0ZTogKHRpbWUpID0+IHsNCiAgICAgICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBuZXcgRGF0ZSgpIC0gOC42NGU3Ow0KICAgICAgICB9LA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDoge30sDQogIHdhdGNoOiB7fSwNCiAgY3JlYXRlZCgpIHt9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMuZm9ybURhdGEubm90aWNlU3RhcnRUaW1lID0gbmV3IERhdGUoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldFNlbGVjdGVkV2luQmlkZGVycygpIHsNCiAgICAgIHJldHVybiB0aGlzLmJpZGRlclRhYmxlLmZpbHRlcihiaWRkZXIgPT4gYmlkZGVyLmlzV2luQ2hlY2tCb3gpOw0KICAgIH0sDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIGxpc3RQcm9qZWN0KHsgZGVsRmxhZzogMCwgcHJvamVjdFN0YXR1czogNTAgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5wcm9qZWN0SWRPcHRpb25zID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQpIHsNCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLnByb2plY3RJZCA9IHBhcnNlSW50KHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCk7DQogICAgICAgICAgdGhpcy4kcmVmcy5zZWxlY3RQcm9qZWN0LiRlbWl0KCJjaGFuZ2UiLCB0aGlzLmZvcm1EYXRhLnByb2plY3RJZCk7DQogICAgICAgICAgdGhpcy5pbml0UHJvamVjdCA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgY2hhbmdlKHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUpIHsNCiAgICAgICAgY29uc3Qgc2VsZWN0ZWQgPSB0aGlzLnByb2plY3RJZE9wdGlvbnMuZmluZCgob3B0aW9uKSA9PiB7DQogICAgICAgICAgcmV0dXJuIG9wdGlvbi5wcm9qZWN0SWQgPT0gdmFsdWU7DQogICAgICAgIH0pOw0KDQogICAgICAgIHRoaXMuZm9ybURhdGEubm90aWNlTmFtZSA9IHNlbGVjdGVkLnByb2plY3ROYW1lICsgIi3nu5PmnpzlhazlkYoiOw0KICAgICAgICB0aGlzLmZvcm1EYXRhLnByb2plY3RDb2RlID0gc2VsZWN0ZWQucHJvamVjdENvZGU7DQogICAgICAgIGxpc3RJbmZvKHsgcHJvamVjdElkOiB2YWx1ZSB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIHRoaXMuYmlkZGVyVGFibGUgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB9KTsNCiAgICAgICAgLy8gZ2V0QmlkZGVySW5mb0J5UHJvamVjdElEKHZhbHVlKS50aGVuKChyZXN1bHQpID0+IHsNCiAgICAgICAgLy8gICBpZiAocmVzdWx0LmNvZGUgPT0gMjAwKSB7DQogICAgICAgIC8vICAgIC8vIHRoaXMuZm9ybURhdGEubm90aWNlQ29udGVudCA9IHJlc3VsdC5kYXRhOw0KICAgICAgICAvLyAgICAgY29uc29sZS5sb2cocmVzdWx0KQ0KICAgICAgICAvLyAgICAgdGhpcy5iaWRkZXJUYWJsZSA9IHJlc3VsdC5kYXRhOyAvLyDlsIbmlbDmja7otYvlgLznu5liaWRkZXJUYWJsZQ0KDQogICAgICAgIC8vICAgfQ0KICAgICAgICAvLyB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHJlc2V0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbIndpbm5pbmdCaWRkZXJOb3RpY2UiXS5yZXNldEZpZWxkcygpOw0KICAgIH0sDQogICAgaGFuZGxlSW5wdXQoZGljdCwgZGF0YSkgew0KICAgICAgaWYgKCFkYXRhIHx8IGRhdGEgPT0gIiIpIHsNCiAgICAgICAgZGVsZXRlIHRoaXMuYXR0YWNobWVudHNNYXBbZGljdC52YWx1ZV07DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgZmlsZUxpc3QgPSBkYXRhLnNwbGl0KCIsIik7DQogICAgICAgIGZpbGVMaXN0ID0gZmlsZUxpc3QubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIGZpbGVOYW1lOiBpdGVtLnN1YnN0cmluZyhpdGVtLmxhc3RJbmRleE9mKCIvIikgKyAxKSwNCiAgICAgICAgICAgIGZpbGVUeXBlOiBkaWN0LnZhbHVlLA0KICAgICAgICAgICAgZmlsZVN1ZmZpeDogaXRlbS5zdWJzdHJpbmcoaXRlbS5sYXN0SW5kZXhPZigiLiIpICsgMSksDQogICAgICAgICAgICBmaWxlUGF0aDogaXRlbSwNCiAgICAgICAgICB9Ow0KICAgICAgICB9KTsNCiAgICAgICAgdGhpcy5hdHRhY2htZW50c01hcFtkaWN0LnZhbHVlXSA9IGZpbGVMaXN0Ow0KICAgICAgfQ0KICAgIH0sDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5kaWN0LnR5cGUuYnVzaV9iaWRkZXJfbm90aWNlX2F0dGFjaG1lbnQpIHsNCiAgICAgICAgaWYgKA0KICAgICAgICAgIGl0ZW0ucmF3LmlzRXF1YWxzID09IDEgJiYNCiAgICAgICAgICB0aGlzLmF0dGFjaG1lbnRzTWFwW2l0ZW0udmFsdWVdID09IHVuZGVmaW5lZA0KICAgICAgICApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGl0ZW0ubGFiZWwgKyAiIOaWh+S7tuS4jeiDveS4uuepuiIpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY29uc3Qgc2VsZWN0ZWRXaW5CaWRkZXJzID0gdGhpcy5nZXRTZWxlY3RlZFdpbkJpZGRlcnMoKTsNCiAgICAgIGNvbnNvbGUubG9nKCfpgInkuK3nmoTkuK3moIfkurrmlbDmja46Jywgc2VsZWN0ZWRXaW5CaWRkZXJzKTsNCiAgICAgIGlmIChzZWxlY3RlZFdpbkJpZGRlcnMubGVuZ3RoICE9PSAxKSB7DQogICAgICAgIC8vIOWmguaenOS4jeaYr+S4gOadoeiusOW9le+8jOW8ueWHuumUmeivr+S/oeaBrw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73pgInkuK3kuIDmnaHkuK3moIfkurrmlbDmja4nKTsNCiAgICAgIH0NCg0KICAgICAgLypjb25zdCB3aW5uaW5nQmlkZGVyID0gdGhpcy5iaWRkZXJUYWJsZS5maW5kKChiaWRkZXIpID0+IHsNCiAgICAgICAgYmlkZGVyLmlzV2luID09IDE7DQogICAgICB9KTsqLw0KICAgICAgY29uc3Qgd2lubmVyID0gdGhpcy5nZXRTZWxlY3RlZFdpbkJpZGRlcnMoKVswXTsNCg0KICAgICAgdGhpcy4kcmVmc1sid2lubmluZ0JpZGRlck5vdGljZSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm47DQoNCiAgICAgICAgaWYgKHRoaXMuJHJvdXRlLnBhcmFtcy5ub3RpY2VJZCA9PSAwKSB7DQogICAgICAgICAgdGhpcy5mb3JtRGF0YS5hdHRhY2htZW50cyA9IFtdLmNvbmNhdCgNCiAgICAgICAgICAgIC4uLk9iamVjdC52YWx1ZXModGhpcy5hdHRhY2htZW50c01hcCkNCiAgICAgICAgICApOw0KICAgICAgICAgIHRoaXMuZm9ybURhdGEuYmlkZGVySWQgPSB3aW5uZXIuYmlkZGVySWQ7DQogICAgICAgICAgdGhpcy5mb3JtRGF0YS5iaWRkZXJDb2RlID0gd2lubmVyLmJpZGRlckNvZGU7DQogICAgICAgICAgdGhpcy5mb3JtRGF0YS5iaWRkZXJOYW1lID0gd2lubmVyLmJpZGRlck5hbWU7DQogICAgICAgICAgdGhpcy5mb3JtRGF0YS5iaWRBbW91bnQgPSB3aW5uZXIuYmlkZGVyQW1vdW50Ow0KICAgICAgICAgIHRoaXMuZm9ybURhdGEucmFua2luZyA9IHdpbm5lci5yYW5raW5nOw0KICAgICAgICAgIHRoaXMuZm9ybURhdGEuc2NvcmUgPSB3aW5uZXIuc2NvcmU7DQogICAgICAgICAgY29uc29sZS5sb2coInN1Ym1pdO+8miIpDQogICAgICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtRGF0YSkNCiAgICAgICAgICBhZGROb3RpY2UodGhpcy5mb3JtRGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5jbG9zZSgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHVwZGF0ZU5vdGljZSh0aGlzLmZvcm1EYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICB0aGlzLmNsb3NlKCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0SW1nUGF0aChkaWN0KSB7DQogICAgICBpZiAoDQogICAgICAgIHRoaXMuYXR0YWNobWVudHNNYXBbZGljdC52YWx1ZV0gJiYNCiAgICAgICAgdGhpcy5hdHRhY2htZW50c01hcFtkaWN0LnZhbHVlXS5sZW5ndGggPiAwDQogICAgICApIHsNCiAgICAgICAgbGV0IGFyciA9IHRoaXMuYXR0YWNobWVudHNNYXBbZGljdC52YWx1ZV07DQogICAgICAgIHJldHVybiBhcnINCiAgICAgICAgICAubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgICByZXR1cm4gaXRlbS5maWxlUGF0aDsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5qb2luKCIsIik7DQogICAgICB9DQogICAgICByZXR1cm4gIiI7DQogICAgfSwNCiAgICB3aW5CaWRkZXIodmFsKSB7DQogICAgICB2YXIgYmlkZGluZ0luZm9MaXN0ID0gdGhpcy5iaWRkZXJUYWJsZTsNCiAgICAgIHZhciB3aW5CaWRkZXIgPSAnJzsNCiAgICAgIGZvciAodmFyIGluZGV4IGluIGJpZGRpbmdJbmZvTGlzdCkgew0KICAgICAgICBpZiAoYmlkZGluZ0luZm9MaXN0W2luZGV4XS5iaWRkZXJJZCA9PSB2YWwpIHsNCiAgICAgICAgICBiaWRkaW5nSW5mb0xpc3RbaW5kZXhdLmlzV2luID0gMTsNCiAgICAgICAgICB3aW5CaWRkZXIgPSBiaWRkaW5nSW5mb0xpc3RbaW5kZXhdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGJpZGRpbmdJbmZvTGlzdFtpbmRleF0uaXNXaW4gPSAwOw0KICAgICAgICAgIGJpZGRpbmdJbmZvTGlzdFtpbmRleF0uaXNXaW5DaGVja0JveCA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBjb25zb2xlLmxvZygnd2luQmlkZGVyJyx3aW5CaWRkZXIpOw0KDQogICAgICBjcmVhdGVOb3RpY2VDb250ZW50KHdpbkJpZGRlci5wcm9qZWN0SWQsIHdpbkJpZGRlci5iaWRkZXJJbmZvSWQpLnRoZW4oKHJlc3VsdCkgPT4gew0KICAgICAgICBpZiAocmVzdWx0LmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5mb3JtRGF0YS5ub3RpY2VDb250ZW50ID0gcmVzdWx0LmRhdGE7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5YWz6Zet5b2T5YmN6aG1DQogICAgY2xvc2UoKSB7DQogICAgICB0aGlzLiR0YWIuY2xvc2VQYWdlKCk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgSA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add.vue", "sourceRoot": "src/views/bidder/notice", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"tender\"\r\n    style=\"margin: 20px 0px;\"\r\n    v-loading=\"loading\"\r\n  >\r\n    <el-form\r\n      ref=\"winningBidderNotice\"\r\n      :model=\"formData\"\r\n      :rules=\"rules\"\r\n      size=\"medium\"\r\n      label-width=\"0\"\r\n    >\r\n      <el-col\r\n        :span=\"24\"\r\n        class=\"card-box\"\r\n      >\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-suitcase\"></i>开标情况</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table\r\n              cellspacing=\"0\"\r\n              style=\"width: 100%;table-layout:fixed;\"\r\n            >\r\n              <tbody>\r\n                <tr>\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong style=\"color:red;\">*</strong>项目\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"22\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <el-form-item prop=\"projectId\">\r\n                        <el-select\r\n                          v-model=\"formData.projectId\"\r\n                          placeholder=\"请选择项目\"\r\n                          ref=\"selectProject\"\r\n                          filterable\r\n                          clearable\r\n                          :disabled=\"initProject\"\r\n                          :style=\"{ width: '100%' }\"\r\n                          @change=\"change($event)\"\r\n                        >\r\n                          <el-option\r\n                            v-for=\"(item, index) in projectIdOptions\"\r\n                            :key=\"index\"\r\n                            :label=\"item.projectName\"\r\n                            :value=\"item.projectId\"\r\n                            :disabled=\"item.disabled\"\r\n                          ></el-option>\r\n                        </el-select>\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong style=\"color:red;\">*</strong>公告名称\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"22\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <el-form-item prop=\"noticeName\">\r\n                        <el-input\r\n                          v-model=\"formData.noticeName\"\r\n                          placeholder=\"公告名称\"\r\n                          clearable\r\n                          :style=\"{ width: '100%' }\"\r\n                        >\r\n                        </el-input>\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong style=\"color:red;\">*</strong>项目编号\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"10\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <el-form-item prop=\"projectId\">\r\n                        {{formData.projectCode}}\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong style=\"color:red;\">*</strong>公告发布时间\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"10\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <el-form-item prop=\"noticeStartTime\">\r\n                        <el-date-picker\r\n                          v-model=\"formData.noticeStartTime\"\r\n                          format=\"yyyy-MM-dd\"\r\n                          value-format=\"yyyy-MM-dd\"\r\n                          :picker-options=\"pickerOptionsOne\"\r\n                          :style=\"{ width: '100%' }\"\r\n                          placeholder=\"请选择日期\"\r\n                          clearable\r\n                          disabled\r\n                        ></el-date-picker>\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col\r\n        :span=\"24\"\r\n        class=\"card-box\"\r\n      >\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-document\"></i>中标人信息</span>\r\n          </div>\r\n          <div>\r\n            <el-table\r\n              :data=\"bidderTable\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"100\"\r\n              ></el-table-column>\r\n              <el-table-column\r\n                v-if=\"false\"\r\n                label=\"投标人记录id\"\r\n                prop=\"bidderInfoId\"\r\n              ></el-table-column>\r\n              <el-table-column\r\n                label=\"投标人名称\"\r\n                prop=\"bidderName\"\r\n              ></el-table-column>\r\n              <el-table-column\r\n                label=\"投标报价\"\r\n                prop=\"bidderAmount\"\r\n              ></el-table-column>\r\n\t            <el-table-column\r\n\t\t            label=\"最终得分\"\r\n\t\t            prop=\"score\"\r\n\t            ></el-table-column>\r\n<!--              <el-table-column\r\n                prop=\"score\"\r\n                label=\"评分\"\r\n              > </el-table-column>-->\r\n<!--              <el-table-column\r\n                prop=\"ranking\"\r\n                label=\"名次\"\r\n              > </el-table-column>-->\r\n              <el-table-column label=\"确认中标人\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-checkbox\r\n                    v-model=\"scope.row.isWinCheckBox\"\r\n                    @change=\"winBidder(scope.row.bidderId)\"\r\n                  ></el-checkbox>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n<!-- <el-col\r\n  :span=\"24\"\r\n  class=\"card-box\"\r\n>\r\n  <el-card>\r\n    <div slot=\"header\">\r\n      <span><i class=\"el-icon-document\"></i>公告内容</span>\r\n    </div>\r\n    <div>\r\n      <el-form-item prop=\"noticeContent\">\r\n        <div\r\n          v-html=\"formData.noticeContent\"\r\n          :min-height=\"192\"\r\n        />\r\n      </el-form-item>\r\n    </div>\r\n  </el-card>\r\n</el-col> -->\r\n\r\n      <el-col\r\n        :span=\"24\"\r\n        class=\"card-box\"\r\n      >\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-document\"></i>附件</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table\r\n              cellspacing=\"0\"\r\n              style=\"width: 100%;table-layout:fixed;\"\r\n            >\r\n              <tbody>\r\n                <tr\r\n                  v-for=\"dict in dict.type.busi_bidder_notice_attachment\"\r\n                  :key=\"dict.label\"\r\n                >\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong\r\n                        style=\"color:red;\"\r\n                        v-if=\"dict.raw.isEquals==1\"\r\n                      >*</strong>{{ dict.label }}\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"22\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell cell-right-border\">\r\n                      <el-form-item class=\"upload\">\r\n                        <template>\r\n                          <FileUpload\r\n                            :value=\"getImgPath(dict)\"\r\n                            @input=\"handleInput(dict, $event)\"\r\n                            :fileType=\"['pdf', 'doc', 'docx']\"\r\n                            :isShowTip=\"false\"\r\n                          ></FileUpload>\r\n                        </template>\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n    </el-form>\r\n    <div\r\n      slot=\"footer\"\r\n      class=\"dialog-footer\"\r\n      style=\"text-align: center;\"\r\n    >\r\n      <el-button\r\n        type=\"primary\"\r\n        @click=\"submitForm\"\r\n      >发布</el-button>\r\n      <!-- <el-button type=\"primary\" @click=\"TemporaryStorage\">暂存</el-button> -->\r\n      <el-button @click=\"resetForm\">重置</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listProject } from \"@/api/tender/project\";\r\nimport { listInfo } from \"@/api/bidding/info\";\r\nimport {\r\n  addNotice,\r\n  updateNotice,\r\n  createNoticeContent,\r\n} from \"@/api/bidder/notice\";\r\n\r\nexport default {\r\n  components: {},\r\n  dicts: [\"busi_bidder_notice_attachment\", \"price_unit\"],\r\n  props: [],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      initProject: false,\r\n      formData: {\r\n        projectId: \"\",\r\n        noticeName: \"\",\r\n        noticeCode: \"\",\r\n        noticeContent: \"\",\r\n        noticeType: 1,\r\n        noticeStartTime: \"\",\r\n        bidderId: \"\",\r\n        bidderCode: \"\",\r\n        bidderName: \"\",\r\n        bidAmount: \"\",\r\n        ranking: \"\",\r\n        score: \"\",\r\n        attachments: [],\r\n      },\r\n      bidderTable: [],\r\n      noticeName: \"\",\r\n      rules: {\r\n        projectId: [\r\n          {\r\n            required: true,\r\n            message: \"请选择\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        noticeName: [\r\n          {\r\n            required: true,\r\n            message: \"请输入公告名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        noticeStartTime: [\r\n          {\r\n            required: true,\r\n            message: \"请选择日期\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        noticeContent: [\r\n          {\r\n            required: false,\r\n            message: \"请输入项目名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      projectIdOptions: [],\r\n      attachmentsMap: {},\r\n      // 公告开始时间日期选择的禁用日期\r\n      pickerOptionsOne: {\r\n        disabledDate: (time) => {\r\n          return time.getTime() < new Date() - 8.64e7;\r\n        },\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  created() {},\r\n  mounted() {\r\n    this.getList();\r\n    this.formData.noticeStartTime = new Date();\r\n  },\r\n  methods: {\r\n    getSelectedWinBidders() {\r\n      return this.bidderTable.filter(bidder => bidder.isWinCheckBox);\r\n    },\r\n    getList() {\r\n      listProject({ delFlag: 0, projectStatus: 50 }).then((response) => {\r\n        this.projectIdOptions = response.rows;\r\n        this.loading = false;\r\n        if (this.$route.query.projectId) {\r\n          this.formData.projectId = parseInt(this.$route.query.projectId);\r\n          this.$refs.selectProject.$emit(\"change\", this.formData.projectId);\r\n          this.initProject = true;\r\n        }\r\n      });\r\n    },\r\n    change(value) {\r\n      if (value) {\r\n        const selected = this.projectIdOptions.find((option) => {\r\n          return option.projectId == value;\r\n        });\r\n\r\n        this.formData.noticeName = selected.projectName + \"-结果公告\";\r\n        this.formData.projectCode = selected.projectCode;\r\n        listInfo({ projectId: value }).then((response) => {\r\n          this.bidderTable = response.rows;\r\n        });\r\n        // getBidderInfoByProjectID(value).then((result) => {\r\n        //   if (result.code == 200) {\r\n        //    // this.formData.noticeContent = result.data;\r\n        //     console.log(result)\r\n        //     this.bidderTable = result.data; // 将数据赋值给bidderTable\r\n\r\n        //   }\r\n        // });\r\n      }\r\n    },\r\n    resetForm() {\r\n      this.$refs[\"winningBidderNotice\"].resetFields();\r\n    },\r\n    handleInput(dict, data) {\r\n      if (!data || data == \"\") {\r\n        delete this.attachmentsMap[dict.value];\r\n      } else {\r\n        let fileList = data.split(\",\");\r\n        fileList = fileList.map((item) => {\r\n          return {\r\n            fileName: item.substring(item.lastIndexOf(\"/\") + 1),\r\n            fileType: dict.value,\r\n            fileSuffix: item.substring(item.lastIndexOf(\".\") + 1),\r\n            filePath: item,\r\n          };\r\n        });\r\n        this.attachmentsMap[dict.value] = fileList;\r\n      }\r\n    },\r\n    submitForm() {\r\n      for (let item of this.dict.type.busi_bidder_notice_attachment) {\r\n        if (\r\n          item.raw.isEquals == 1 &&\r\n          this.attachmentsMap[item.value] == undefined\r\n        ) {\r\n          this.$message.error(item.label + \" 文件不能为空\");\r\n          return;\r\n        }\r\n      }\r\n      const selectedWinBidders = this.getSelectedWinBidders();\r\n      console.log('选中的中标人数据:', selectedWinBidders);\r\n      if (selectedWinBidders.length !== 1) {\r\n        // 如果不是一条记录，弹出错误信息\r\n        this.$message.error('只能选中一条中标人数据');\r\n      }\r\n\r\n      /*const winningBidder = this.bidderTable.find((bidder) => {\r\n        bidder.isWin == 1;\r\n      });*/\r\n      const winner = this.getSelectedWinBidders()[0];\r\n\r\n      this.$refs[\"winningBidderNotice\"].validate((valid) => {\r\n        if (!valid) return;\r\n\r\n        if (this.$route.params.noticeId == 0) {\r\n          this.formData.attachments = [].concat(\r\n            ...Object.values(this.attachmentsMap)\r\n          );\r\n          this.formData.bidderId = winner.bidderId;\r\n          this.formData.bidderCode = winner.bidderCode;\r\n          this.formData.bidderName = winner.bidderName;\r\n          this.formData.bidAmount = winner.bidderAmount;\r\n          this.formData.ranking = winner.ranking;\r\n          this.formData.score = winner.score;\r\n          console.log(\"submit：\")\r\n          console.log(this.formData)\r\n          addNotice(this.formData).then((response) => {\r\n            this.$modal.msgSuccess(\"新增成功\");\r\n            this.close();\r\n          });\r\n        } else {\r\n          updateNotice(this.formData).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.close();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getImgPath(dict) {\r\n      if (\r\n        this.attachmentsMap[dict.value] &&\r\n        this.attachmentsMap[dict.value].length > 0\r\n      ) {\r\n        let arr = this.attachmentsMap[dict.value];\r\n        return arr\r\n          .map((item) => {\r\n            return item.filePath;\r\n          })\r\n          .join(\",\");\r\n      }\r\n      return \"\";\r\n    },\r\n    winBidder(val) {\r\n      var biddingInfoList = this.bidderTable;\r\n      var winBidder = '';\r\n      for (var index in biddingInfoList) {\r\n        if (biddingInfoList[index].bidderId == val) {\r\n          biddingInfoList[index].isWin = 1;\r\n          winBidder = biddingInfoList[index];\r\n        } else {\r\n          biddingInfoList[index].isWin = 0;\r\n          biddingInfoList[index].isWinCheckBox = false;\r\n        }\r\n      }\r\n      console.log('winBidder',winBidder);\r\n\r\n      createNoticeContent(winBidder.projectId, winBidder.bidderInfoId).then((result) => {\r\n        if (result.code == 200) {\r\n          this.formData.noticeContent = result.data;\r\n        }\r\n      });\r\n    },\r\n    // 关闭当前页\r\n    close() {\r\n      this.$tab.closePage();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style>\r\n.tender {\r\n  padding: 0 50px;\r\n}\r\n\r\n.makeTenserFile {\r\n  width: 208px;\r\n  border: rgba(0, 0, 0, 1) solid 1px;\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  font-family: Microsoft YaHei;\r\n  color: rgba(80, 80, 80, 1);\r\n  line-height: 150%;\r\n  font-size: 14px;\r\n\r\n  text-align: center;\r\n  vertical-align: middle;\r\n}\r\n.makeTenserFile:hover :active :focus {\r\n  color: rgba(80, 80, 80, 1);\r\n}\r\n\r\n.attachment {\r\n  height: 27px;\r\n  left: 64px;\r\n  top: 668px;\r\n  color: rgba(80, 80, 80, 1);\r\n  font-size: 18px;\r\n  line-height: 150%;\r\n  text-align: left;\r\n}\r\n.line {\r\n  width: 100%;\r\n  height: 2px;\r\n  left: 64px;\r\n  top: 700px;\r\n  color: rgba(80, 80, 80, 1);\r\n  background-color: rgba(58, 25, 236, 1);\r\n  font-size: 14px;\r\n  line-height: 150%;\r\n  text-align: center;\r\n\r\n  margin-bottom: 25px;\r\n}\r\n.option {\r\n  text-align: center;\r\n}\r\n.select-option {\r\n  z-index: 999 !important;\r\n}\r\n</style>\r\n<style scoped>\r\n.el-form-item {\r\n  margin-bottom: 0px;\r\n}\r\n/deep/ .el-upload {\r\n  float: right;\r\n}\r\n/deep/ .el-upload-list {\r\n  width: 90%;\r\n}\r\n/deep/ .upload > .el-form-item__content {\r\n  border-bottom: rgba(153, 153, 153, 1) solid 1px;\r\n}\r\n</style>\r\n"]}]}