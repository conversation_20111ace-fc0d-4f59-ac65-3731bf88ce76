{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\suppliersRoom.vue?vue&type=style&index=0&id=a374ce3e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\suppliersRoom.vue", "mtime": 1753956770122}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCjo6di1kZWVwIC5lbC1jYXJkX19ib2R5IHsNCiAgcGFkZGluZzogMDsNCn0NCg=="}, {"version": 3, "sources": ["suppliersRoom.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkrBA;AACA;AACA", "file": "suppliersRoom.vue", "sourceRoot": "src/views/bidOpeningHall", "sourcesContent": ["<template>\r\n  <div>\r\n    <BidHeadone ref=\"head\" @updateStatus=\"handleStatus\"></BidHeadone>\r\n    <div class=\"bidOpeningHall\">\r\n      <el-card id=\"main\" class=\"box-card\">\r\n        <div style=\"height: 10px;\">\r\n        <div style=\"padding: 5px 0 0 20px; float: left;\">平台当前时间：<span >{{ currentTime }}</span></div>\r\n        <div style=\"padding: 5px 20px 0 0; float: right;\">\r\n          连接状态：<span :style=\"`color:${isLink ? 'green' : 'red'}`\">{{\r\n            isLink ? \"已连接\" : \"已断连，请刷新重连\"\r\n          }}</span>\r\n        </div></div>\r\n        <Sign ref=\"signIn\" v-if=\"shouldRenderSign\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" @sendMessage=\"operateSend\"></Sign>\r\n        <ready ref=\"ready\" v-if=\"node == 'ready' && projectInfo\" :projectInfo=\"projectInfo\"></ready>\r\n        <publicity ref=\"publicity\" v-if=\"node == 'publicity'\"></publicity>\r\n        <decryption ref=\"decryption\" v-if=\"node == 'decryption' && projectInfo\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" :deadline=\"decryptionDeadline\" @sendMessage=\"operateSend\"></decryption>\r\n        <bidAnnouncement ref=\"bidAnnouncement\" v-if=\"node == 'bidAnnouncement'\"></bidAnnouncement>\r\n        <end ref=\"end\" v-if=\"node == 'end'\" @sendData=\"handleData\"></end>\r\n\r\n      </el-card>\r\n      <el-card class=\"box-card\" style=\"width: 15%;\">\r\n        <div class=\"im\">\r\n          <div class=\"im-title\">{{ userInfo.ent?userInfo.ent.entName :  ''}}</div>\r\n          <div ref=\"messagesContainer\" class=\"im-content\" :style=\"{height: syncedHeight }\">\r\n            <div v-for=\"(itemc, indexc) in recordContent\" :key=\"indexc\">\r\n              <div class=\"sysMessage\" v-if=\"itemc.type == 0\">\r\n              </div>\r\n              <div v-else>\r\n                <div class=\"word\" v-if=\"itemc.sendId !== userInfo.entId\">\r\n                  <div class=\"info\">\r\n                    <div class=\"message_time\">\r\n                      {{anonymous? \"*******\":itemc.sendName }}\r\n                    </div>\r\n                    <div class=\"info-content\">{{ itemc.content }}</div>\r\n                    <div class=\"message_time\">\r\n                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"word-my\" v-else>\r\n                  <div class=\"info\">\r\n                    <div class=\"info-content\">{{ itemc.content }}</div>\r\n                    <div class=\"Sender_time\">\r\n                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"im-operation\">\r\n            <div style=\"margin-right:5px\">\r\n              <el-input v-model=\"message\" placeholder=\"输入内容\"></el-input>\r\n            </div>\r\n            <el-button style=\"height: 36px;background: #176ADB;color:#fff\" @click=\"send\">发送</el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <Foot></Foot>\r\n\r\n    <el-dialog :visible.sync=\"secondQuoteVisible\" width=\"60%\">\r\n      <!-- 显示倒计时 -->\r\n      <el-table :data=\"quoteList\" border style=\"width: 100%;\" :cell-style=\"cellStyle\" :header-cell-style=\"headStyle\">\r\n        <el-table-column label=\"二次报价\">\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"100\">\r\n          </el-table-column>\r\n          <el-table-column label=\"历史报价\" prop=\"quoteAmount\" align=\"center\">\r\n          </el-table-column>\r\n          <el-table-column label=\"报价大写\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatAmount(scope.row.quoteAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"报价时间\" prop=\"createTime\" align=\"center\">\r\n          </el-table-column>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"decryption-countdown\">\r\n        <el-statistic @finish=\"finishSecondQuote\" format=\"二次报价结束倒计时：HH小时mm分ss秒\" :value=\"countdown\" time-indices>\r\n        </el-statistic>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"90px\">\r\n          <el-col :span=\"6\" :offset=\"3\">\r\n            <el-form-item label=\"报价金额：\">\r\n              <el-input v-model=\"form.quoteAmount\" placeholder=\"请输入报价金额\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"大写金额：\">\r\n              <el-input :disabled=\"true\" :value=\"returnConvertToChineseCurrency(form.quoteAmount)\" placeholder=\"请输入报价金额大写\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n        <el-col :span=\"5\">\r\n          <div style=\"display:flex;justify-content: center;align-items: center;\">\r\n            <el-button class=\"quote-button\" :disabled=\"isCountdownEnded\"  @click=\"submitQuote\">确 定</el-button>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Sign from \"./supplierComponent/sign.vue\";\r\nimport Ready from \"./supplierComponent/ready.vue\";\r\nimport publicity from \"./supplierComponent/publicity.vue\";\r\nimport decryption from \"./supplierComponent/decryption.vue\";\r\nimport bidAnnouncement from \"./supplierComponent/bidAnnouncement.vue\";\r\nimport end from \"./supplierComponent/end.vue\";\r\n\r\nimport {\r\n  formatDateOption,\r\n  getTodayStartWithDate,\r\n  getTodayEndWithDate,\r\n} from \"@/utils/index\";\r\nimport { bidInfo, chatHistory } from \"@/api/onlineBidOpening/info\";\r\nimport { getUserProfile } from \"@/api/system/user\";\r\nimport { convertToChineseCurrency } from \"@/utils/amount\";\r\nimport { listInfo } from \"@/api/evaluation/info\";\r\nimport { listQuote, addQuote } from \"@/api/again/quote\";\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { approvalProcess,inquiringBidList } from \"@/api/expert/review\";\r\n\r\nconst baseUrl = process.env.VUE_APP_BASE_API;\r\nconst socketUrl = baseUrl.replace(\"http\", \"ws\").replace(\"/prod-api\", \"\");\r\nexport default {\r\n  components: { Sign, Ready, publicity, decryption, bidAnnouncement, end },\r\n  data() {\r\n    return {\r\n      node: \"sign\",\r\n      userInfo: {},\r\n      projectInfo: null,\r\n\r\n      // 二次报价表单\r\n      form: {\r\n        quoteAmount: \"\",\r\n        projectEvaluationId: \"\",\r\n        quoteAmountStr: \"\",\r\n      },\r\n      // 二次报价金额\r\n      rules: {\r\n        quoteAmount: [\r\n          { required: true, message: \"请输入金额\", trigger: \"blur\" },\r\n        ],\r\n        quoteAmountStr: [\r\n          { required: true, message: \"请输入金额大写\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      progress: [\r\n        {\r\n          itemName: \"资格性评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"符合性评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"技术标评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"商务标评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"投标报价打分\",\r\n          status: 0,\r\n        },\r\n      ],\r\n      // 二次报价历史报价\r\n      quoteList: [],\r\n      // 显示二次报价弹框\r\n      secondQuoteVisible: false,\r\n\r\n      decryptionDeadline: \"\",\r\n\r\n      url: \"\",\r\n      message: \"\",\r\n      text_content: \"\",\r\n      ws: null,\r\n\r\n      recordContent: [],\r\n      evaluationInfo: {},\r\n      timer: null, // 存储定时器引用\r\n      isCountdownEnded: false,\r\n      // 专家信息\r\n      expertList: {},\r\n      intervalId: null,\r\n      countdown: \"\",\r\n      num: 0,\r\n      isLink: false,\r\n      anonymous: true,\r\n      syncedHeight: '450px', // 初始高度\r\n      currentTime:null\r\n\r\n    };\r\n  },\r\n  computed: {\r\n    shouldRenderSign() {\r\n      return (\r\n        this.node === \"signIn\" &&\r\n        this.projectInfo &&\r\n        this.userInfo &&\r\n        Object.keys(this.userInfo).length > 0\r\n      );\r\n    },\r\n  },\r\n  watch: {\r\n    node: {\r\n      handler() {\r\n        setTimeout(() => {\r\n          var element = document.getElementById('main');\r\n          console.log('element.clientHeight', element.offsetHeight);\r\n          this.syncedHeight = element.offsetHeight - 120 + 'px'\r\n        }, 10);\r\n\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    finishSecondQuote(isOk){\r\n      // this.isCountdownEnded = isOk;\r\n    },\r\n    // 初始化\r\n    init() {\r\n      // 获取开标项目信息\r\n      const promise1 = bidInfo({\r\n        bidOpeningTime: getTodayStartWithDate(),\r\n        bidOpeningEndTime: getTodayEndWithDate(),\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.projectInfo = response.data;\r\n        } else {\r\n          this.$modal.msgwarning(response.msg);\r\n        }\r\n      });\r\n      // 获取用户信息\r\n      const promise2 = getUserProfile().then((response) => {\r\n        this.userInfo = response.data;\r\n        localStorage.setItem(\"userInfo\", JSON.stringify(this.userInfo));\r\n      });\r\n      // 获取项目评审信息\r\n      const promise3 = listInfo({\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          if (response.rows && response.rows.length > 0) {\r\n            this.evaluationInfo = response.rows[response.rows.length - 1];\r\n          } else {\r\n            this.evaluationInfo = {};\r\n            // this.$message.error(\"未查询到项目评审信息\");\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg);\r\n        }\r\n      });\r\n      // 获取专家信息\r\n      const promise4 = expertInfoById({\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.expertList = response.data;\r\n          localStorage.setItem(\"expertList\", JSON.stringify(this.expertList));\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n\r\n      Promise.all([promise1, promise2, promise3, promise4]).then((result) => {\r\n        this.join();\r\n        if (this.node == \"end\") {\r\n          this.$refs.end.getExpertReviewProgress();\r\n        }\r\n      });\r\n    },\r\n    // 获取当前开标室流程\r\n    handleStatus(data) {\r\n      this.anonymous = this.$store.getters.supplierBidOpenStatus >= 2 ? false : true;\r\n      switch (data) {\r\n        case \"签到\":\r\n          this.node = \"signIn\";\r\n          break;\r\n        case \"开标准备\":\r\n          this.node = \"ready\";\r\n          break;\r\n        case \"投标人公示\":\r\n          this.node = \"publicity\";\r\n          break;\r\n        case \"标书解密\":\r\n          this.node = \"decryption\";\r\n          break;\r\n        case \"唱标\":\r\n          this.node = \"bidAnnouncement\";\r\n          break;\r\n        case \"开标结束\":\r\n          this.node = \"end\";\r\n          break;\r\n      }\r\n    },\r\n    // 节点更新通知\r\n    updateStatus() {\r\n      this.$refs.head.getBidStatus();\r\n    },\r\n\r\n    // 转换大写的报价金额\r\n    returnConvertToChineseCurrency(money) {\r\n      return convertToChineseCurrency(money);\r\n    },\r\n    // 提交报价\r\n    submitQuote() {\r\n      if (this.isCountdownEnded) {\r\n        this.$message.warning(\"倒计时结束，禁止报价\");\r\n        this.secondQuoteVisible = false;\r\n        return;\r\n      }\r\n      var previousPrice = 0;\r\n\r\n      if(this.quoteList.length===0){\r\n        previousPrice = this.projectInfo.project.budgetAmount;\r\n      }else{\r\n        previousPrice = this.quoteList[this.quoteList.length-1].quoteAmount;\r\n      }\r\n      if(this.form.quoteAmount>previousPrice){\r\n        this.$confirm(\"本次报价超过上次报价 \"+ previousPrice + \" 元，是否继续提交？\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          // 用户确认后执行提交报价\r\n          this.doSubmitQuote();\r\n        }).catch(() => {\r\n          // 用户取消，不执行任何操作\r\n          return;\r\n        });\r\n        return;\r\n      }\r\n      //提交报价\r\n      this.doSubmitQuote();\r\n    },\r\n    doSubmitQuote() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          if (this.evaluationInfo.projectEvaluationId == null) {\r\n            listInfo({\r\n              projectId: this.$route.query.projectId,\r\n            }).then((response) => {\r\n              if (response.code === 200) {\r\n                if (response.rows && response.rows.length > 0) {\r\n                  this.evaluationInfo = response.rows[response.rows.length - 1];\r\n                  this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;\r\n                } else {\r\n                  this.$message.error(\"没有评审信息！\");\r\n                  return\r\n                }\r\n              } else {\r\n                this.$message.success(response.msg);\r\n              }\r\n            });\r\n          } else {\r\n            this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;\r\n          }\r\n          addQuote(this.form)\r\n            .then((result) => {\r\n              if (result.code === 200) {\r\n                this.$message.success(result.msg);\r\n                this.getQuoteList();\r\n                this.secondQuoteVisible = false;\r\n              } else {\r\n                this.$message.error(result.msg);\r\n              }\r\n            })\r\n            .catch((err) => { });\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n      // 关闭弹窗时，清除定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n    },\r\n    // 转换大写的报价金额\r\n    formatAmount(amount) {\r\n      return convertToChineseCurrency(amount);\r\n    },\r\n\r\n    // 格式化开标时间显示\r\n    formatBidOpeningTime(time) {\r\n      return formatDateOption(time, \"date\");\r\n    },\r\n    // 格式化开标时间显示 时-分-秒\r\n    formatBidOpeningTimeTwo(time) {\r\n      return formatDateOption(time, \"time\");\r\n    },\r\n    // 获取历史报价\r\n    getQuoteList() {\r\n      const queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 999,\r\n        params: {\r\n          projectId: this.$route.query.projectId,\r\n          entId: this.userInfo.entId,\r\n        },\r\n      };\r\n      listQuote(queryParams)\r\n        .then((result) => {\r\n          //存入数据\r\n          this.quoteList = result.rows;\r\n          console.log(this.quoteList.length + 1 == this.num);\r\n          if (this.quoteList.length + 1 == this.num) {\r\n            this.secondQuoteVisible = true;\r\n            // this.isCountdownEnded = true;\r\n          } else {\r\n            // this.isCountdownEnded = false;\r\n            this.secondQuoteVisible = false;\r\n          }\r\n        })\r\n        .catch((err) => { });\r\n    },\r\n    getExpertReviewProgress() {\r\n      approvalProcess(this.$route.query.projectId, this.expertList[0].resultId).then(\r\n        (response) => {\r\n          if (response.code == 200 && response.data.scoringMethodUinfo.scoringMethodItems) {\r\n            \r\n            const existingItemNames = response.data.scoringMethodUinfo.scoringMethodItems.map(item => item.itemName);\r\n            this.progress = this.progress.filter(item => existingItemNames.includes(item.itemName));\r\n\r\n            const evalProjectEvaluationProcess = this.progress.find((item) => {\r\n              return item.itemName == \"投标报价打分\";\r\n            }).evalProjectEvaluationProcess;\r\n            if (evalProjectEvaluationProcess) {\r\n              let startTime = new Date(\r\n                evalProjectEvaluationProcess.startTime.replace(\" \", \"T\") + \"Z\"\r\n              );\r\n              // 将 Date 对象加上30秒\r\n              startTime.setMinutes(startTime.getMinutes() + evalProjectEvaluationProcess.minutes);\r\n              var endTime = startTime\r\n                .toISOString()\r\n                .replace(\"T\", \" \")\r\n                .replace(\"Z\", \"\")\r\n                .split(\".\")[0];\r\n              // 将更新后的时间转换回字符串格式\r\n              this.num = evalProjectEvaluationProcess.num;\r\n              this.handleData({\r\n                startTime: evalProjectEvaluationProcess.startTime,\r\n                endTime: endTime,\r\n                num: this.num,\r\n                isAbandonedBid: 0\r\n              });\r\n            }\r\n          } else {\r\n\r\n          }\r\n        }\r\n      );\r\n\r\n    },\r\n    // 判断是否打开二次报价弹窗\r\n    handleData({ startTime, endTime, num, isAbandonedBid }) {\r\n      if (this.secondQuoteVisible == true) {\r\n        return;\r\n      } else {\r\n        const now = new Date(); // 当前时间\r\n        const start = new Date(startTime); // 开始时间\r\n        const end = new Date(endTime); // 结束时间\r\n        console.log(\"isAbandonedBid\",isAbandonedBid);\r\n        \r\n        if (now >= start && now <= end && isAbandonedBid==0) {\r\n          this.$nextTick(() => {\r\n            this.countdown = new Date(endTime);\r\n          });\r\n          this.num = num;\r\n          console.log(\"this.num\", this.num);\r\n          this.form = {};\r\n          this.getQuoteList();\r\n        } else {\r\n          this.secondQuoteVisible = false;\r\n        }\r\n      }\r\n    },\r\n    // 连接websocket\r\n    join() {\r\n      // this.url = `${socketUrl}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n      this.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n      const wsurl = this.url;\r\n      this.ws = new WebSocket(wsurl);\r\n      const self = this;\r\n      // 心跳检测函数\r\n      const ws_heartCheck = {\r\n        timeout: 5000, // 5秒\r\n        timeoutObj: null,\r\n        serverTimeoutObj: null,\r\n        start: function () {\r\n          this.timeoutObj = setTimeout(() => {\r\n            // 这里发送一个心跳包\r\n            self.ws.send(\"ping\");\r\n            this.serverTimeoutObj = setTimeout(() => {\r\n              self.ws.close(); // 如果超过一定时间还没重置，视为断开连接\r\n            }, this.timeout);\r\n          }, this.timeout);\r\n        },\r\n        reset: function () {\r\n          clearTimeout(this.timeoutObj);\r\n          clearTimeout(this.serverTimeoutObj);\r\n          this.start();\r\n        },\r\n        stop: function () {\r\n          clearTimeout(this.timeoutObj);\r\n          clearTimeout(this.serverTimeoutObj);\r\n        }\r\n      };\r\n      this.ws.onopen = function (event) {\r\n        ws_heartCheck.start();\r\n        self.text_content = self.text_content + \"已经打开开标室连接!\" + \"\\n\";\r\n        self.isLink = true;\r\n\r\n        console.log(self.text_content);\r\n      };\r\n      this.ws.onmessage = function (event) {\r\n        console.log(event.data);\r\n        if(event.data == \"ping\"){\r\n          // 心跳响应\r\n          ws_heartCheck.reset();\r\n        }else if (event.data == \"连接成功\") {\r\n          console.log(\"socketUrl\", socketUrl);\r\n        } else if (event.data == \"signIn\") {\r\n          self.updateStatus();\r\n        } else if (event.data == \"bidPublicity\") {\r\n          self.updateStatus();\r\n        } else if (event.data.includes(\"decryption\")) {\r\n          self.updateStatus();\r\n        } else if (event.data == \"supDecrytion\") {\r\n          self.$refs.decryption.initdataList();\r\n        } else if (event.data == \"nextStep\") {\r\n          // 不做任何操作\r\n        } else if (\r\n          event.data == \"bidAnnouncement\" ||\r\n          event.data == \"flowLabel\"\r\n        ) {\r\n          self.updateStatus();\r\n        } else if (event.data == \"end\") {\r\n          self.updateStatus();\r\n        } else if (event.data == \"evalAgainQuote\") {\r\n          self.getExpertReviewProgress();\r\n          // //打开二次报价弹窗\r\n          // self.form = {};\r\n          // //加载报价记录\r\n          // self.getQuoteList();\r\n        } else {\r\n          self.initChat();\r\n        }\r\n      };\r\n      this.ws.onclose = function (event) {\r\n        self.text_content = self.text_content + \"已经关闭开标室连接!\" + \"\\n\";\r\n        self.isLink = false;\r\n        console.log(self.text_content);\r\n        //断开后自动重连\r\n        ws_heartCheck.stop();\r\n        self.join();\r\n      };\r\n    },\r\n    // 断开websocket连接\r\n    exit() {\r\n      if (this.ws) {\r\n        this.ws.close();\r\n        this.ws = null;\r\n      }\r\n    },\r\n    // 发送消息\r\n    send() {\r\n      if (this.ws) {\r\n        this.ws.send(this.message);\r\n        this.message = \"\";\r\n        this.scrollToBottom();\r\n      } else {\r\n        alert(\"未连接到开标室服务器\");\r\n      }\r\n    },\r\n    // 发送消息\r\n    operateSend(message) {\r\n      if (this.ws) {\r\n        this.ws.send(message);\r\n      } else {\r\n        alert(\"未连接到开标室服务器\");\r\n      }\r\n    },\r\n    // 初始化聊天记录\r\n    initChat() {\r\n      chatHistory(this.$route.query.projectId).then((response) => {\r\n        this.recordContent = response.data;\r\n      });\r\n    },\r\n    // 处理滚动\r\n    scrollToBottom() {\r\n      this.$nextTick(() => {\r\n        const container = this.$refs.messagesContainer;\r\n        container.scrollTop = container.scrollHeight;\r\n      });\r\n    },\r\n    // 表格头颜色\r\n    headStyle({ row, column, rowIndex, columnIndex }) {\r\n      console.log(row, column, rowIndex, columnIndex);\r\n      if (rowIndex === 0 && columnIndex === 0) {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          background: \"#1C57A7\",\r\n          color: \"#fff\",\r\n          \"font-size\": \"16px\",\r\n          \"font-weight\": \"700\",\r\n        };\r\n      } else {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          background: \"#176ADB\",\r\n          color: \"#fff\",\r\n          \"font-size\": \"16px\",\r\n          \"font-weight\": \"700\",\r\n          border: \"0px\",\r\n        };\r\n      }\r\n    },\r\n    // 表格样式\r\n    cellStyle({ row, column, rowIndex, columnIndex }) {\r\n      if (rowIndex % 2 === 0) {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          \"font-weight\": \"700\",\r\n          color: \"#000\",\r\n          \"font-size\": \"14px\",\r\n          background: \"#FFFFFF\",\r\n        };\r\n      } else {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          \"font-weight\": \"700\",\r\n          color: \"#000\",\r\n          \"font-size\": \"14px\",\r\n          background: \"#F5F5F5\",\r\n        };\r\n      }\r\n    },\r\n    startProgressInterval() {\r\n      this.intervalId = setInterval(() => {\r\n        if (this.node == \"end\") {\r\n          this.$refs.end.getExpertReviewProgress();\r\n          //在这里增加 供应商消息刷新页面接口\r\n\r\n        }\r\n      }, 5000); // 每隔 5 秒调用一次\r\n    },\r\n    stopProgressInterval() {\r\n      clearInterval(this.intervalId);\r\n    },\r\n    updateTime() {\r\n      var ct = new Date();\r\n      var _this = this;\r\n\r\n      setInterval(function(){\r\n        ct.setSeconds(ct.getSeconds() + 1);\r\n        _this.currentTime = formatDateOption(ct, \"cdatetime\");\r\n      }, 1000); // 每秒更新时间\r\n    }\r\n  },\r\n  created() { },\r\n  mounted() {\r\n    this.init();\r\n    this.initChat();\r\n    // 在组件挂载后启动定时器\r\n    this.startProgressInterval();\r\n    this.updateTime();\r\n  },\r\n  updated() {\r\n    this.scrollToBottom();\r\n  },\r\n  beforeDestroy() {\r\n    // 在组件销毁前清除定时器\r\n    this.stopProgressInterval();\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .el-card__body {\r\n  padding: 0;\r\n}\r\n</style>\r\n\r\n<style scoped lang=\"scss\">\r\n.bidOpeningHall {\r\n  position: relative;\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  justify-content: center;\r\n  align-content: flex-start;\r\n  align-items: flex-start;\r\n}\r\n.box-card {\r\n  min-height: 600px;\r\n  width: 50%;\r\n  margin: 15px 5px;\r\n}\r\n.im {\r\n  .im-title {\r\n    width: 100%;\r\n    height: 50px;\r\n    background: #176adb;\r\n\r\n    font-weight: 500;\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    letter-spacing: 0;\r\n\r\n    line-height: 50px;\r\n    text-align: center;\r\n  }\r\n  .im-content {\r\n    margin: 10px;\r\n    background: #f5f5f5;\r\n    // height: 450px;\r\n    overflow-y: auto;\r\n  }\r\n  .im-operation {\r\n    display: flex;\r\n    margin: 0 10px;\r\n    margin-bottom: 10px;\r\n    overflow: auto;\r\n  }\r\n}\r\n.im-content {\r\n  .word {\r\n    display: flex;\r\n    margin-bottom: 30px;\r\n\r\n    img {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n    }\r\n    .info {\r\n      width: 47%;\r\n      margin-left: 10px;\r\n      .Sender_time {\r\n        padding-right: 12px;\r\n        padding-top: 5px;\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n      }\r\n      .message_time {\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n        line-height: 20px;\r\n        margin-top: -5px;\r\n        margin-top: 5px;\r\n      }\r\n      .info-content {\r\n        word-break: break-all;\r\n        // max-width: 45%;\r\n        display: inline-block;\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        background: #fff;\r\n        position: relative;\r\n        margin-top: 8px;\r\n        background: #dbdbdb;\r\n        border-radius: 4px;\r\n      }\r\n      //小三角形\r\n      .info-content::before {\r\n        position: absolute;\r\n        left: -8px;\r\n        top: 8px;\r\n        content: \"\";\r\n        border-right: 10px solid #dbdbdb;\r\n        border-top: 8px solid transparent;\r\n        border-bottom: 8px solid transparent;\r\n      }\r\n    }\r\n  }\r\n\r\n  .word-my {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    // margin-bottom: 30px;\r\n    img {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n    }\r\n    .info {\r\n      width: 90%;\r\n      // margin-left: 10px;\r\n      text-align: right;\r\n      // position: relative;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      flex-wrap: wrap;\r\n      flex-direction: column;\r\n      .info-content {\r\n        word-break: break-all;\r\n        max-width: 45%;\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        // float: right;\r\n        margin-right: 10px;\r\n        position: relative;\r\n        margin-top: 8px;\r\n        background: #a3c3f6;\r\n        text-align: left;\r\n        border-radius: 4px;\r\n      }\r\n      .Sender_time {\r\n        padding-right: 12px;\r\n        padding-top: 5px;\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n      }\r\n      //小三角形\r\n      .info-content::after {\r\n        position: absolute;\r\n        right: -8px;\r\n        top: 8px;\r\n        content: \"\";\r\n        border-left: 10px solid #a3c3f6;\r\n        border-top: 8px solid transparent;\r\n        border-bottom: 8px solid transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n.countdown-timer {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #fff;\r\n  margin: 30px 0;\r\n\r\n  width: 30%;\r\n  height: 70px;\r\n\r\n  background: #176adb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.quote-button {\r\n  width: 100px;\r\n  background: #176adb;\r\n\r\n  color: #fff;\r\n  font-weight: 700;\r\n\r\n  border-radius: 5px;\r\n}\r\n.exper-title {\r\n  height: 45px;\r\n  background: #176adb;\r\n  display: flex;\r\n  justify-content: center;\r\n\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  letter-spacing: 0;\r\n  align-items: center;\r\n}\r\n.expert-title-second {\r\n  height: 40px;\r\n  background: #1c57a7;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n.decryption-countdown {\r\n  width: 50%;\r\n  height: 105px;\r\n  margin: 10px 25%;\r\n\r\n  // background: #176adb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  ::v-deep .number {\r\n    color: #000;\r\n    font-weight: 700;\r\n  }\r\n}\r\n</style>\r\n"]}]}