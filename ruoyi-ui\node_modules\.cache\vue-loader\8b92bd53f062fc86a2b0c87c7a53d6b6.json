{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\one.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\one.vue", "mtime": 1753952311943}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KCXN1cHBsaWVySW5mbywNCglhcHByb3ZhbFByb2Nlc3MsDQoJc2NvcmluZ0ZhY3RvcnMsDQoJY2hlY2tSZXZpZXdTdW1tYXJ5LA0KCWZpbGVzQnlJZCwNCn0gZnJvbSAiQC9hcGkvZXhwZXJ0L3JldmlldyI7DQppbXBvcnQgeyBnZXREZXRhaWxCeVBzeHggfSBmcm9tICJAL2FwaS9ldmFsdWF0aW9uL2RldGFpbC8iOw0KaW1wb3J0IHsgZWRpdEV2YWxFeHBlcnRTY29yZUluZm8gfSBmcm9tICJAL2FwaS9ldmFsdWF0aW9uL2V4cGVydFN0YXR1cyI7DQppbXBvcnQgeyByZXNEb2NSZXZpZXdGYWN0b3JzRGVjaXNpb24gfSBmcm9tICJAL2FwaS9kb2NSZXNwb25zZS9lbnRJbmZvIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KCWRhdGEoKSB7DQoJCXJldHVybiB7DQoJCQlzdXBwbGllck9wdGlvbnM6IFtdLCAvLyDkvpvlupTllYbkuIvmi4npgInpobkNCgkJCXNjb3JpbmdTeXN0ZW06IFtdLCAvLyDlvZPliY3or4TliIbkvZPns7sNCgkJCXNlbGVjdGVkRmFjdG9yTm9kZToge30sIC8vIOW9k+WJjemAieS4reivhOWIhumhuQ0KCQkJc2VsZWN0ZWRTdXBwbGllck5hbWU6ICIiLCAvLyDlvZPliY3pgInkuK3kvpvlupTllYblkI3np7ANCgkJCXNlbGVjdGVkU3VwcGxpZXI6IHt9LCAvLyDlvZPliY3pgInkuK3kvpvlupTllYblr7nosaENCgkJCWV4cGVydEluZm86IHt9LCAvLyDkuJPlrrbkv6Hmga8NCgkJCXJhdGluZ1N0YXRlTWFwOiB7fSwgLy8g6K+E5YiG6aG554q25oCB5LiO5Y6f5ZugDQoJCQlmaWxlSW5mbzoge30sIC8vIOaWh+S7tuS/oeaBrw0KCQkJaXNSZXNwb25zZVZpc2libGU6IGZhbHNlLCAvLyDmmK/lkKbmmL7npLrlk43lupTmlofku7YNCgkJCWlzUHJvY3VyZW1lbnRWaXNpYmxlOiBmYWxzZSwgLy8g5piv5ZCm5pi+56S66YeH6LSt5paH5Lu2DQoJCQlpc0RvdWJsZVZpZXc6IGZhbHNlLCAvLyDmmK/lkKblr7nmr5TmmL7npLoNCgkJCQ0KCQkJZW50RG9jUmVzcG9uc2VQYWdlOiB7fSwgLy8g5ZON5bqU5paH5Lu26aG156CB5L+h5oGvDQoJCQkNCgkJCWZhY3RvckRldGFpbExpc3Q6IFtdLCAvLyDor4TliIbpobnor6bmg4UNCgkJCWZhY3RvcnNQYWdlTWFwOiB7fSwgLy8g6K+E5YiG6aG56aG156CB5L+h5oGvDQoJCQkNCgkJCWVudERvY1Byb2N1cmVtZW50UGFnZToge30sIC8vIOmHh+i0reaWh+S7tumhteeggeS/oeaBrw0KCQkJcGFnZVByb2N1cmVtZW50OltdLCAvLyDph4fotK3mlofku7bnmoTor4TliIbpobkNCgkJCWF0dGFjaG1lbnRzTGlzdDogW10sIC8vIOaWh+S7tuWIl+ihqA0KCQkJDQoJCQlzdXBwbGllckZhY3RvclBhZ2VNYXA6IHt9LCAvLyDlvZPliY3kvpvlupTllYbor4TliIbpobnpobXnoIENCgkJCXJlc3BvbnNlUGRmVXJsOiBudWxsLCAvLyDlk43lupTmlofku7ZQREbot6/lvoQNCgkJCXByb2N1cmVtZW50UGRmVXJsOiBudWxsLCAvLyDph4fotK3mlofku7ZQREbot6/lvoQNCg0KCQkJLy8g5oyJ6ZKu54q25oCB566h55CGDQoJCQlhY3RpdmVCdXR0b246ICdyZXNwb25zZScsIC8vIOW9k+WJjea/gOa0u+eahOaMiemSru+8midyZXNwb25zZSfjgIEncHJvY3VyZW1lbnQn44CBJ2NvbnRyYXN0Jw0KDQoJCQkvLyBQREbmuLLmn5PnirbmgIHnrqHnkIYNCgkJCXJlc3BvbnNlUGRmUmVuZGVyZWQ6IGZhbHNlLCAvLyDlk43lupTmlofku7ZQREbmmK/lkKbmuLLmn5PlrozmiJANCgkJCXByb2N1cmVtZW50UGRmUmVuZGVyZWQ6IGZhbHNlLCAvLyDph4fotK3mlofku7ZQREbmmK/lkKbmuLLmn5PlrozmiJANCg0KCQkJaGVscEltYWdlTGlzdDogWyIvZXZhbHV0aW9uL2hlbHAuanBnIl0sIC8vIOatpemqpOWbvueJhw0KCQkJZmFjdG9yS2V5TWFwOiB7IC8vIOivhOWIhumhueS4juWQjuerr+Wtl+auteaYoOWwhA0KCQkJCSLnibnlrprotYTmoLzopoHmsYIiOiAiemd6cyIsDQoJCQkJIuWTjeW6lOWGheWuuSI6ICJqc3BsYiIsDQoJCQkJIumHh+i0remcgOaxgiI6ICJqc3BsYiIsDQoJCQkJIuS+m+i0p+acn+mZkCI6ICJnaHF4IiwNCgkJCQki5oqV5qCH5oql5Lu3IjogInRiYmoiDQoJCQl9LA0KCQkJY2hlY2tSZXN1bHQ6IHt9LCAvLyDns7vnu5/liJ3pqoznu5PmnpwNCgkJCWNoZWNrUmVzdWx0TGFiZWw6IHsgLy8g57O757uf5Yid6aqM57uT5p6c5ZCN56ewDQoJCQkJIuespuWQiOOAiuS4reWNjuS6uuawkeWFseWSjOWbveaUv+W6nOmHh+i0reazleOAi+esrOS6jOWNgeS6jOadoeinhOWumiI6ICLns7vnu5/liJ3pqozpgJrov4ciLA0KCQkJCSLnibnlrprotYTmoLzopoHmsYIiOiAi57O757uf5Yid6aqM6YCa6L+HIiwNCgkJCQki5L+h55So5p+l6K+iIjogIuezu+e7n+WInemqjOmAmui/hyIsDQoJCQkJIuWTjeW6lOS6uuWQjeensCI6ICLns7vnu5/liJ3pqozpgJrov4ciLA0KCQkJCSLlk43lupTlhoXlrrkiOiAi57O757uf5Yid6aqM6YCa6L+HIiwNCgkJCQki6YeH6LSt6ZyA5rGCIjogIuezu+e7n+WInemqjOmAmui/hyIsDQoJCQkJIuS+m+i0p+acn+mZkCI6ICLns7vnu5/liJ3pqozpgJrov4ciLA0KCQkJCSLmipXmoIfmiqXku7ciOiAi57O757uf5Yid6aqM6YCa6L+HIg0KCQkJfSwNCg0KCQkJLy8g5oKs5YGc54q25oCB566h55CGDQoJCQlob3ZlcmVkRmFjdG9yTm9kZTogbnVsbCwgLy8g5oKs5YGc5pe255qE6K+E5YiG6aG5DQoJCQl0b29sdGlwVGltZXI6IG51bGwsIC8vIOaCrOa1ruahhuaYvuekuuWumuaXtuWZqA0KCQl9Ow0KCX0sDQoNCgltZXRob2RzOiB7DQoJCS8vID09PT09PT09PT0g6K+E5YiG55u45YWzID09PT09PT09PT0NCgkJLyoqDQoJCSAqIOezu+e7n+WInemqjOe7k+aenOWIpOaWrQ0KCQkgKiBAcGFyYW0ge3N0cmluZ30gZmFjdG9yTmFtZSDor4TliIbpobnlkI3np7ANCgkJICogQHJldHVybnMge3N0cmluZ30gMS3pgJrov4cgMC3mnKrpgJrov4cNCgkJICovDQoJCWdldENoZWNrUmVzdWx0U3RhdGUoZmFjdG9yTmFtZSkgew0KCQkJaWYgKCF0aGlzLmNoZWNrUmVzdWx0IHx8IE9iamVjdC5rZXlzKHRoaXMuY2hlY2tSZXN1bHQpLmxlbmd0aCA9PT0gMCkgcmV0dXJuICIiOyAvLyDlpoLmnpzmsqHmnInns7vnu5/liJ3pqoznu5PmnpzvvIzliJnov5Tlm57nqboNCgkJCWxldCBzdGF0ZSA9ICIxIjsNCgkJCWNvbnN0IGtleSA9IHRoaXMuZmFjdG9yS2V5TWFwW2ZhY3Rvck5hbWVdOw0KCQkJaWYgKGtleSkgew0KCQkJCXN0YXRlID0gdGhpcy5jaGVja1Jlc3VsdFtrZXldOw0KCQkJCWlmIChmYWN0b3JOYW1lID09PSAi5oqV5qCH5oql5Lu3IiAmJiBzdGF0ZSA9PT0gIjEiKSB7DQoJCQkJCXN0YXRlID0gdGhpcy5jaGVja1Jlc3VsdFsibXhiamIiXTsNCgkJCQl9DQoJCQl9DQoJCQlpZiAoc3RhdGUgPT09ICIwIikgew0KCQkJCXRoaXMuY2hlY2tSZXN1bHRMYWJlbFtmYWN0b3JOYW1lXSA9ICLns7vnu5/liJ3pqozmnKrpgJrov4ciOw0KCQkJfSBlbHNlIHsNCgkJCQlzdGF0ZSA9ICIxIjsNCgkJCQl0aGlzLmNoZWNrUmVzdWx0TGFiZWxbZmFjdG9yTmFtZV0gPSAi57O757uf5Yid6aqM6YCa6L+HIjsNCgkJCX0NCgkJCXJldHVybiBzdGF0ZTsNCgkJfSwNCg0KCQkvKioNCgkJICog5qCh6aqM5omA5pyJ6K+E5YiG6aG55piv5ZCm5aGr5YaZ5a6M5pW0DQoJCSAqIEByZXR1cm5zIHtib29sZWFufSDmmK/lkKblhajpg6jloavlhpkNCgkJICovDQoJCXZhbGlkYXRlQWxsUmF0aW5ncygpIHsNCgkJCWZvciAoY29uc3QgaXRlbSBvZiB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zKSB7DQoJCQkJY29uc3Qgc3RhdGUgPSB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5zdGF0ZTsNCgkJCQljb25zdCByZWFzb24gPSB0aGlzLnJhdGluZ1N0YXRlTWFwW2l0ZW0uZW50TWV0aG9kSXRlbUlkXS5yZWFzb247DQoJCQkJLy8g6K+E5YiG57uT5p6c5pyq5aGr5YaZDQoJCQkJaWYgKHN0YXRlID09PSBudWxsIHx8IHN0YXRlID09PSAnJykgew0KCQkJCQkvLyB0aGlzLiRtZXNzYWdlLndhcm5pbmcoYOivt+Whq+WGmeivhOWIhumhue+8miR7aXRlbS5pdGVtTmFtZX0g55qE6K+E5YiG57uT5p6cYCk7DQoJCQkJCXJldHVybiB0cnVlOw0KCQkJCX0NCgkJCQkvLyDkuI3pgJrov4fkvYbmnKrloavlhpnljp/lm6AgLSDlsIbor4TlrqHpobnorr7nva7kuLrnqbrvvIznhLblkI7nu6fnu63miafooYzlkI7nu63mtYHnqIsNCgkJCQlpZiAoc3RhdGUgPT09ICIwIiAmJiAoIXJlYXNvbiB8fCByZWFzb24udHJpbSgpID09PSAnJykpIHsNCgkJCQkJLy8g5bCG5q2k6K+E5a6h6aG56K6+572u5Li656m677yI5pyq6K+E5a6h54q25oCB77yJDQoJCQkJCXRoaXMucmF0aW5nU3RhdGVNYXBbaXRlbS5lbnRNZXRob2RJdGVtSWRdLnN0YXRlID0gbnVsbDsNCgkJCQkJdGhpcy5yYXRpbmdTdGF0ZU1hcFtpdGVtLmVudE1ldGhvZEl0ZW1JZF0ucmVhc29uID0gIiI7DQoJCQkJCWNvbnNvbGUubG9nKGAke2l0ZW0uaXRlbU5hbWV96K+E5a6h5LiN6YCa6L+H5L2G5pyq5aGr5YaZ5aSH5rOo77yM5bey5bCG6K+l6K+E5a6h6aG56K6+572u5Li656m6YCk7DQoJCQkJfQ0KCQkJfQ0KCQkJcmV0dXJuIHRydWU7DQoJCX0sDQoNCgkJLy8g55Sf5oiQ5L+d5a2Y5pWw5o2uDQoJCWdlbmVyYXRlU2F2ZURhdGEoKSB7DQoJCQljb25zdCByYXRpbmdDb3B5ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnJhdGluZ1N0YXRlTWFwKSk7IC8vIOivhOWIhumhueeKtuaAgQ0KCQkJY29uc3QgZGF0YSA9IFtdOyAvLyDkv53lrZjmlbDmja4NCgkJCWZvciAoY29uc3QgaXRlbSBvZiB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zKSB7IC8vIOmBjeWOhuivhOWIhumhuQ0KCQkJCWNvbnN0IGl0ZW1JZCA9IGl0ZW0uZW50TWV0aG9kSXRlbUlkOyAvLyDor4TliIbpoblJRA0KCQkJCWNvbnN0IGV2YWx1YXRpb25SZXN1bHQgPSByYXRpbmdDb3B5W2l0ZW1JZF0uc3RhdGU7IC8vIOivhOWIhumhueeKtuaAgQ0KCQkJCWlmIChldmFsdWF0aW9uUmVzdWx0ID09PSBudWxsIHx8IGV2YWx1YXRpb25SZXN1bHQgPT09ICIiKSBjb250aW51ZTsgLy8g5aaC5p6c6K+E5YiG6aG554q25oCB5Li656m677yM5YiZ6Lez6L+HDQoJCQkJLy8g5rOo5oSP77ya5LiN6YCa6L+H5Y6f5Zug55qE5qCh6aqM5bey57uP5ZyodmFsaWRhdGVBbGxSYXRpbmdz5Lit5aSE55CG77yM6L+Z6YeM5Y+q6ZyA6KaB5p6E5bu65pWw5o2uDQoJCQkJY29uc3QgZXZhbHVhdGlvblJlbWFyayA9IHJhdGluZ0NvcHlbaXRlbUlkXS5yZWFzb24gfHwgIiI7IC8vIOivhOWIhumhueWkh+azqA0KCQkJCWRhdGEucHVzaCh7DQoJCQkJCXNjb3JpbmdNZXRob2RVaXRlbUlkOiBpdGVtSWQsIC8vIOivhOWIhumhuUlEDQoJCQkJCWV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsIC8vIOS4k+WutklEDQoJCQkJCWVudElkOiB0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWQsIC8vIOS+m+W6lOWVhklEDQoJCQkJCWV2YWx1YXRpb25SZXN1bHQsIC8vIOivhOWIhumhueeKtuaAgQ0KCQkJCQlldmFsdWF0aW9uUmVtYXJrIC8vIOivhOWIhumhueWkh+azqA0KCQkJCX0pOw0KCQkJfQ0KCQkJcmV0dXJuIGRhdGE7IC8vIOi/lOWbnuS/neWtmOaVsOaNrg0KCQl9LA0KDQoJCS8qKg0KCQkgKiDkuLTml7bkv53lrZjor4TliIbnu5PmnpwNCgkJICogQHJldHVybnMge1Byb21pc2V9DQoJCSAqLw0KCQlzYXZlUmF0aW5nVGVtcCgpIHsNCgkJCS8vIOWFiOagoemqjOaJgOacieivhOWIhumhueaYr+WQpuWhq+WGmeWujOaVtA0KCQkJaWYgKCF0aGlzLnZhbGlkYXRlQWxsUmF0aW5ncygpKSB7DQoJCQkJcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7IGNvZGU6IDAsIHN1Y2Nlc3M6IGZhbHNlIH0pOyAvLyDmoKHpqozlpLHotKUNCgkJCX0NCg0KCQkJY29uc3QgZGF0YSA9IHRoaXMuZ2VuZXJhdGVTYXZlRGF0YSgpOyAvLyDnlJ/miJDkv53lrZjmlbDmja4NCgkJCWlmIChkYXRhLmxlbmd0aCA+IDApIHsNCgkJCQlyZXR1cm4gc2NvcmluZ0ZhY3RvcnMoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQoJCQkJCWlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCgkJCQkJCXRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIik7DQoJCQkJCQlyZXR1cm4geyBjb2RlOiAyMDAsIHN1Y2Nlc3M6IHRydWUgfTsNCgkJCQkJfSBlbHNlIHsNCgkJCQkJCXRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXNwb25zZS5tc2cpOw0KCQkJCQkJcmV0dXJuIHsgY29kZTogcmVzcG9uc2UuY29kZSwgc3VjY2VzczogZmFsc2UgfTsNCgkJCQkJfQ0KCQkJCX0pLmNhdGNoKChlcnJvcikgPT4gew0KCQkJCQl0aGlzLiRtZXNzYWdlLmVycm9yKCLkv53lrZjlpLHotKUiKTsNCgkJCQkJcmV0dXJuIHsgY29kZTogMCwgc3VjY2VzczogZmFsc2UgfTsNCgkJCQl9KTsNCgkJCX0gZWxzZSB7DQoJCQkJcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7IGNvZGU6IDIwMCwgc3VjY2VzczogdHJ1ZSB9KTsgLy8g5rKh5pyJ5pWw5o2u6ZyA6KaB5L+d5a2Y5pe25Lmf6L+U5Zue5oiQ5YqfDQoJCQl9DQoJCX0sDQoNCgkJLyoqDQoJCSAqIOaPkOS6pOivhOWIhue7k+aenA0KCQkgKi8NCgkJc3VibWl0UmF0aW5nKCkgew0KCQkJdGhpcy5zYXZlUmF0aW5nVGVtcCgpLnRoZW4oKHNhdmVSZXN1bHQpID0+IHsNCgkJCQkvLyDmo4Dmn6Xkv53lrZjnu5PmnpzvvIzlpoLmnpzmoKHpqozlpLHotKXliJnkuI3nu6fnu63mj5DkuqQNCgkJCQlpZiAoIXNhdmVSZXN1bHQgfHwgc2F2ZVJlc3VsdC5zdWNjZXNzID09PSBmYWxzZSkgew0KCQkJCQlyZXR1cm47IC8vIOagoemqjOWksei0pe+8jOS4jee7p+e7reaPkOS6pOa1geeoiw0KCQkJCX0NCgkJCQl0aGlzLmNoZWNrQW5kU3VibWl0UmV2aWV3KCk7DQoJCQl9KTsNCgkJfSwNCg0KCQkvLyDmo4Dmn6Xlubbmj5DkuqTor4TlrqHmsYfmgLsNCgkJY2hlY2tBbmRTdWJtaXRSZXZpZXcoKSB7DQoJCQkJY29uc3QgZGF0YSA9IHsNCgkJCQkJcHJvamVjdElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsDQoJCQkJCWV4cGVydFJlc3VsdElkOiB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQsDQoJCQkJCXNjb3JpbmdNZXRob2RJdGVtSWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnNjb3JpbmdNZXRob2RJdGVtSWQsDQoJCQkJfTsNCgkJCQljaGVja1Jldmlld1N1bW1hcnkoZGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCgkJCQkJaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KCQkJCQkJdGhpcy51cGRhdGVFeHBlcnRTY29yZVN0YXR1cygpOyAvLyDkv67mlLnkuJPlrrbov5vluqbnirbmgIENCgkJCQkJCXRoaXMuJGVtaXQoInNlbmQiLCAidHdvIik7IC8vIOi3s+i9rOiHs+S6jOasoeaKpeS7tw0KCQkJCQl9IGVsc2Ugew0KCQkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQoJCQkJCX0NCgkJCX0pOw0KCQl9LA0KDQoJCS8vIOS/ruaUueS4k+Wutui/m+W6pueKtuaAgQ0KCQl1cGRhdGVFeHBlcnRTY29yZVN0YXR1cygpIHsNCgkJCWNvbnN0IHN0YXR1cyA9IHsNCgkJCQlldmFsRXhwZXJ0U2NvcmVJbmZvSWQ6IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImV2YWxFeHBlcnRTY29yZUluZm8iKSkuZXZhbEV4cGVydFNjb3JlSW5mb0lkLA0KCQkJCWV2YWxTdGF0ZTogMSwNCgkJCX07DQoJCQllZGl0RXZhbEV4cGVydFNjb3JlSW5mbyhzdGF0dXMpLnRoZW4oKHJlcykgPT4gew0KCQkJCWlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQoJCQkJCXRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5o+Q5Lqk5oiQ5YqfIik7DQoJCQkJfQ0KCQkJfSk7DQoJCX0sDQoNCgkJLy8gPT09PT09PT09PSDliJ3lp4vljJbnm7jlhbMgPT09PT09PT09PQ0KCQkvKioNCgkJICog5Yid5aeL5YyW6aG16Z2i5pWw5o2uDQoJCSAqLw0KCQlpbml0UGFnZSgpIHsNCgkJCXRoaXMuaW5pdEV4cGVydEluZm8oKTsvLyDliJ3lp4vljJbkuJPlrrbkv6Hmga8NCgkJCXRoaXMuaW5pdEVudERvY1Jlc3BvbnNlUGFnZSgpOyAvLyDliJ3lp4vljJblk43lupTmlofku7bpobXnoIHkv6Hmga8NCgkJCXRoaXMuaW5pdEVudERvY1Byb2N1cmVtZW50UGFnZSgpOyAvLyDliJ3lp4vljJbph4fotK3mlofku7bpobXnoIHkv6Hmga8NCgkJCXRoaXMubG9hZFN1cHBsaWVyT3B0aW9ucygpOy8vIOWKoOi9veS+m+W6lOWVhuS4i+aLiemAiemhuQ0KCQkJdGhpcy5sb2FkU2NvcmluZ1N5c3RlbSgpOyAvLyDliqDovb3or4TliIbkvZPns7sNCgkJCXRoaXMubG9hZEZpbGVzKCk7IC8vIOWKoOi9veaWh+S7tuS/oeaBrw0KCQl9LA0KCQkvLyDliJ3lp4vljJbkuJPlrrbkv6Hmga8NCgkJaW5pdEV4cGVydEluZm8oKSB7DQoJCQl0cnkgew0KCQkJCWNvbnN0IGV4cGVydEluZm9TdHIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZXhwZXJ0SW5mbyIpOw0KCQkJCWlmIChleHBlcnRJbmZvU3RyKSB7DQoJCQkJCXRoaXMuZXhwZXJ0SW5mbyA9IEpTT04ucGFyc2UoZXhwZXJ0SW5mb1N0cik7DQoJCQkJCWNvbnNvbGUubG9nKCLkuJPlrrbkv6Hmga/lt7LliJ3lp4vljJYiLCB0aGlzLmV4cGVydEluZm8pOw0KCQkJCX0gZWxzZSB7DQoJCQkJCWNvbnNvbGUud2FybigibG9jYWxTdG9yYWdl5Lit5pyq5om+5YiwZXhwZXJ0SW5mbyIpOw0KCQkJCX0NCgkJCX0gY2F0Y2ggKGVycm9yKSB7DQoJCQkJY29uc29sZS5lcnJvcigi5Yid5aeL5YyW5LiT5a625L+h5oGv5aSx6LSlOiIsIGVycm9yKTsNCgkJCX0NCgkJfSwNCgkJLy8g5Yid5aeL5YyW5ZON5bqU5paH5Lu26aG156CB5L+h5oGvDQoJCWluaXRFbnREb2NSZXNwb25zZVBhZ2UoKSB7DQoJCQl0aGlzLmVudERvY1Jlc3BvbnNlUGFnZSA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImVudERvY1Jlc3BvbnNlUGFnZSIpKTsNCgkJfSwNCgkJLy8g5Yid5aeL5YyW6YeH6LSt5paH5Lu26aG156CB5L+h5oGvDQoJCWluaXRFbnREb2NQcm9jdXJlbWVudFBhZ2UoKSB7DQoJCQl0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZSA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImVudERvY1Byb2N1cmVtZW50UGFnZSIpKTsNCgkJfSwNCgkJLy8g5Yqg6L295L6b5bqU5ZWG5LiL5ouJ6YCJ6aG5DQoJCWxvYWRTdXBwbGllck9wdGlvbnMoKSB7DQoJCQlzdXBwbGllckluZm8oeyBwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KCQkJCWlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCgkJCQkJdGhpcy5zdXBwbGllck9wdGlvbnMgPSByZXNwb25zZS5yb3dzLmZpbHRlcihpdGVtID0+IGl0ZW0uaXNBYmFuZG9uZWRCaWQgPT09IDApOyAvLyDov4fmu6TmjonooqvmlL7lvIPnmoTmipXmoIcNCgkJCQkJY29uc29sZS5sb2codGhpcy5zdXBwbGllck9wdGlvbnMpOw0KCQkJCX0gZWxzZSB7DQoJCQkJCXRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXNwb25zZS5tc2cpOw0KCQkJCX0NCgkJCX0pOw0KCQl9LA0KCQkvLyDliqDovb3or4TliIbkvZPns7vlj4rliJ3lp4vljJbor4TliIbpobnnirbmgIENCgkJbG9hZFNjb3JpbmdTeXN0ZW0oKSB7DQoJCQlhcHByb3ZhbFByb2Nlc3ModGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLCB0aGlzLmV4cGVydEluZm8ucmVzdWx0SWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQoJCQkJaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KCQkJCQkvLyDmlofku7bliJfooagNCgkJCQkJdGhpcy5hdHRhY2htZW50c0xpc3QgPSByZXNwb25zZS5kYXRhLmJ1c2lUZW5kZXJOb3RpY2UuYXR0YWNobWVudHMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5maWxlVHlwZSA9PSAiMCIpOw0KDQoJCQkJCXRoaXMuc2NvcmluZ1N5c3RlbSA9IHJlc3BvbnNlLmRhdGEuc2NvcmluZ01ldGhvZFVpbmZvLnNjb3JpbmdNZXRob2RJdGVtcy5maW5kKA0KCQkJCQkJaXRlbSA9PiBpdGVtLnNjb3JpbmdNZXRob2RJdGVtSWQgPT0gdGhpcy4kcm91dGUucXVlcnkuc2NvcmluZ01ldGhvZEl0ZW1JZA0KCQkJCQkpOyAvLyDojrflj5blvZPliY3or4TliIbpobkNCgkJCQkJbG9jYWxTdG9yYWdlLnNldEl0ZW0oDQoJCQkJCQkiZXZhbFByb2plY3RFdmFsdWF0aW9uUHJvY2VzcyIsDQoJCQkJCQlKU09OLnN0cmluZ2lmeSh0aGlzLnNjb3JpbmdTeXN0ZW0uZXZhbFByb2plY3RFdmFsdWF0aW9uUHJvY2VzcykNCgkJCQkJKTsgLy8g5L+d5a2Y6K+E5YiG5L2T57O7DQoJCQkJCWNvbnNvbGUubG9nKHRoaXMuc2NvcmluZ1N5c3RlbSk7DQoJCQkJCXRoaXMuaW5pdFJhdGluZ1N0YXRlTWFwQnlTeXN0ZW0oKTsgLy8g5Yid5aeL5YyW6K+E5YiG6aG554q25oCBDQoJCQkJfSBlbHNlIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQoJCQkJfQ0KCQkJfSk7DQoJCX0sDQoJCS8vIOWIneWni+WMluivhOWIhumhueeKtuaAge+8iOagueaNruivhOWIhuS9k+ezu++8iQ0KCQlpbml0UmF0aW5nU3RhdGVNYXBCeVN5c3RlbSgpIHsNCgkJCXRoaXMucmF0aW5nU3RhdGVNYXAgPSB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLnJlZHVjZSgoYWNjLCBpdGVtKSA9PiB7DQoJCQkJYWNjW2l0ZW0uZW50TWV0aG9kSXRlbUlkXSA9IHsgc3RhdGU6IG51bGwsIHJlYXNvbjogIiIgfTsNCgkJCQlyZXR1cm4gYWNjOw0KCQkJfSwge30pOyAvLyDliJ3lp4vljJbor4TliIbpobnnirbmgIENCgkJfSwNCgkJLy8g5Yqg6L295paH5Lu25L+h5oGvDQoJCWxvYWRGaWxlcygpIHsNCgkJCWZpbGVzQnlJZCh0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQoJCQkJaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KCQkJCQl0aGlzLmZpbGVJbmZvID0gcmVzcG9uc2UuZGF0YTsgLy8g5paH5Lu25L+h5oGvDQoJCQkJCS8vIPCfjq8g6ZyA5rGC5LiA77ya5LiN6Ieq5Yqo6K6+572u6YeH6LSt5paH5Lu2VVJM77yM5Y+q5Zyo55So5oi354K55Ye75pe25omN6K6+572uDQoJCQkJCS8vIGlmICh0aGlzLmZpbGVJbmZvLnRlbmRlck5vdGljZUZpbGVQYXRoKSB7DQoJCQkJCS8vIAl0aGlzLnByb2N1cmVtZW50UGRmVXJsID0gdGhpcy5maWxlSW5mby50ZW5kZXJOb3RpY2VGaWxlUGF0aDsgLy8g6YeH6LSt5paH5Lu2DQoJCQkJCS8vIH0NCgkJCQkJLy8g5LiN6Ieq5Yqo6K6+572u5ZON5bqU5paH5Lu2VVJM77yM5Y+q5Zyo6ZyA6KaB5pe25omN6K6+572uDQoJCQkJCS8vIGlmICh0aGlzLmZpbGVJbmZvLmZpbGUpIHsNCgkJCQkJLy8gCXRoaXMucmVzcG9uc2VQZGZVcmwgPSB0aGlzLmZpbGVJbmZvLmZpbGVbMF07IC8vIOWTjeW6lOaWh+S7tg0KCQkJCQkvLyB9DQoJCQkJfSBlbHNlIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlc3BvbnNlLm1zZyk7DQoJCQkJfQ0KCQkJfSk7DQoJCX0sDQoNCgkJLy8gPT09PT09PT09PSDkvpvlupTllYbnm7jlhbMgPT09PT09PT09PQ0KCQkvKioNCgkJICog5L6b5bqU5ZWG5YiH5o2i5pe25aSE55CGDQoJCSAqIEBwYXJhbSB7c3RyaW5nfSBzdXBwbGllck5hbWUg5L6b5bqU5ZWG5ZCN56ewDQoJCSAqLw0KCQloYW5kbGVTdXBwbGllckNoYW5nZShzdXBwbGllck5hbWUpIHsNCgkJCS8vIPCfjq8g6ZyA5rGC5LqM77ya5qOA5p+l5piv5ZCm5pyJUERG5q2j5Zyo5riy5p+T77yM5aaC5p6c5pyJ5YiZ56aB5q2i5YiH5o2i5L6b5bqU5ZWGDQoJCQlpZiAodGhpcy5pc0FueVBkZlJlbmRlcmluZygpKSB7DQoJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKCJQREbmlofku7bmraPlnKjmuLLmn5PkuK3vvIzor7fnqI3lgJnlho3liIfmjaLkvpvlupTllYYiKTsNCgkJCQkvLyDmgaLlpI3kuYvliY3nmoTpgInmi6kNCgkJCQl0aGlzLiRuZXh0VGljaygoKSA9PiB7DQoJCQkJCXRoaXMuc2VsZWN0ZWRTdXBwbGllck5hbWUgPSB0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVyTmFtZSB8fCAiIjsNCgkJCQl9KTsNCgkJCQlyZXR1cm47DQoJCQl9DQoNCgkJCXRoaXMuaXNSZXNwb25zZVZpc2libGUgPSB0cnVlDQoJCQlpZiAoT2JqZWN0LmtleXModGhpcy5zZWxlY3RlZFN1cHBsaWVyKS5sZW5ndGggIT09IDApIHsNCgkJCQl0aGlzLnNhdmVSYXRpbmdUZW1wKCk7DQoJCQl9DQoJCQl0aGlzLnNlbGVjdGVkU3VwcGxpZXIgPSB0aGlzLnN1cHBsaWVyT3B0aW9ucy5maW5kKGl0ZW0gPT4gaXRlbS5iaWRkZXJOYW1lID09PSBzdXBwbGllck5hbWUpIHx8IHt9OyAvLyDojrflj5blvZPliY3kvpvlupTllYYNCgkJCXRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlTWFwID0gdGhpcy5mYWN0b3JzUGFnZU1hcFt0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWRdIHx8IHt9OyAvLyDojrflj5blvZPliY3kvpvlupTllYbor4TliIbpobnpobXnoIENCgkJCXRoaXMubG9hZFN1cHBsaWVyRmFjdG9yRGV0YWlsKHN1cHBsaWVyTmFtZSk7IC8vIOWKoOi9veW9k+WJjeS+m+W6lOWVhuivhOWIhumhueivpuaDhQ0KCQkJdGhpcy5sb2FkU3VwcGxpZXJDaGVja1Jlc3VsdCgpOyAvLyDliqDovb3lvZPliY3kvpvlupTllYbns7vnu5/liJ3pqoznu5PmnpwNCgkJCXRoaXMuc2hvd1Jlc3BvbnNlRmlsZSgpOyAvLyDmmL7npLrlk43lupTmlofku7YNCgkJfSwNCgkJLy8g5Yqg6L295b2T5YmN5L6b5bqU5ZWG6K+E5YiG6aG56K+m5oOFDQoJCWxvYWRTdXBwbGllckZhY3RvckRldGFpbChiaWRkZXJOYW1lKSB7DQoJCQl0aGlzLmNsZWFyUmF0aW5nU3RhdGVNYXAoKTsNCgkJCQ0KCQkJY29uc3QgZGV0YWlsRGF0YSA9IHsNCgkJCQlleHBlcnRSZXN1bHRJZDogdGhpcy5leHBlcnRJbmZvLnJlc3VsdElkLA0KCQkJCXByb2plY3RJZDogdGhpcy4kcm91dGUucXVlcnkucHJvamVjdElkLA0KCQkJCXNjb3JpbmdNZXRob2RJdGVtSWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnNjb3JpbmdNZXRob2RJdGVtSWQsDQoJCQl9Ow0KCQkJZ2V0RGV0YWlsQnlQc3h4KGRldGFpbERhdGEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQoJCQkJaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KCQkJCQl0aGlzLmZhY3RvckRldGFpbExpc3QgPSByZXNwb25zZS5kYXRhOw0KCQkJCQljb25zdCBmYWN0b3IgPSB0aGlzLmZhY3RvckRldGFpbExpc3QuZmluZChpdGVtID0+IGl0ZW0uYmlkZGVyTmFtZSA9PT0gYmlkZGVyTmFtZSk/LmV2YWxFeHBlcnRFdmFsdWF0aW9uRGV0YWlsczsNCgkJCQkJaWYgKGZhY3Rvcikgew0KCQkJCQkJdGhpcy5zZXRSYXRpbmdTdGF0ZU1hcEJ5RmFjdG9yKGZhY3Rvcik7DQoJCQkJCX0gZWxzZSB7DQoJCQkJCQl0aGlzLmNsZWFyUmF0aW5nU3RhdGVNYXAoKTsNCgkJCQkJfQ0KCQkJCX0gZWxzZSB7DQoJCQkJCXRoaXMuJG1lc3NhZ2Uud2FybmluZyhyZXNwb25zZS5tc2cpOw0KCQkJCX0NCgkJCX0pOw0KCQl9LA0KCQkvLyDmoLnmja7or4TliIbor6bmg4Xorr7nva7or4TliIbpobnnirbmgIENCgkJc2V0UmF0aW5nU3RhdGVNYXBCeUZhY3RvcihmYWN0b3IpIHsNCgkJCWZhY3Rvci5mb3JFYWNoKGl0ZW0gPT4gew0KCQkJCXRoaXMucmF0aW5nU3RhdGVNYXBbaXRlbS5zY29yaW5nTWV0aG9kVWl0ZW1JZF0ucmVhc29uID0gaXRlbS5ldmFsdWF0aW9uUmVtYXJrOw0KCQkJCXRoaXMucmF0aW5nU3RhdGVNYXBbaXRlbS5zY29yaW5nTWV0aG9kVWl0ZW1JZF0uc3RhdGUgPSBpdGVtLmV2YWx1YXRpb25SZXN1bHQ7DQoJCQl9KTsNCgkJfSwNCgkJLy8g5Yqg6L295b2T5YmN5L6b5bqU5ZWG57O757uf5Yid6aqM57uT5p6cDQoJCWxvYWRTdXBwbGllckNoZWNrUmVzdWx0KCkgew0KCQkJY29uc3QgcmV2aWV3RGF0YSA9IHsNCgkJCQlwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwNCgkJCQllbnRJZDogdGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkLA0KCQkJfTsNCgkJCXJlc0RvY1Jldmlld0ZhY3RvcnNEZWNpc2lvbihyZXZpZXdEYXRhKS50aGVuKChyZXMpID0+IHsNCgkJCQlpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KCQkJCQl0aGlzLmNoZWNrUmVzdWx0ID0gcmVzLmRhdGE7DQoJCQkJfQ0KCQkJfSk7DQoJCX0sDQoJCS8vIOWIneWni+WMluivhOWIhumhueeKtuaAge+8iOa4heepuu+8iQ0KCQljbGVhclJhdGluZ1N0YXRlTWFwKCkgew0KCQkJT2JqZWN0LmtleXModGhpcy5yYXRpbmdTdGF0ZU1hcCkuZm9yRWFjaChrZXkgPT4gew0KCQkJCXRoaXMucmF0aW5nU3RhdGVNYXBba2V5XS5zdGF0ZSA9IG51bGw7DQoJCQkJdGhpcy5yYXRpbmdTdGF0ZU1hcFtrZXldLnJlYXNvbiA9ICIiOw0KCQkJfSk7DQoJCX0sDQoNCgkJLy8gPT09PT09PT09PSDmlofku7bnm7jlhbMgPT09PT09PT09PQ0KCQkvKioNCgkJICog5pi+56S65ZON5bqU5paH5Lu2DQoJCSAqLw0KCQlzaG93UmVzcG9uc2VGaWxlKCkgew0KCQkJaWYgKE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0ZWRTdXBwbGllcikubGVuZ3RoID09PSAwKSB7DQoJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nkvpvlupTllYYiKTsNCgkJCX0gZWxzZSB7DQoJCQkJLy8g8J+OryDpnIDmsYLkuozvvJrmo4Dmn6XmmK/lkKbmnIlQREbmraPlnKjmuLLmn5PvvIzlpoLmnpzmnInliJnnpoHmraLliIfmjaINCgkJCQlpZiAodGhpcy5pc0FueVBkZlJlbmRlcmluZygpKSB7DQoJCQkJCXRoaXMuJG1lc3NhZ2Uud2FybmluZygiUERG5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN5YiH5o2iIik7DQoJCQkJCXJldHVybjsNCgkJCQl9DQoNCgkJCQl0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdyZXNwb25zZSc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KCQkJCXRoaXMuaXNEb3VibGVWaWV3ID0gZmFsc2U7IC8vIOWFs+mXreWvueavlA0KCQkJCXRoaXMuaXNQcm9jdXJlbWVudFZpc2libGUgPSBmYWxzZTsgLy8g5YWz6Zet6YeH6LSt5paH5Lu2DQoJCQkJdGhpcy5pc1Jlc3BvbnNlVmlzaWJsZSA9IHRydWU7IC8vIOaYvuekuuWTjeW6lOaWh+S7tg0KDQoJCQkJLy8g8J+OryDlj6rlnKjnlKjmiLfngrnlh7vml7bmiY3orr7nva7lk43lupTmlofku7ZVUkzvvIzlvIDlp4vmuLLmn5MNCgkJCQl0aGlzLnJlc3BvbnNlUGRmVXJsID0gdGhpcy5maWxlSW5mby5maWxlW3RoaXMuc2VsZWN0ZWRTdXBwbGllci5iaWRkZXJJZF07IC8vIOWTjeW6lOaWh+S7tg0KDQoJCQkJLy8g5Y+z5L6n6K+E5YiG6aG55pi+56S65Li65ZON5bqU5paH5Lu255qE6K+E5YiG6aG5DQoJCQkJaWYgKE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0ZWRTdXBwbGllcikubGVuZ3RoICE9PSAwKSB7DQoJCQkJCXRoaXMuc2F2ZVJhdGluZ1RlbXAoKTsNCgkJCQl9DQoJCQkJdGhpcy5zZWxlY3RlZFN1cHBsaWVyID0gdGhpcy5zdXBwbGllck9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uYmlkZGVyTmFtZSA9PT0gdGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlck5hbWUpIHx8IHt9OyAvLyDojrflj5blvZPliY3kvpvlupTllYYNCgkJCQl0aGlzLnN1cHBsaWVyRmFjdG9yUGFnZU1hcCA9IHRoaXMuZmFjdG9yc1BhZ2VNYXBbdGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlcklkXSB8fCB7fTsgLy8g6I635Y+W5b2T5YmN5L6b5bqU5ZWG6K+E5YiG6aG56aG156CBDQoJCQkJdGhpcy5sb2FkU3VwcGxpZXJGYWN0b3JEZXRhaWwodGhpcy5zZWxlY3RlZFN1cHBsaWVyLmJpZGRlck5hbWUpOyAvLyDliqDovb3lvZPliY3kvpvlupTllYbor4TliIbpobnor6bmg4UNCgkJCQl0aGlzLmxvYWRTdXBwbGllckNoZWNrUmVzdWx0KCk7IC8vIOWKoOi9veW9k+WJjeS+m+W6lOWVhuezu+e7n+WInemqjOe7k+aenA0KCQkJfQ0KCQl9LA0KCQkvKioNCgkJICog5paH5Lu25a+55q+U5pi+56S6DQoJCSAqLw0KCQlzaG93RmlsZUNvbnRyYXN0KCkgew0KCQkJaWYgKE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0ZWRTdXBwbGllcikubGVuZ3RoID09PSAwKSB7DQoJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nkvpvlupTllYYiKTsNCgkJCX0gZWxzZSB7DQoJCQkJLy8g8J+OryDpnIDmsYLkuozvvJrmo4Dmn6XmmK/lkKbmnIlQREbmraPlnKjmuLLmn5PvvIzlpoLmnpzmnInliJnnpoHmraLliIfmjaINCgkJCQlpZiAodGhpcy5pc0FueVBkZlJlbmRlcmluZygpKSB7DQoJCQkJCXRoaXMuJG1lc3NhZ2Uud2FybmluZygiUERG5paH5Lu25q2j5Zyo5riy5p+T5Lit77yM6K+356iN5YCZ5YaN5YiH5o2iIik7DQoJCQkJCXJldHVybjsNCgkJCQl9DQoNCgkJCQl0aGlzLmFjdGl2ZUJ1dHRvbiA9ICdjb250cmFzdCc7IC8vIOiuvue9ruW9k+WJjea/gOa0u+aMiemSrg0KCQkJCXRoaXMuaXNEb3VibGVWaWV3ID0gdHJ1ZTsNCgkJCQl0aGlzLmlzUHJvY3VyZW1lbnRWaXNpYmxlID0gdHJ1ZTsNCgkJCQl0aGlzLmlzUmVzcG9uc2VWaXNpYmxlID0gdHJ1ZTsNCg0KCQkJCS8vIPCfjq8g5Y+q5Zyo55So5oi354K55Ye75a+55q+U5pe25omN6K6+572u5paH5Lu2VVJM77yM5byA5aeL5riy5p+TDQoJCQkJdGhpcy5yZXNwb25zZVBkZlVybCA9IHRoaXMuZmlsZUluZm8uZmlsZVt0aGlzLnNlbGVjdGVkU3VwcGxpZXIuYmlkZGVySWRdOyAvLyDlk43lupTmlofku7YNCgkJCQlpZiAodGhpcy5maWxlSW5mby50ZW5kZXJOb3RpY2VGaWxlUGF0aCkgew0KCQkJCQl0aGlzLnByb2N1cmVtZW50UGRmVXJsID0gdGhpcy5maWxlSW5mby50ZW5kZXJOb3RpY2VGaWxlUGF0aDsgLy8g6YeH6LSt5paH5Lu2DQoJCQkJfQ0KCQkJfQ0KCQl9LA0KCQkvKioNCgkJICog5p+l55yL6YeH6LSt5paH5Lu2DQoJCSAqLw0KCQlzaG93UHJvY3VyZW1lbnRGaWxlKCkgew0KCQkJLy8g8J+OryDpnIDmsYLkuozvvJrmo4Dmn6XmmK/lkKbmnIlQREbmraPlnKjmuLLmn5PvvIzlpoLmnpzmnInliJnnpoHmraLliIfmjaINCgkJCWlmICh0aGlzLmlzQW55UGRmUmVuZGVyaW5nKCkpIHsNCgkJCQl0aGlzLiRtZXNzYWdlLndhcm5pbmcoIlBERuaWh+S7tuato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeWIh+aNoiIpOw0KCQkJCXJldHVybjsNCgkJCX0NCg0KCQkJdGhpcy5hY3RpdmVCdXR0b24gPSAncHJvY3VyZW1lbnQnOyAvLyDorr7nva7lvZPliY3mv4DmtLvmjInpkq4NCgkJCXRoaXMuaXNEb3VibGVWaWV3ID0gZmFsc2U7DQoJCQl0aGlzLmlzUmVzcG9uc2VWaXNpYmxlID0gZmFsc2U7DQoJCQl0aGlzLmlzUHJvY3VyZW1lbnRWaXNpYmxlID0gdHJ1ZTsNCg0KCQkJLy8g8J+OryDpnIDmsYLkuIDvvJrlj6rlnKjnlKjmiLfngrnlh7vml7bmiY3orr7nva7ph4fotK3mlofku7ZVUkzvvIzlvIDlp4vmuLLmn5MNCgkJCWlmICh0aGlzLmZpbGVJbmZvLnRlbmRlck5vdGljZUZpbGVQYXRoKSB7DQoJCQkJdGhpcy5wcm9jdXJlbWVudFBkZlVybCA9IHRoaXMuZmlsZUluZm8udGVuZGVyTm90aWNlRmlsZVBhdGg7IC8vIOmHh+i0reaWh+S7tg0KCQkJfQ0KDQoJCQkvLyDlj7Pkvqfor4TliIbpobnmmL7npLrkuLrph4fotK3mlofku7bnmoTor4TliIbpobkNCgkJCWxldCBwYWdlUHJvY3VyZW1lbnRBcnIgPSBbXTsNCgkJCWZvciAobGV0IGl0ZW0gaW4gdGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2Upew0KCQkJCXBhZ2VQcm9jdXJlbWVudEFyci5wdXNoKHsNCgkJCQkJaXRlbU5hbWU6IGl0ZW0sDQoJCQkJCWp1bXBUb1BhZ2U6IHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2l0ZW1dDQoJCQkJfSkNCgkJCX0NCg0KCQkJY29uc29sZS5sb2codGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtcyk7DQoJCQljb25zb2xlLmxvZyhwYWdlUHJvY3VyZW1lbnRBcnIpDQoJCQl0aGlzLnBhZ2VQcm9jdXJlbWVudCA9IFtdOw0KCQkJZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnNjb3JpbmdTeXN0ZW0udWl0ZW1zLmxlbmd0aDtpKyspew0KCQkJCWZvciAobGV0IGogPSAwOyBqIDwgcGFnZVByb2N1cmVtZW50QXJyLmxlbmd0aDtqKyspew0KCQkJCQlpZiAodGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpXS5pdGVtTmFtZSA9PSBwYWdlUHJvY3VyZW1lbnRBcnJbal0uaXRlbU5hbWUpew0KCQkJCQkJdGhpcy5wYWdlUHJvY3VyZW1lbnQucHVzaCh7Li4udGhpcy5zY29yaW5nU3lzdGVtLnVpdGVtc1tpXSwuLi5wYWdlUHJvY3VyZW1lbnRBcnJbal19KTsNCgkJCQkJfQ0KCQkJCX0NCgkJCX0NCgkJCWNvbnNvbGUubG9nKHRoaXMucGFnZVByb2N1cmVtZW50KQ0KCQl9LA0KDQoJCS8vID09PT09PT09PT0g6aG16Z2i6Lez6L2s55u45YWzID09PT09PT09PT0NCgkJLyoqDQoJCSAqIOi3s+i9rOWIsOivhOWIhumhueWvueW6lOmhteeggQ0KCQkgKiBAcGFyYW0ge09iamVjdH0gZmFjdG9ySXRlbSDor4TliIbpobnlr7nosaENCgkJICovDQoJCWp1bXBUb0ZhY3RvclBhZ2UoZmFjdG9ySXRlbSkgew0KCQkJLy8g5qOA5p+lUERG5piv5ZCm5riy5p+T5a6M5oiQDQoJCQlpZiAoIXRoaXMuY2FuSnVtcFRvUGFnZSgpKSB7DQoJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKCJQREbpobXpnaLmraPlnKjmuLLmn5PkuK3vvIzor7fnqI3lgJnlho3or5UiKTsNCgkJCQlyZXR1cm47DQoJCQl9DQoNCgkJCXRoaXMuc2VsZWN0ZWRGYWN0b3JOb2RlID0gZmFjdG9ySXRlbTsgLy8g6K6+572u5b2T5YmN6YCJ5Lit5Zug5a2QDQoNCgkJCS8vIOWmguaenOWPquaYvuekuumHh+i0reaWh+S7tu+8jOS9v+eUqOmHh+i0reaWh+S7tumhteeggeS/oeaBrw0KCQkJaWYgKHRoaXMuaXNQcm9jdXJlbWVudFZpc2libGUgJiYgIXRoaXMuaXNSZXNwb25zZVZpc2libGUpIHsNCgkJCQlpZiAoIXRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZCkgew0KCQkJCQl0aGlzLiRtZXNzYWdlLndhcm5pbmcoIumHh+i0reaWh+S7tuato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeivlSIpOw0KCQkJCQlyZXR1cm47DQoJCQkJfQ0KCQkJCWlmIChmYWN0b3JJdGVtLmp1bXBUb1BhZ2UpIHsNCgkJCQkJdGhpcy4kcmVmcy5wcm9jdXJlbWVudC5za2lwUGFnZShmYWN0b3JJdGVtLmp1bXBUb1BhZ2UpOyAvLyDph4fotK3mlofku7bot7PpobUNCgkJCQl9IGVsc2UgaWYgKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlICYmIHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2ZhY3Rvckl0ZW0uaXRlbU5hbWVdKSB7DQoJCQkJCXRoaXMuJHJlZnMucHJvY3VyZW1lbnQuc2tpcFBhZ2UodGhpcy5lbnREb2NQcm9jdXJlbWVudFBhZ2VbZmFjdG9ySXRlbS5pdGVtTmFtZV0pOyAvLyDph4fotK3mlofku7bot7PpobUNCgkJCQl9DQoJCQkJcmV0dXJuOw0KCQkJfQ0KDQoJCQkvLyDlpoLmnpzmmL7npLrlk43lupTmlofku7bmiJblr7nmr5TmqKHlvI/vvIzpnIDopoHpgInmi6nkvpvlupTllYYNCgkJCWlmICghdGhpcy5zdXBwbGllckZhY3RvclBhZ2VNYXAgfHwgT2JqZWN0LmtleXModGhpcy5zdXBwbGllckZhY3RvclBhZ2VNYXApLmxlbmd0aCA9PT0gMCkgew0KCQkJCXRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI6YCJ5oup5L6b5bqU5ZWGIik7IC8vIOacqumAieS+m+W6lOWVhuaPkOekug0KCQkJCXJldHVybjsNCgkJCX0NCg0KCQkJLy8g6Lez6L2s5Yiw5ZON5bqU5paH5Lu25a+55bqU6aG156CBDQoJCQlpZiAodGhpcy5pc1Jlc3BvbnNlVmlzaWJsZSAmJiB0aGlzLiRyZWZzLnJlc3BvbnNlKSB7DQoJCQkJaWYgKCF0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQpIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLlk43lupTmlofku7bmraPlnKjmuLLmn5PkuK3vvIzor7fnqI3lgJnlho3or5UiKTsNCgkJCQkJcmV0dXJuOw0KCQkJCX0NCgkJCQl0aGlzLiRyZWZzLnJlc3BvbnNlLnNraXBQYWdlKHRoaXMuc3VwcGxpZXJGYWN0b3JQYWdlTWFwW3RoaXMuc2VsZWN0ZWRGYWN0b3JOb2RlLml0ZW1OYW1lXSk7IC8vIOWTjeW6lOaWh+S7tui3s+mhtQ0KCQkJfQ0KDQoJCQkvLyDot7PovazliLDph4fotK3mlofku7blr7nlupTpobXnoIENCgkJCWlmICh0aGlzLmlzUHJvY3VyZW1lbnRWaXNpYmxlICYmIHRoaXMuJHJlZnMucHJvY3VyZW1lbnQpIHsNCgkJCQlpZiAoIXRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZCkgew0KCQkJCQl0aGlzLiRtZXNzYWdlLndhcm5pbmcoIumHh+i0reaWh+S7tuato+WcqOa4suafk+S4re+8jOivt+eojeWAmeWGjeivlSIpOw0KCQkJCQlyZXR1cm47DQoJCQkJfQ0KCQkJCS8vIOWcqOWvueavlOaooeW8j+S4i++8jOmHh+i0reaWh+S7tuW6lOivpei3s+i9rOWIsOmHh+i0reaWh+S7tueahOWvueW6lOmhteegge+8jOiAjOS4jeaYr+S+m+W6lOWVhueahOmhteeggQ0KCQkJCWlmICh0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZSAmJiB0aGlzLmVudERvY1Byb2N1cmVtZW50UGFnZVtmYWN0b3JJdGVtLml0ZW1OYW1lXSkgew0KCQkJCQl0aGlzLiRyZWZzLnByb2N1cmVtZW50LnNraXBQYWdlKHRoaXMuZW50RG9jUHJvY3VyZW1lbnRQYWdlW2ZhY3Rvckl0ZW0uaXRlbU5hbWVdKTsgLy8g6YeH6LSt5paH5Lu26Lez6aG1DQoJCQkJfSBlbHNlIHsNCgkJCQkJLy8g5Zyo5a+55q+U5qih5byP5LiL77yM5aaC5p6c5rKh5pyJ6YeH6LSt5paH5Lu26aG156CB5L+h5oGv77yM5YiZ5Y+q6Lez6L2s5ZON5bqU5paH5Lu255qE6aG156CB77yM5LiN6Lez6L2s6YeH6LSt5paH5Lu2DQoJCQkJCS8vIOi/meagt+WPr+S7pemBv+WFjemHh+i0reaWh+S7tuWSjOWTjeW6lOaWh+S7tuaYvuekuuS4jeWQjOeahOWGheWuuemAoOaIkOa3t+a3hg0KCQkJCX0NCgkJCX0NCgkJfSwNCg0KCQkvKioNCgkJICog5qOA5p+l5piv5ZCm5Y+v5Lul6Lez6L2s6aG16Z2iDQoJCSAqIEByZXR1cm5zIHtib29sZWFufSDmmK/lkKblj6/ku6Xot7PovawNCgkJICovDQoJCWNhbkp1bXBUb1BhZ2UoKSB7DQoJCQkvLyDlpoLmnpzlj6rmmL7npLrph4fotK3mlofku7YNCgkJCWlmICh0aGlzLmlzUHJvY3VyZW1lbnRWaXNpYmxlICYmICF0aGlzLmlzUmVzcG9uc2VWaXNpYmxlKSB7DQoJCQkJcmV0dXJuIHRoaXMucHJvY3VyZW1lbnRQZGZSZW5kZXJlZDsNCgkJCX0NCgkJCS8vIOWmguaenOWPquaYvuekuuWTjeW6lOaWh+S7tg0KCQkJaWYgKHRoaXMuaXNSZXNwb25zZVZpc2libGUgJiYgIXRoaXMuaXNQcm9jdXJlbWVudFZpc2libGUpIHsNCgkJCQlyZXR1cm4gdGhpcy5yZXNwb25zZVBkZlJlbmRlcmVkOw0KCQkJfQ0KCQkJLy8g5aaC5p6c5a+55q+U5qih5byP77yI5Lik5Liq6YO95pi+56S677yJDQoJCQlpZiAodGhpcy5pc1Jlc3BvbnNlVmlzaWJsZSAmJiB0aGlzLmlzUHJvY3VyZW1lbnRWaXNpYmxlKSB7DQoJCQkJcmV0dXJuIHRoaXMucmVzcG9uc2VQZGZSZW5kZXJlZCAmJiB0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQ7DQoJCQl9DQoJCQlyZXR1cm4gZmFsc2U7DQoJCX0sDQoNCgkJLyoqDQoJCSAqIPCfjq8g6ZyA5rGC5LqM77ya5qOA5p+l5piv5ZCm5pyJ5Lu75L2VUERG5q2j5Zyo5riy5p+TDQoJCSAqIEByZXR1cm5zIHtib29sZWFufSDmmK/lkKbmnIlQREbmraPlnKjmuLLmn5MNCgkJICovDQoJCWlzQW55UGRmUmVuZGVyaW5nKCkgew0KCQkJLy8g5qOA5p+l5b2T5YmN5pi+56S655qEUERG5piv5ZCm5q2j5Zyo5riy5p+TDQoJCQlpZiAodGhpcy5pc1Byb2N1cmVtZW50VmlzaWJsZSAmJiB0aGlzLnByb2N1cmVtZW50UGRmVXJsICYmICF0aGlzLnByb2N1cmVtZW50UGRmUmVuZGVyZWQpIHsNCgkJCQlyZXR1cm4gdHJ1ZTsNCgkJCX0NCgkJCWlmICh0aGlzLmlzUmVzcG9uc2VWaXNpYmxlICYmIHRoaXMucmVzcG9uc2VQZGZVcmwgJiYgIXRoaXMucmVzcG9uc2VQZGZSZW5kZXJlZCkgew0KCQkJCXJldHVybiB0cnVlOw0KCQkJfQ0KCQkJcmV0dXJuIGZhbHNlOw0KCQl9LA0KDQoJCS8qKg0KCQkgKiDlpITnkIZQREbmuLLmn5PnirbmgIHlj5jljJYNCgkJICogQHBhcmFtIHtib29sZWFufSBpc1JlbmRlcmVkIOaYr+WQpua4suafk+WujOaIkA0KCQkgKiBAcGFyYW0ge3N0cmluZ30gcGRmVHlwZSBQREbnsbvlnovvvJoncmVzcG9uc2UnIOaIliAncHJvY3VyZW1lbnQnDQoJCSAqLw0KCQloYW5kbGVQZGZSZW5kZXJTdGF0dXNDaGFuZ2UoaXNSZW5kZXJlZCwgcGRmVHlwZSkgew0KCQkJaWYgKHBkZlR5cGUgPT09ICdyZXNwb25zZScpIHsNCgkJCQl0aGlzLnJlc3BvbnNlUGRmUmVuZGVyZWQgPSBpc1JlbmRlcmVkOw0KCQkJfSBlbHNlIGlmIChwZGZUeXBlID09PSAncHJvY3VyZW1lbnQnKSB7DQoJCQkJdGhpcy5wcm9jdXJlbWVudFBkZlJlbmRlcmVkID0gaXNSZW5kZXJlZDsNCgkJCX0NCgkJCQ0KCQkJaWYgKGlzUmVuZGVyZWQpIHsNCgkJCQljb25zb2xlLmxvZyhgJHtwZGZUeXBlID09PSAncmVzcG9uc2UnID8gJ+WTjeW6lCcgOiAn6YeH6LStJ33mlofku7bmuLLmn5PlrozmiJDvvIzlj6/ku6Xov5vooYzpobXpnaLot7PovaxgKTsNCgkJCX0NCgkJfSwNCgkJLyoqDQoJCSAqIOi3s+i9rOWIsOS6jOasoeaKpeS7tw0KCQkgKi8NCgkJZ29Ub1NlY29uZE9mZmVyKCkgew0KCQkJY29uc3QgcXVlcnkgPSB7DQoJCQkJcHJvamVjdElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5wcm9qZWN0SWQsDQoJCQkJempobTogdGhpcy4kcm91dGUucXVlcnkuempobSwNCgkJCQlzY29yaW5nTWV0aG9kSXRlbUlkOiBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJ0ZW5kZXJPZmZlclNjb3JpbmdNZXRob2RJdGVtcyIpKSwNCgkJCX07DQoJCQl0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICIvc2Vjb25kT2ZmZXIiLCBxdWVyeSB9KTsNCgkJfSwNCgkJLyoqDQoJCSAqIOi3s+i9rOWIsOivouaghw0KCQkgKi8NCgkJZ29Ub0JpZElucXVpcnkoKSB7DQoJCQljb25zdCBxdWVyeSA9IHsNCgkJCQlwcm9qZWN0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2plY3RJZCwNCgkJCQl6amhtOiB0aGlzLiRyb3V0ZS5xdWVyeS56amhtLA0KCQkJCXNjb3JpbmdNZXRob2RJdGVtSWQ6IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oInRlbmRlck9mZmVyU2NvcmluZ01ldGhvZEl0ZW1zIikpLA0KCQkJfTsNCgkJCXRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogIi9iaWRJbnF1aXJ5IiwgcXVlcnkgfSk7DQoJCX0sDQoJCS8qKg0KCQkgKiDojrflj5blm6DntKDlr7nlupTpobXnoIENCgkJICovDQoJCWxvYWRGYWN0b3JzUGFnZU1hcCgpIHsNCgkJCXRoaXMuZmFjdG9yc1BhZ2VNYXAgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJlbnREb2NSZXNwb25zZVBhZ2UiKSk7DQoJCX0sDQoNCgkJLyoqDQoJCSAqIOS4i+i9veaWh+S7tg0KCQkgKiBAcGFyYW0ge09iamVjdH0gaXRlbSAtIOaWh+S7tuWvueixoQ0KCQkgKi8NCgkJZG93bmxvYWRGaWxlKGl0ZW0pIHsNCgkJCXRoaXMuJGRvd25sb2FkLnppcChpdGVtLmZpbGVQYXRoLCBpdGVtLmZpbGVOYW1lKTsNCgkJfSwNCg0KCQkvLyA9PT09PT09PT09IOaCrOWBnOebuOWFsyA9PT09PT09PT09DQoJCS8qKg0KCQkgKiDmmL7npLror4TliIbpobnmgqzmta7moYYNCgkJICogQHBhcmFtIHtPYmplY3R9IGZhY3Rvckl0ZW0g6K+E5YiG6aG55a+56LGhDQoJCSAqLw0KCQlzaG93RmFjdG9yVG9vbHRpcChmYWN0b3JJdGVtKSB7DQoJCQlpZiAoIWZhY3Rvckl0ZW0uaXRlbVJlbWFyaykgcmV0dXJuOyAvLyDlpoLmnpzmsqHmnInor4TlrqHlhoXlrrnliJnkuI3mmL7npLoNCg0KCQkJLy8g5riF6Zmk5LmL5YmN55qE5a6a5pe25ZmoDQoJCQlpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCQljbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkJfQ0KDQoJCQkvLyDlu7bov5/mmL7npLrmgqzmta7moYbvvIzpgb/lhY3lv6vpgJ/np7vliqjml7bpopHnuYHmmL7npLoNCgkJCXRoaXMudG9vbHRpcFRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7DQoJCQkJdGhpcy5ob3ZlcmVkRmFjdG9yTm9kZSA9IGZhY3Rvckl0ZW07DQoJCQl9LCAzMDApOyAvLyAzMDBtc+W7tui/nw0KCQl9LA0KDQoJCS8qKg0KCQkgKiDpmpDol4/or4TliIbpobnmgqzmta7moYYNCgkJICovDQoJCWhpZGVGYWN0b3JUb29sdGlwKCkgew0KCQkJLy8g5riF6Zmk5a6a5pe25ZmoDQoJCQlpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCQljbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkJCXRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJCX0NCg0KCQkJLy8g5bu26L+f6ZqQ6JeP77yM57uZ55So5oi35pe26Ze056e75Yqo5Yiw5oKs5rWu5qGG5LiKDQoJCQlzZXRUaW1lb3V0KCgpID0+IHsNCgkJCQl0aGlzLmhvdmVyZWRGYWN0b3JOb2RlID0gbnVsbDsNCgkJCX0sIDEwMCk7DQoJCX0sDQoNCgkJLyoqDQoJCSAqIOa4hemZpOaCrOa1ruahhuWumuaXtuWZqO+8iOW9k+m8oOagh+enu+WKqOWIsOaCrOa1ruahhuS4iuaXtu+8iQ0KCQkgKi8NCgkJY2xlYXJUb29sdGlwVGltZXIoKSB7DQoJCQlpZiAodGhpcy50b29sdGlwVGltZXIpIHsNCgkJCQljbGVhclRpbWVvdXQodGhpcy50b29sdGlwVGltZXIpOw0KCQkJCXRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJCX0NCgkJfQ0KCQ0KfSwNCg0KCW1vdW50ZWQoKSB7DQoJCXRoaXMuaW5pdFBhZ2UoKTsNCgkJdGhpcy5sb2FkRmFjdG9yc1BhZ2VNYXAoKTsNCgl9LA0KDQoJYmVmb3JlRGVzdHJveSgpIHsNCgkJLy8g5riF55CG5a6a5pe25ZmoDQoJCWlmICh0aGlzLnRvb2x0aXBUaW1lcikgew0KCQkJY2xlYXJUaW1lb3V0KHRoaXMudG9vbHRpcFRpbWVyKTsNCgkJCXRoaXMudG9vbHRpcFRpbWVyID0gbnVsbDsNCgkJfQ0KCX0sDQp9Ow0K"}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu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file": "one.vue", "sourceRoot": "src/views/expertReview/qualification", "sourcesContent": ["<template>\r\n\t<div class=\"main-container-one\">\r\n\t\t<div class=\"left-panel\">\r\n\t\t\t<div class=\"header-bar\">\r\n\t\t\t\t<div class=\"header-title\">\r\n\t\t\t\t\t<div>资格性评审</div>\r\n\t\t\t\t\t<div class=\"header-steps\">\r\n\t\t\t\t\t\t<div class=\"steps-tip\">该页面操作说明</div>\r\n\t\t\t\t\t\t<el-image class=\"steps-img\" :src=\"helpImageList[0]\" :preview-src-list=\"helpImageList\">\r\n\t\t\t\t\t\t</el-image>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 文件列表 -->\r\n\t\t\t\t<div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t\t\t\t<div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t\t\t\t<el-card\r\n\t\t\t\t\t\tv-for=\"(item, index) in attachmentsList\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"fileItem\"\r\n\t\t\t\t\t\tshadow=\"hover\"\r\n\t\t\t\t\t\**************=\"downloadFile(item)\"\r\n\t\t\t\t\t\tstyle=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t\t\t\t<span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-card>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div class=\"header-btns\">\r\n\t\t\t\t\t<el-button class=\"item-button\" @click=\"goToBidInquiry\">询标</el-button>\r\n\t\t\t\t\t<!-- <el-button class=\"item-button\" @click=\"secondOffer\">发起二次报价</el-button> -->\r\n\t\t\t\t\t<div class=\"header-btns-group\">\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'procurement' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t:disabled=\"isAnyPdfRendering()\"\r\n\t\t\t\t\t\t\t@click=\"showProcurementFile\">采购文件</el-button>\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'response' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t:disabled=\"isAnyPdfRendering()\"\r\n\t\t\t\t\t\t\t@click=\"showResponseFile\">响应文件</el-button>\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'contrast' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t:disabled=\"isAnyPdfRendering()\"\r\n\t\t\t\t\t\t\t@click=\"showFileContrast\">对比</el-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div style=\"height:82%\">\r\n\t\t\t\t<!-- PDF预览区域 -->\r\n\t\t\t\t<div class=\"pdf-container\">\r\n\t\t\t\t\t<div v-show=\"isProcurementVisible\" :class=\"['pdf-view', { 'border-right': isDoubleView }]\">\r\n<!--\t\t\t\t\t\t<pdfView ref=\"procurement\" :pdfurl=\"procurementPdfUrl\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<PdfViewImproved\r\n\t\t\t\t\t\t\tref=\"procurement\"\r\n\t\t\t\t\t\t\t:pdfurl=\"procurementPdfUrl\"\r\n\t\t\t\t\t\t\t:page-height=\"800\"\r\n\t\t\t\t\t\t\t:buffer-size=\"2\"\r\n\t\t\t\t\t\t\t@render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div v-show=\"isResponseVisible\" :class=\"['pdf-view', { 'border-left': isDoubleView }]\">\r\n<!--\t\t\t\t\t\t<pdfView ref=\"response\" :pdfurl=\"responsePdfUrl\" :uni_key=\"'response'\"></pdfView>-->\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<PdfViewImproved\r\n\t\t\t\t\t\t\tref=\"response\"\r\n\t\t\t\t\t\t\t:pdfurl=\"responsePdfUrl\"\r\n\t\t\t\t\t\t\t:page-height=\"800\"\r\n\t\t\t\t\t\t\t:buffer-size=\"2\"\r\n\t\t\t\t\t\t\t@render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"divider\"></div>\r\n\t\t<div class=\"right-panel\">\r\n\t\t\t<div class=\"right-header\">\r\n\t\t\t\t<el-select\r\n\t\t\t\t\tstyle=\"width:100%\"\r\n\t\t\t\t\tv-model=\"selectedSupplierName\"\r\n\t\t\t\t\tplaceholder=\"请选择供应商\"\r\n\t\t\t\t\t:disabled=\"isAnyPdfRendering()\"\r\n\t\t\t\t\t@change=\"handleSupplierChange\">\r\n\t\t\t\t\t<el-option v-for=\"item in supplierOptions\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n\t\t\t\t\t</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"right-content\" v-if=\"isResponseVisible\">\r\n\t\t\t\t<!-- PDF渲染状态提示 -->\r\n\t\t\t\t<div v-if=\"responsePdfUrl && !responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t\t\t<i class=\"el-icon-loading\"></i>\r\n\t\t\t\t\t<span>响应文件正在渲染中，请稍候...</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-else-if=\"responsePdfUrl && responsePdfRendered\" class=\"render-status-tip success\">\r\n\t\t\t\t\t<i class=\"el-icon-success\"></i>\r\n\t\t\t\t\t<span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div v-for=\"(item, index) in scoringSystem.uitems\" :key=\"index\" class=\"factor-item\"\r\n\t\t\t\t\t@mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t<!-- 悬浮框 -->\r\n\t\t\t\t\t<div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t\t\tclass=\"factor-tooltip\"\r\n\t\t\t\t\t\t@mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t\t<div class=\"tooltip-header\">\r\n\t\t\t\t\t\t\t<div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div class=\"factors\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"factor-title\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t\t\t@click=\"jumpToFactorPage(item)\">\r\n\t\t\t\t\t\t\t\t{{ item.itemName }}\r\n\t\t\t\t\t\t\t\t<i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"factor-radio-group\">\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-input v-if=\"(ratingStateMap[item.entMethodItemId].state == 0)\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t<span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t{{checkResultLabel[item.itemName]}}</span>\r\n\t\t\t\t\t\t<div class=\"factor-divider\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"right-btns\">\r\n\t\t\t\t\t<!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n\t\t\t\t\t<div><el-button class=\"item-button-little\" @click=\"submitRating\">提交</el-button></div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"review-content\">\r\n\t\t\t\t\t<div class=\"review-title\">评审内容：</div>\r\n\t\t\t\t\t<div class=\"review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<div class=\"right-content\" v-else>\r\n\t\t\t\t<!-- PDF渲染状态提示 -->\r\n\t\t\t\t<div v-if=\"procurementPdfUrl && !procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t\t\t<i class=\"el-icon-loading\"></i>\r\n\t\t\t\t\t<span>采购文件正在渲染中，请稍候...</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-else-if=\"procurementPdfUrl && procurementPdfRendered\" class=\"render-status-tip success\">\r\n\t\t\t\t\t<i class=\"el-icon-success\"></i>\r\n\t\t\t\t\t<span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div v-for=\"(item, index) in pageProcurement\" :key=\"index\" class=\"factor-item\"\r\n\t\t\t\t\t@mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t<!-- 悬浮框 -->\r\n\t\t\t\t\t<div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t\t\tclass=\"factor-tooltip\"\r\n\t\t\t\t\t\t@mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t\t<div class=\"tooltip-header\">\r\n\t\t\t\t\t\t\t<div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div class=\"factors\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"factor-title\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t\t\t@click=\"jumpToFactorPage(item)\">\r\n\t\t\t\t\t\t\t\t{{ item.itemName }}\r\n\t\t\t\t\t\t\t\t<i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"factor-radio-group\">\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-input v-if=\"(ratingStateMap[item.entMethodItemId].state == 0)\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t<span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t{{checkResultLabel[item.itemName]}}</span>\r\n\t\t\t\t\t\t<div class=\"factor-divider\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"right-btns\">\r\n\t\t\t\t\t<!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n\t\t\t\t\t<div><el-button class=\"item-button-little\" @click=\"submitRating\">提交</el-button></div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"review-content\">\r\n\t\t\t\t\t<div class=\"review-title\">评审内容：</div>\r\n\t\t\t\t\t<div class=\"review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tsupplierInfo,\r\n\tapprovalProcess,\r\n\tscoringFactors,\r\n\tcheckReviewSummary,\r\n\tfilesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\nimport { resDocReviewFactorsDecision } from \"@/api/docResponse/entInfo\";\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsupplierOptions: [], // 供应商下拉选项\r\n\t\t\tscoringSystem: [], // 当前评分体系\r\n\t\t\tselectedFactorNode: {}, // 当前选中评分项\r\n\t\t\tselectedSupplierName: \"\", // 当前选中供应商名称\r\n\t\t\tselectedSupplier: {}, // 当前选中供应商对象\r\n\t\t\texpertInfo: {}, // 专家信息\r\n\t\t\tratingStateMap: {}, // 评分项状态与原因\r\n\t\t\tfileInfo: {}, // 文件信息\r\n\t\t\tisResponseVisible: false, // 是否显示响应文件\r\n\t\t\tisProcurementVisible: false, // 是否显示采购文件\r\n\t\t\tisDoubleView: false, // 是否对比显示\r\n\t\t\t\r\n\t\t\tentDocResponsePage: {}, // 响应文件页码信息\r\n\t\t\t\r\n\t\t\tfactorDetailList: [], // 评分项详情\r\n\t\t\tfactorsPageMap: {}, // 评分项页码信息\r\n\t\t\t\r\n\t\t\tentDocProcurementPage: {}, // 采购文件页码信息\r\n\t\t\tpageProcurement:[], // 采购文件的评分项\r\n\t\t\tattachmentsList: [], // 文件列表\r\n\t\t\t\r\n\t\t\tsupplierFactorPageMap: {}, // 当前供应商评分项页码\r\n\t\t\tresponsePdfUrl: null, // 响应文件PDF路径\r\n\t\t\tprocurementPdfUrl: null, // 采购文件PDF路径\r\n\r\n\t\t\t// 按钮状态管理\r\n\t\t\tactiveButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n\t\t\t// PDF渲染状态管理\r\n\t\t\tresponsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t\t\tprocurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n\t\t\thelpImageList: [\"/evalution/help.jpg\"], // 步骤图片\r\n\t\t\tfactorKeyMap: { // 评分项与后端字段映射\r\n\t\t\t\t\"特定资格要求\": \"zgzs\",\r\n\t\t\t\t\"响应内容\": \"jsplb\",\r\n\t\t\t\t\"采购需求\": \"jsplb\",\r\n\t\t\t\t\"供货期限\": \"ghqx\",\r\n\t\t\t\t\"投标报价\": \"tbbj\"\r\n\t\t\t},\r\n\t\t\tcheckResult: {}, // 系统初验结果\r\n\t\t\tcheckResultLabel: { // 系统初验结果名称\r\n\t\t\t\t\"符合《中华人民共和国政府采购法》第二十二条规定\": \"系统初验通过\",\r\n\t\t\t\t\"特定资格要求\": \"系统初验通过\",\r\n\t\t\t\t\"信用查询\": \"系统初验通过\",\r\n\t\t\t\t\"响应人名称\": \"系统初验通过\",\r\n\t\t\t\t\"响应内容\": \"系统初验通过\",\r\n\t\t\t\t\"采购需求\": \"系统初验通过\",\r\n\t\t\t\t\"供货期限\": \"系统初验通过\",\r\n\t\t\t\t\"投标报价\": \"系统初验通过\"\r\n\t\t\t},\r\n\r\n\t\t\t// 悬停状态管理\r\n\t\t\thoveredFactorNode: null, // 悬停时的评分项\r\n\t\t\ttooltipTimer: null, // 悬浮框显示定时器\r\n\t\t};\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// ========== 评分相关 ==========\r\n\t\t/**\r\n\t\t * 系统初验结果判断\r\n\t\t * @param {string} factorName 评分项名称\r\n\t\t * @returns {string} 1-通过 0-未通过\r\n\t\t */\r\n\t\tgetCheckResultState(factorName) {\r\n\t\t\tif (!this.checkResult || Object.keys(this.checkResult).length === 0) return \"\"; // 如果没有系统初验结果，则返回空\r\n\t\t\tlet state = \"1\";\r\n\t\t\tconst key = this.factorKeyMap[factorName];\r\n\t\t\tif (key) {\r\n\t\t\t\tstate = this.checkResult[key];\r\n\t\t\t\tif (factorName === \"投标报价\" && state === \"1\") {\r\n\t\t\t\t\tstate = this.checkResult[\"mxbjb\"];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (state === \"0\") {\r\n\t\t\t\tthis.checkResultLabel[factorName] = \"系统初验未通过\";\r\n\t\t\t} else {\r\n\t\t\t\tstate = \"1\";\r\n\t\t\t\tthis.checkResultLabel[factorName] = \"系统初验通过\";\r\n\t\t\t}\r\n\t\t\treturn state;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 校验所有评分项是否填写完整\r\n\t\t * @returns {boolean} 是否全部填写\r\n\t\t */\r\n\t\tvalidateAllRatings() {\r\n\t\t\tfor (const item of this.scoringSystem.uitems) {\r\n\t\t\t\tconst state = this.ratingStateMap[item.entMethodItemId].state;\r\n\t\t\t\tconst reason = this.ratingStateMap[item.entMethodItemId].reason;\r\n\t\t\t\t// 评分结果未填写\r\n\t\t\t\tif (state === null || state === '') {\r\n\t\t\t\t\t// this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t\t// 不通过但未填写原因 - 将评审项设置为空，然后继续执行后续流程\r\n\t\t\t\tif (state === \"0\" && (!reason || reason.trim() === '')) {\r\n\t\t\t\t\t// 将此评审项设置为空（未评审状态）\r\n\t\t\t\t\tthis.ratingStateMap[item.entMethodItemId].state = null;\r\n\t\t\t\t\tthis.ratingStateMap[item.entMethodItemId].reason = \"\";\r\n\t\t\t\t\tconsole.log(`${item.itemName}评审不通过但未填写备注，已将该评审项设置为空`);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\r\n\t\t// 生成保存数据\r\n\t\tgenerateSaveData() {\r\n\t\t\tconst ratingCopy = JSON.parse(JSON.stringify(this.ratingStateMap)); // 评分项状态\r\n\t\t\tconst data = []; // 保存数据\r\n\t\t\tfor (const item of this.scoringSystem.uitems) { // 遍历评分项\r\n\t\t\t\tconst itemId = item.entMethodItemId; // 评分项ID\r\n\t\t\t\tconst evaluationResult = ratingCopy[itemId].state; // 评分项状态\r\n\t\t\t\tif (evaluationResult === null || evaluationResult === \"\") continue; // 如果评分项状态为空，则跳过\r\n\t\t\t\t// 注意：不通过原因的校验已经在validateAllRatings中处理，这里只需要构建数据\r\n\t\t\t\tconst evaluationRemark = ratingCopy[itemId].reason || \"\"; // 评分项备注\r\n\t\t\t\tdata.push({\r\n\t\t\t\t\tscoringMethodUitemId: itemId, // 评分项ID\r\n\t\t\t\t\texpertResultId: this.expertInfo.resultId, // 专家ID\r\n\t\t\t\t\tentId: this.selectedSupplier.bidderId, // 供应商ID\r\n\t\t\t\t\tevaluationResult, // 评分项状态\r\n\t\t\t\t\tevaluationRemark // 评分项备注\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\treturn data; // 返回保存数据\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 临时保存评分结果\r\n\t\t * @returns {Promise}\r\n\t\t */\r\n\t\tsaveRatingTemp() {\r\n\t\t\t// 先校验所有评分项是否填写完整\r\n\t\t\tif (!this.validateAllRatings()) {\r\n\t\t\t\treturn Promise.resolve({ code: 0, success: false }); // 校验失败\r\n\t\t\t}\r\n\r\n\t\t\tconst data = this.generateSaveData(); // 生成保存数据\r\n\t\t\tif (data.length > 0) {\r\n\t\t\t\treturn scoringFactors(data).then(response => {\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tthis.$message.success(\"保存成功\");\r\n\t\t\t\t\t\treturn { code: 200, success: true };\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t\t\treturn { code: response.code, success: false };\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch((error) => {\r\n\t\t\t\t\tthis.$message.error(\"保存失败\");\r\n\t\t\t\t\treturn { code: 0, success: false };\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\treturn Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 提交评分结果\r\n\t\t */\r\n\t\tsubmitRating() {\r\n\t\t\tthis.saveRatingTemp().then((saveResult) => {\r\n\t\t\t\t// 检查保存结果，如果校验失败则不继续提交\r\n\t\t\t\tif (!saveResult || saveResult.success === false) {\r\n\t\t\t\t\treturn; // 校验失败，不继续提交流程\r\n\t\t\t\t}\r\n\t\t\t\tthis.checkAndSubmitReview();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 检查并提交评审汇总\r\n\t\tcheckAndSubmitReview() {\r\n\t\t\t\tconst data = {\r\n\t\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\t\texpertResultId: this.expertInfo.resultId,\r\n\t\t\t\t\tscoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n\t\t\t\t};\r\n\t\t\t\tcheckReviewSummary(data).then((response) => {\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tthis.updateExpertScoreStatus(); // 修改专家进度状态\r\n\t\t\t\t\t\tthis.$emit(\"send\", \"two\"); // 跳转至二次报价\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 修改专家进度状态\r\n\t\tupdateExpertScoreStatus() {\r\n\t\t\tconst status = {\r\n\t\t\t\tevalExpertScoreInfoId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\")).evalExpertScoreInfoId,\r\n\t\t\t\tevalState: 1,\r\n\t\t\t};\r\n\t\t\teditEvalExpertScoreInfo(status).then((res) => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tthis.$message.success(\"提交成功\");\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 初始化相关 ==========\r\n\t\t/**\r\n\t\t * 初始化页面数据\r\n\t\t */\r\n\t\tinitPage() {\r\n\t\t\tthis.initExpertInfo();// 初始化专家信息\r\n\t\t\tthis.initEntDocResponsePage(); // 初始化响应文件页码信息\r\n\t\t\tthis.initEntDocProcurementPage(); // 初始化采购文件页码信息\r\n\t\t\tthis.loadSupplierOptions();// 加载供应商下拉选项\r\n\t\t\tthis.loadScoringSystem(); // 加载评分体系\r\n\t\t\tthis.loadFiles(); // 加载文件信息\r\n\t\t},\r\n\t\t// 初始化专家信息\r\n\t\tinitExpertInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst expertInfoStr = localStorage.getItem(\"expertInfo\");\r\n\t\t\t\tif (expertInfoStr) {\r\n\t\t\t\t\tthis.expertInfo = JSON.parse(expertInfoStr);\r\n\t\t\t\t\tconsole.log(\"专家信息已初始化\", this.expertInfo);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn(\"localStorage中未找到expertInfo\");\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"初始化专家信息失败:\", error);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 初始化响应文件页码信息\r\n\t\tinitEntDocResponsePage() {\r\n\t\t\tthis.entDocResponsePage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n\t\t},\r\n\t\t// 初始化采购文件页码信息\r\n\t\tinitEntDocProcurementPage() {\r\n\t\t\tthis.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n\t\t},\r\n\t\t// 加载供应商下拉选项\r\n\t\tloadSupplierOptions() {\r\n\t\t\tsupplierInfo({ projectId: this.$route.query.projectId }).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.supplierOptions = response.rows.filter(item => item.isAbandonedBid === 0); // 过滤掉被放弃的投标\r\n\t\t\t\t\tconsole.log(this.supplierOptions);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 加载评分体系及初始化评分项状态\r\n\t\tloadScoringSystem() {\r\n\t\t\tapprovalProcess(this.$route.query.projectId, this.expertInfo.resultId).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t// 文件列表\r\n\t\t\t\t\tthis.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\r\n\t\t\t\t\tthis.scoringSystem = response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n\t\t\t\t\t\titem => item.scoringMethodItemId == this.$route.query.scoringMethodItemId\r\n\t\t\t\t\t); // 获取当前评分项\r\n\t\t\t\t\tlocalStorage.setItem(\r\n\t\t\t\t\t\t\"evalProjectEvaluationProcess\",\r\n\t\t\t\t\t\tJSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n\t\t\t\t\t); // 保存评分体系\r\n\t\t\t\t\tconsole.log(this.scoringSystem);\r\n\t\t\t\t\tthis.initRatingStateMapBySystem(); // 初始化评分项状态\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 初始化评分项状态（根据评分体系）\r\n\t\tinitRatingStateMapBySystem() {\r\n\t\t\tthis.ratingStateMap = this.scoringSystem.uitems.reduce((acc, item) => {\r\n\t\t\t\tacc[item.entMethodItemId] = { state: null, reason: \"\" };\r\n\t\t\t\treturn acc;\r\n\t\t\t}, {}); // 初始化评分项状态\r\n\t\t},\r\n\t\t// 加载文件信息\r\n\t\tloadFiles() {\r\n\t\t\tfilesById(this.$route.query.projectId).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.fileInfo = response.data; // 文件信息\r\n\t\t\t\t\t// 🎯 需求一：不自动设置采购文件URL，只在用户点击时才设置\r\n\t\t\t\t\t// if (this.fileInfo.tenderNoticeFilePath) {\r\n\t\t\t\t\t// \tthis.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// 不自动设置响应文件URL，只在需要时才设置\r\n\t\t\t\t\t// if (this.fileInfo.file) {\r\n\t\t\t\t\t// \tthis.responsePdfUrl = this.fileInfo.file[0]; // 响应文件\r\n\t\t\t\t\t// }\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 供应商相关 ==========\r\n\t\t/**\r\n\t\t * 供应商切换时处理\r\n\t\t * @param {string} supplierName 供应商名称\r\n\t\t */\r\n\t\thandleSupplierChange(supplierName) {\r\n\t\t\t// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换供应商\r\n\t\t\tif (this.isAnyPdfRendering()) {\r\n\t\t\t\tthis.$message.warning(\"PDF文件正在渲染中，请稍候再切换供应商\");\r\n\t\t\t\t// 恢复之前的选择\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.selectedSupplierName = this.selectedSupplier.bidderName || \"\";\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.isResponseVisible = true\r\n\t\t\tif (Object.keys(this.selectedSupplier).length !== 0) {\r\n\t\t\t\tthis.saveRatingTemp();\r\n\t\t\t}\r\n\t\t\tthis.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName) || {}; // 获取当前供应商\r\n\t\t\tthis.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码\r\n\t\t\tthis.loadSupplierFactorDetail(supplierName); // 加载当前供应商评分项详情\r\n\t\t\tthis.loadSupplierCheckResult(); // 加载当前供应商系统初验结果\r\n\t\t\tthis.showResponseFile(); // 显示响应文件\r\n\t\t},\r\n\t\t// 加载当前供应商评分项详情\r\n\t\tloadSupplierFactorDetail(bidderName) {\r\n\t\t\tthis.clearRatingStateMap();\r\n\t\t\t\r\n\t\t\tconst detailData = {\r\n\t\t\t\texpertResultId: this.expertInfo.resultId,\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tscoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n\t\t\t};\r\n\t\t\tgetDetailByPsxx(detailData).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.factorDetailList = response.data;\r\n\t\t\t\t\tconst factor = this.factorDetailList.find(item => item.bidderName === bidderName)?.evalExpertEvaluationDetails;\r\n\t\t\t\t\tif (factor) {\r\n\t\t\t\t\t\tthis.setRatingStateMapByFactor(factor);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.clearRatingStateMap();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 根据评分详情设置评分项状态\r\n\t\tsetRatingStateMapByFactor(factor) {\r\n\t\t\tfactor.forEach(item => {\r\n\t\t\t\tthis.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark;\r\n\t\t\t\tthis.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult;\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 加载当前供应商系统初验结果\r\n\t\tloadSupplierCheckResult() {\r\n\t\t\tconst reviewData = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tentId: this.selectedSupplier.bidderId,\r\n\t\t\t};\r\n\t\t\tresDocReviewFactorsDecision(reviewData).then((res) => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tthis.checkResult = res.data;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 初始化评分项状态（清空）\r\n\t\tclearRatingStateMap() {\r\n\t\t\tObject.keys(this.ratingStateMap).forEach(key => {\r\n\t\t\t\tthis.ratingStateMap[key].state = null;\r\n\t\t\t\tthis.ratingStateMap[key].reason = \"\";\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 文件相关 ==========\r\n\t\t/**\r\n\t\t * 显示响应文件\r\n\t\t */\r\n\t\tshowResponseFile() {\r\n\t\t\tif (Object.keys(this.selectedSupplier).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请选择供应商\");\r\n\t\t\t} else {\r\n\t\t\t\t// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换\r\n\t\t\t\tif (this.isAnyPdfRendering()) {\r\n\t\t\t\t\tthis.$message.warning(\"PDF文件正在渲染中，请稍候再切换\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.activeButton = 'response'; // 设置当前激活按钮\r\n\t\t\t\tthis.isDoubleView = false; // 关闭对比\r\n\t\t\t\tthis.isProcurementVisible = false; // 关闭采购文件\r\n\t\t\t\tthis.isResponseVisible = true; // 显示响应文件\r\n\r\n\t\t\t\t// 🎯 只在用户点击时才设置响应文件URL，开始渲染\r\n\t\t\t\tthis.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件\r\n\r\n\t\t\t\t// 右侧评分项显示为响应文件的评分项\r\n\t\t\t\tif (Object.keys(this.selectedSupplier).length !== 0) {\r\n\t\t\t\t\tthis.saveRatingTemp();\r\n\t\t\t\t}\r\n\t\t\t\tthis.selectedSupplier = this.supplierOptions.find(item => item.bidderName === this.selectedSupplier.bidderName) || {}; // 获取当前供应商\r\n\t\t\t\tthis.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码\r\n\t\t\t\tthis.loadSupplierFactorDetail(this.selectedSupplier.bidderName); // 加载当前供应商评分项详情\r\n\t\t\t\tthis.loadSupplierCheckResult(); // 加载当前供应商系统初验结果\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 文件对比显示\r\n\t\t */\r\n\t\tshowFileContrast() {\r\n\t\t\tif (Object.keys(this.selectedSupplier).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请选择供应商\");\r\n\t\t\t} else {\r\n\t\t\t\t// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换\r\n\t\t\t\tif (this.isAnyPdfRendering()) {\r\n\t\t\t\t\tthis.$message.warning(\"PDF文件正在渲染中，请稍候再切换\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.activeButton = 'contrast'; // 设置当前激活按钮\r\n\t\t\t\tthis.isDoubleView = true;\r\n\t\t\t\tthis.isProcurementVisible = true;\r\n\t\t\t\tthis.isResponseVisible = true;\r\n\r\n\t\t\t\t// 🎯 只在用户点击对比时才设置文件URL，开始渲染\r\n\t\t\t\tthis.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件\r\n\t\t\t\tif (this.fileInfo.tenderNoticeFilePath) {\r\n\t\t\t\t\tthis.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 查看采购文件\r\n\t\t */\r\n\t\tshowProcurementFile() {\r\n\t\t\t// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换\r\n\t\t\tif (this.isAnyPdfRendering()) {\r\n\t\t\t\tthis.$message.warning(\"PDF文件正在渲染中，请稍候再切换\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.activeButton = 'procurement'; // 设置当前激活按钮\r\n\t\t\tthis.isDoubleView = false;\r\n\t\t\tthis.isResponseVisible = false;\r\n\t\t\tthis.isProcurementVisible = true;\r\n\r\n\t\t\t// 🎯 需求一：只在用户点击时才设置采购文件URL，开始渲染\r\n\t\t\tif (this.fileInfo.tenderNoticeFilePath) {\r\n\t\t\t\tthis.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件\r\n\t\t\t}\r\n\r\n\t\t\t// 右侧评分项显示为采购文件的评分项\r\n\t\t\tlet pageProcurementArr = [];\r\n\t\t\tfor (let item in this.entDocProcurementPage){\r\n\t\t\t\tpageProcurementArr.push({\r\n\t\t\t\t\titemName: item,\r\n\t\t\t\t\tjumpToPage: this.entDocProcurementPage[item]\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log(this.scoringSystem.uitems);\r\n\t\t\tconsole.log(pageProcurementArr)\r\n\t\t\tthis.pageProcurement = [];\r\n\t\t\tfor (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n\t\t\t\tfor (let j = 0; j < pageProcurementArr.length;j++){\r\n\t\t\t\t\tif (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n\t\t\t\t\t\tthis.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tconsole.log(this.pageProcurement)\r\n\t\t},\r\n\r\n\t\t// ========== 页面跳转相关 ==========\r\n\t\t/**\r\n\t\t * 跳转到评分项对应页码\r\n\t\t * @param {Object} factorItem 评分项对象\r\n\t\t */\r\n\t\tjumpToFactorPage(factorItem) {\r\n\t\t\t// 检查PDF是否渲染完成\r\n\t\t\tif (!this.canJumpToPage()) {\r\n\t\t\t\tthis.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.selectedFactorNode = factorItem; // 设置当前选中因子\r\n\r\n\t\t\t// 如果只显示采购文件，使用采购文件页码信息\r\n\t\t\tif (this.isProcurementVisible && !this.isResponseVisible) {\r\n\t\t\t\tif (!this.procurementPdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (factorItem.jumpToPage) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页\r\n\t\t\t\t} else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果显示响应文件或对比模式，需要选择供应商\r\n\t\t\tif (!this.supplierFactorPageMap || Object.keys(this.supplierFactorPageMap).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请先选择供应商\"); // 未选供应商提示\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 跳转到响应文件对应页码\r\n\t\t\tif (this.isResponseVisible && this.$refs.response) {\r\n\t\t\t\tif (!this.responsePdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.response.skipPage(this.supplierFactorPageMap[this.selectedFactorNode.itemName]); // 响应文件跳页\r\n\t\t\t}\r\n\r\n\t\t\t// 跳转到采购文件对应页码\r\n\t\t\tif (this.isProcurementVisible && this.$refs.procurement) {\r\n\t\t\t\tif (!this.procurementPdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n\t\t\t\tif (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n\t\t\t\t\t// 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 检查是否可以跳转页面\r\n\t\t * @returns {boolean} 是否可以跳转\r\n\t\t */\r\n\t\tcanJumpToPage() {\r\n\t\t\t// 如果只显示采购文件\r\n\t\t\tif (this.isProcurementVisible && !this.isResponseVisible) {\r\n\t\t\t\treturn this.procurementPdfRendered;\r\n\t\t\t}\r\n\t\t\t// 如果只显示响应文件\r\n\t\t\tif (this.isResponseVisible && !this.isProcurementVisible) {\r\n\t\t\t\treturn this.responsePdfRendered;\r\n\t\t\t}\r\n\t\t\t// 如果对比模式（两个都显示）\r\n\t\t\tif (this.isResponseVisible && this.isProcurementVisible) {\r\n\t\t\t\treturn this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 🎯 需求二：检查是否有任何PDF正在渲染\r\n\t\t * @returns {boolean} 是否有PDF正在渲染\r\n\t\t */\r\n\t\tisAnyPdfRendering() {\r\n\t\t\t// 检查当前显示的PDF是否正在渲染\r\n\t\t\tif (this.isProcurementVisible && this.procurementPdfUrl && !this.procurementPdfRendered) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\tif (this.isResponseVisible && this.responsePdfUrl && !this.responsePdfRendered) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 处理PDF渲染状态变化\r\n\t\t * @param {boolean} isRendered 是否渲染完成\r\n\t\t * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t\t */\r\n\t\thandlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t\tif (pdfType === 'response') {\r\n\t\t\t\tthis.responsePdfRendered = isRendered;\r\n\t\t\t} else if (pdfType === 'procurement') {\r\n\t\t\t\tthis.procurementPdfRendered = isRendered;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (isRendered) {\r\n\t\t\t\tconsole.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 跳转到二次报价\r\n\t\t */\r\n\t\tgoToSecondOffer() {\r\n\t\t\tconst query = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tzjhm: this.$route.query.zjhm,\r\n\t\t\t\tscoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")),\r\n\t\t\t};\r\n\t\t\tthis.$router.push({ path: \"/secondOffer\", query });\r\n\t\t},\r\n\t\t/**\r\n\t\t * 跳转到询标\r\n\t\t */\r\n\t\tgoToBidInquiry() {\r\n\t\t\tconst query = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tzjhm: this.$route.query.zjhm,\r\n\t\t\t\tscoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")),\r\n\t\t\t};\r\n\t\t\tthis.$router.push({ path: \"/bidInquiry\", query });\r\n\t\t},\r\n\t\t/**\r\n\t\t * 获取因素对应页码\r\n\t\t */\r\n\t\tloadFactorsPageMap() {\r\n\t\t\tthis.factorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 下载文件\r\n\t\t * @param {Object} item - 文件对象\r\n\t\t */\r\n\t\tdownloadFile(item) {\r\n\t\t\tthis.$download.zip(item.filePath, item.fileName);\r\n\t\t},\r\n\r\n\t\t// ========== 悬停相关 ==========\r\n\t\t/**\r\n\t\t * 显示评分项悬浮框\r\n\t\t * @param {Object} factorItem 评分项对象\r\n\t\t */\r\n\t\tshowFactorTooltip(factorItem) {\r\n\t\t\tif (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t}\r\n\r\n\t\t\t// 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t\tthis.tooltipTimer = setTimeout(() => {\r\n\t\t\t\tthis.hoveredFactorNode = factorItem;\r\n\t\t\t}, 300); // 300ms延迟\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 隐藏评分项悬浮框\r\n\t\t */\r\n\t\thideFactorTooltip() {\r\n\t\t\t// 清除定时器\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t\tthis.tooltipTimer = null;\r\n\t\t\t}\r\n\r\n\t\t\t// 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.hoveredFactorNode = null;\r\n\t\t\t}, 100);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t\t */\r\n\t\tclearTooltipTimer() {\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t\tthis.tooltipTimer = null;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n},\r\n\r\n\tmounted() {\r\n\t\tthis.initPage();\r\n\t\tthis.loadFactorsPageMap();\r\n\t},\r\n\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.main-container-one {\r\n\tmin-height: 57vh;\r\n\tdisplay: flex;\r\n}\r\n.left-panel {\r\n\tmin-height: 57vh;\r\n\twidth: 79%;\r\n}\r\n.header-bar {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tborder-bottom: 2px solid #176ADB;\r\n\tpadding: 15px 20px;\r\n}\r\n.header-title {\r\n\tdisplay: flex;\r\n\theight: 36px;\r\n\tfont-weight: 700;\r\n\tfont-size: 24px;\r\n\tcolor: #333;\r\n}\r\n.header-steps {\r\n\tdisplay: grid;\r\n\tjustify-items: center;\r\n\tposition: relative;\r\n\tbottom: -30px;\r\n}\r\n.steps-tip {\r\n\tfont-size: 12px;\r\n}\r\n.steps-img {\r\n\twidth: 80px;\r\n\theight: 30px;\r\n\tmargin-right: 20px;\r\n}\r\n.header-btns {\r\n\ttext-align: right;\r\n}\r\n.header-btns-group {\r\n\tmargin-top: 20px;\r\n}\r\n.item-button.main {\r\n\tbackground-color: #176ADB;\r\n\tcolor: #fff;\r\n\tborder: 1px solid #176ADB;\r\n}\r\n.pdf-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\theight: 100%;\r\n\tmin-height: 600px;\r\n}\r\n.pdf-view {\r\n\twidth: 49%;\r\n}\r\n.border-right {\r\n\tborder-right: 1px solid #176ADB;\r\n}\r\n.border-left {\r\n\tborder-left: 1px solid #176ADB;\r\n}\r\n.divider {\r\n\tmin-height: 57vh;\r\n\twidth: 1%;\r\n\tbackground-color: #F5F5F5;\r\n}\r\n.right-panel {\r\n\tmin-height: 57vh;\r\n\twidth: 20%;\r\n}\r\n.right-header {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tborder-bottom: 2px solid #176ADB;\r\n\tpadding: 15px 20px;\r\n}\r\n.right-content {\r\n\tpadding: 15px 20px;\r\n}\r\n.factor-item {\r\n\tmargin-bottom: 10px;\r\n}\r\n.factors {\r\n\tdisplay: flex;\r\n\tjustify-content: flex-start;\r\n\tflex-wrap: wrap;\r\n\talign-items: center;\r\n\tmargin-bottom: 10px;\r\n}\r\n.factor-title {\r\n\tcursor: pointer;\r\n\tfont-family: SourceHanSansSC-Bold;\r\n\tfont-weight: 700;\r\n\tfont-size: 16px;\r\n\tcolor: #333;\r\n\tletter-spacing: 0;\r\n\twidth: auto;\r\n\ttext-align: left;\r\n\ttransition: all 0.3s ease;\r\n\tpadding: 4px 8px;\r\n\tborder-radius: 4px;\r\n\r\n\t&:hover {\r\n\t\tbackground-color: #f0f8ff;\r\n\t\tcolor: #176ADB;\r\n\t\ttransform: translateX(2px);\r\n\t}\r\n}\r\n.factor-radio-group {\r\n\tdisplay: flex;\r\n\twidth: 100%;\r\n\tjustify-content: flex-end;\r\n}\r\n.factor-divider {\r\n\theight: 1px;\r\n\tbackground-color: #DCDFE6;\r\n\tmargin-top: 10px;\r\n}\r\n.right-btns {\r\n\tdisplay: flex;\r\n\tmargin: 32px 0;\r\n\tjustify-content: space-evenly;\r\n}\r\n.review-content {\r\n\ttext-align: left;\r\n\tfont-size: 14px;\r\n}\r\n.review-title {\r\n\tfont-family: SourceHanSansSC-Bold;\r\n\tfont-weight: 700;\r\n\tfont-size: 15px;\r\n\tcolor: #176ADB;\r\n\tletter-spacing: 0;\r\n}\r\n.review-html {\r\n\tpadding: 6px 30px;\r\n}\r\n.item-button {\r\n\tborder: 1px solid #979797;\r\n\twidth: 150px;\r\n\theight: 36px;\r\n\tmargin: 0 10px;\r\n\tfont-weight: 700;\r\n\tfont-size: 17px;\r\n\tborder-radius: 6px;\r\n\tcolor: #333;\r\n\t&:hover {\r\n\t\tcolor: #333;\r\n\t}\r\n}\r\n.qualification-blue-btn {\r\n\tbackground-color: #176ADB !important;\r\n\tcolor: #fff !important;\r\n\tborder: 1px solid #176ADB !important;\r\n}\r\n.qualification-blue-btn-active {\r\n\tbackground-color: #FF6B35 !important;\r\n\tcolor: #fff !important;\r\n\tborder: 1px solid #FF6B35 !important;\r\n\tbox-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n\twidth: 124px;\r\n\theight: 36px;\r\n\tfont-weight: 700;\r\n\tfont-size: 18px;\r\n\tcolor: #fff;\r\n\tbackground-color: #176ADB;\r\n\t&:hover {\r\n\t\tcolor: #fff;\r\n\t}\r\n}\r\n.text {\r\n\t::v-deep .el-textarea__inner {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 0;\r\n\t\tborder: 1px solid #f5f5f5;\r\n\t}\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\t.fileItem {\r\n\t\ttransition: all 0.3s ease;\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\r\n\t\t::v-deep .el-card__body {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n\r\n// 🎯 需求二：PDF渲染期间禁用状态样式\r\n.item-button:disabled {\r\n\topacity: 0.6 !important;\r\n\tcursor: not-allowed !important;\r\n\tbackground-color: #f5f5f5 !important;\r\n\tcolor: #c0c4cc !important;\r\n\tborder-color: #e4e7ed !important;\r\n}\r\n\r\n.el-select.is-disabled .el-input__inner {\r\n\tbackground-color: #f5f5f5;\r\n\tborder-color: #e4e7ed;\r\n\tcolor: #c0c4cc;\r\n\tcursor: not-allowed;\r\n}\r\n</style>\r\n"]}]}