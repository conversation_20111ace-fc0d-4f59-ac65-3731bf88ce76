{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\agentRoom.vue?vue&type=style&index=0&id=bd926d82&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\agentRoom.vue", "mtime": 1753950779107}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLy8g6KaG55uWZWwtY2FyZCBib2R55YaF6L656LedDQo6OnYtZGVlcCAuZWwtY2FyZF9fYm9keSB7DQoJcGFkZGluZzogMDsNCn0NCg=="}, {"version": 3, "sources": ["agentRoom.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqYA;AACA;AACA;AACA", "file": "agentRoom.vue", "sourceRoot": "src/views/bidOpeningHall", "sourcesContent": ["<template>\r\n\t<!-- 开标室主页面 -->\r\n\t<div>\r\n\t\t<!-- 头部组件，负责流程状态展示和切换 -->\r\n\t\t<BidHeadtwo ref=\"head\" @updateStatus=\"handleStatus\"></BidHeadtwo>\r\n\t\t<div class=\"bidOpeningHall\">\r\n\t\t\t<!-- 主体区域，包含流程节点内容 -->\r\n\t\t\t<el-card id=\"main\" class=\"box-card\">\r\n\t\t\t\t<!-- 平台当前时间与聊天室连接状态 -->\r\n\t\t\t\t<div style=\"height: 10px;\">\r\n\t\t\t\t\t<div style=\"padding: 5px 0 0 20px; float: left;\">平台当前时间：<span >{{ currentTime }}</span></div>\r\n\t\t\t\t\t<div style=\"padding: 5px 20px 0 0; float: right;\">\r\n\t\t\t\t\t\t连接状态：<span :style=\"`color:${isLink ? 'green' : 'red'}`\">{{\r\n\t\t\t\t\t\t\tisLink ? \"已连接\" : \"已断连，请刷新重连\"\r\n\t\t\t\t\t\t}}</span>\r\n\t\t\t\t\t</div></div>\r\n\t\t\t\t<!-- 根据流程节点动态渲染不同子组件 -->\r\n\t\t\t\t<ready ref=\"ready\" v-if=\"node == 'ready' && projectInfo\" :projectInfo=\"projectInfo\" @sendMessage=\"operateSend\"></ready>\r\n\t\t\t\t<publicity ref=\"publicity\" v-if=\"node == 'publicity'\" :projectInfo=\"projectInfo\" @sendMessage=\"operateSend\"></publicity>\r\n\t\t\t\t<decryption ref=\"decryption\" v-if=\"node == 'decryption' && projectInfo\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" @sendMessage=\"operateSend\"></decryption>\r\n\t\t\t\t<bidAnnouncement ref=\"bidAnnouncement\" v-if=\"node == 'bidAnnouncement'\" @sendMessage=\"operateSend\"></bidAnnouncement>\r\n\t\t\t\t<end ref=\"end\" v-if=\"node == 'end'\" @sendMessage=\"operateSend\"></end>\r\n\t\t\t</el-card>\r\n\t\t\t<!-- 聊天室侧边栏 -->\r\n\t\t\t<el-card class=\"box-card\" style=\"width: 15%;\">\r\n\t\t\t\t<div class=\"im\">\r\n\t\t\t\t\t<div class=\"im-title\">{{ userInfo.nickName }}</div>\r\n\t\t\t\t\t<!-- 聊天内容区域 -->\r\n\t\t\t\t\t<div ref=\"messagesContainer\" class=\"im-content\" :style=\"{height: syncedHeight }\">\r\n\t\t\t\t\t\t<div v-for=\"(itemc, indexc) in recordContent\" :key=\"indexc\">\r\n\t\t\t\t\t\t\t<div class=\"sysMessage\" v-if=\"itemc.type == 0\">\r\n\t\t\t\t\t\t\t\t<!-- 系统消息（可扩展） -->\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div v-else>\r\n\t\t\t\t\t\t\t\t<!-- 他人消息 -->\r\n\t\t\t\t\t\t\t\t<div class=\"word\" v-if=\"itemc.sendId !== userInfo.entId\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"message_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{anonymous? \"*******\":itemc.sendName }}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"info-content\">{{ itemc.content }}</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"message_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<!-- 自己消息 -->\r\n\t\t\t\t\t\t\t\t<div class=\"word-my\" v-else>\r\n\t\t\t\t\t\t\t\t\t<div class=\"info\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"info-content\">{{ itemc.content }}</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"Sender_time\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<!-- 聊天输入与发送 -->\r\n\t\t\t\t\t<div class=\"im-operation\">\r\n\t\t\t\t\t\t<div style=\"margin-right:5px\">\r\n\t\t\t\t\t\t\t<el-input v-model=\"message\" placeholder=\"输入内容\"></el-input>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-button style=\"height: 36px;background: #176ADB;color:#fff\" @click=\"send\">发送</el-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</el-card>\r\n\t\t</div>\r\n\t\t<!-- 页脚 -->\r\n\t\t<Foot></Foot>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n// 引入各流程节点组件\r\nimport Ready from \"./agentComponent/ready.vue\";\r\nimport publicity from \"./agentComponent/publicity.vue\";\r\nimport decryption from \"./agentComponent/decryption.vue\";\r\nimport bidAnnouncement from \"./agentComponent/bidAnnouncement.vue\";\r\nimport end from \"./agentComponent/end.vue\";\r\n\r\n// 工具方法与API接口\r\nimport {\r\n\tformatDateOption,\r\n\tgetTodayStartWithDate,\r\n\tgetTodayEndWithDate,\r\n} from \"@/utils/index\";\r\nimport { bidInfo, chatHistory } from \"@/api/onlineBidOpening/info\";\r\nimport { getUserProfile } from \"@/api/system/user\";\r\nimport { getSystemTime } from \"@/api/bid/opening\";\r\n\r\nexport default {\r\n\tcomponents: { Ready, publicity, decryption, bidAnnouncement, end },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 当前流程节点\r\n\t\t\tnode: \"ready\",\r\n\t\t\t// 当前用户信息\r\n\t\t\tuserInfo: {},\r\n\t\t\t// 项目信息\r\n\t\t\tprojectInfo: null,\r\n\t\t\t// 接口基础地址\r\n\t\t\tbaseUrl: process.env.VUE_APP_BASE_API,\r\n\t\t\t// 聊天输入内容\r\n\t\t\tmessage: \"\",\r\n\t\t\t// 文本内容（用于日志等）\r\n\t\t\ttext_content: \"\",\r\n\t\t\t// websocket 实例\r\n\t\t\tws: null,\r\n\t\t\t// 是否匿名（根据流程节点控制）\r\n\t\t\tanonymous: true,\r\n\t\t\t// 聊天记录内容\r\n\t\t\trecordContent: [\r\n\t\t\t\t// 示例数据，实际会被接口数据覆盖\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 1,\r\n\t\t\t\t\tcontent: \"Nice to meet you.\",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:00:00\",\r\n\t\t\t\t\tSender: \"张三\",\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 2,\r\n\t\t\t\t\tcontent: \"Nice to meet you.too\",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:01:00\",\r\n\t\t\t\t\tSender: \"李四\",\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 2,\r\n\t\t\t\t\tcontent: \"How are you? \",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:02:00\",\r\n\t\t\t\t\tSender: \"李四\",\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tsendId: 1,\r\n\t\t\t\t\tcontent: \"I'am fine,Thank you.\",\r\n\t\t\t\t\tsendTime: \"2024-7-17 11:03:00\",\r\n\t\t\t\t\tSender: \"张三\",\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t\t// websocket连接状态\r\n\t\t\tisLink: false,\r\n\t\t\t// 聊天内容区高度（与主卡片同步）\r\n\t\t\tsyncedHeight: '450px', // 初始高度\r\n\t\t\t// 平台当前时间\r\n\t\t\tcurrentTime:null,\r\n\t\t\t// 时间更新定时器\r\n\t\t\ttimeInterval: null\r\n\t\t};\r\n\t},\r\n\twatch: {\r\n\t\t// 监听流程节点变化，动态调整聊天区高度\r\n\t\tnode: {\r\n\t\t\thandler() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tvar element = document.getElementById('main');\r\n\t\t\t\t\tconsole.log('element.clientHeight', element.offsetHeight);\r\n\t\t\t\t\tthis.syncedHeight = element.offsetHeight - 120 + 'px'\r\n\t\t\t\t}, 10);\r\n\r\n\t\t\t},\r\n\t\t\tdeep: true\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 初始化，获取项目信息和用户信息\r\n\t\tinit() {\r\n\t\t\t// 获取开标项目信息\r\n\t\t\tconst promise1 = bidInfo({\r\n\t\t\t\tbidOpeningTime: getTodayStartWithDate(),\r\n\t\t\t\tbidOpeningEndTime: getTodayEndWithDate(),\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t}).then((response) => {\r\n\t\t\t\tif (response.code == 200) {\r\n\t\t\t\t\tthis.projectInfo = response.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$modal.msgwarning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// 获取用户信息\r\n\t\t\tconst promise2 = getUserProfile().then((response) => {\r\n\t\t\t\tthis.userInfo = response.data;\r\n\t\t\t});\r\n\r\n\t\t\t// 两个接口都完成后，建立websocket连接\r\n\t\t\tPromise.all([promise1, promise2]).then((result) => {\r\n\t\t\t\tthis.join();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 处理流程节点切换\r\n\t\thandleStatus(data) {\r\n\t\t\t// 根据流程状态判断是否匿名\r\n\t\t\tthis.anonymous = this.$store.getters.agentBidOpenStatus >= 2 ? false : true;\r\n\t\t\tswitch (data) {\r\n\t\t\t\tcase \"签到\":\r\n\t\t\t\t\tthis.node = \"signIn\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"开标准备\":\r\n\t\t\t\t\tthis.node = \"ready\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"投标人公示\":\r\n\t\t\t\t\tthis.node = \"publicity\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"标书解密\":\r\n\t\t\t\t\tthis.node = \"decryption\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"唱标\":\r\n\t\t\t\t\tthis.node = \"bidAnnouncement\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"开标结束\":\r\n\t\t\t\t\tthis.node = \"end\";\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 节点更新通知，刷新头部状态\r\n\t\tupdateStatus() {\r\n\t\t\tthis.$refs.head.getBidStatus();\r\n\t\t},\r\n\t\t// 格式化开标时间显示 年-月-日\r\n\t\tformatBidOpeningTime(time) {\r\n\t\t\treturn formatDateOption(time, \"date\");\r\n\t\t},\r\n\t\t// 格式化开标时间显示 时-分-秒\r\n\t\tformatBidOpeningTimeTwo(time) {\r\n\t\t\treturn formatDateOption(time, \"time\");\r\n\t\t},\r\n\r\n\t\t// 建立websocket连接，处理心跳、消息、断线重连等\r\n\t\tjoin() {\r\n\t\t\tlet socketUrl = this.baseUrl.replace(\"http\", \"ws\");\r\n\t\t\tconsole.log(\"socketUrl\", socketUrl);\r\n\t\t\t// 拼接websocket地址\r\n\t\t\tthis.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n\t\t\tconst wsurl = this.url;\r\n\t\t\tthis.ws = new WebSocket(wsurl);\r\n\t\t\tconst self = this;\r\n\t\t\t// 心跳检测函数，防止连接超时断开\r\n\t\t\tconst ws_heartCheck = {\r\n\t\t\t\ttimeout: 5000, // 5秒\r\n\t\t\t\ttimeoutObj: null,\r\n\t\t\t\tserverTimeoutObj: null,\r\n\t\t\t\tstart: function () {\r\n\t\t\t\t\tthis.timeoutObj = setTimeout(() => {\r\n\t\t\t\t\t\t// 发送心跳包\r\n\t\t\t\t\t\tself.ws.send(\"ping\");\r\n\t\t\t\t\t\tthis.serverTimeoutObj = setTimeout(() => {\r\n\t\t\t\t\t\t\tself.ws.close(); // 超时未响应则断开\r\n\t\t\t\t\t\t}, this.timeout);\r\n\t\t\t\t\t}, this.timeout);\r\n\t\t\t\t},\r\n\t\t\t\treset: function () {\r\n\t\t\t\t\tclearTimeout(this.timeoutObj); // 重置心跳\r\n\t\t\t\t\tclearTimeout(this.serverTimeoutObj);\r\n\t\t\t\t\tthis.start();\r\n\t\t\t\t},\r\n\t\t\t\tstop: function () {\r\n\t\t\t\t\tclearTimeout(this.timeoutObj);\r\n\t\t\t\t\tclearTimeout(this.serverTimeoutObj);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// 连接打开\r\n\t\t\tthis.ws.onopen = function (event) {\r\n\t\t\t\tws_heartCheck.start();\r\n\t\t\t\tself.text_content = self.text_content + \"已经打开开标室连接!\" + \"\\n\";\r\n\t\t\t\tself.isLink = true;\r\n\t\t\t\tconsole.log(self.text_content);\r\n\t\t\t};\r\n\t\t\t// 收到消息\r\n\t\t\tthis.ws.onmessage = function (event) {\r\n\t\t\t\tconsole.log(event.data);\r\n\t\t\t\tif (event.data == \"ping\") {\r\n\t\t\t\t\tws_heartCheck.reset(); // 心跳包\r\n\t\t\t\t} else if (event.data == \"连接成功\") {\r\n\t\t\t\t} else if (event.data == \"signIn\") {\r\n\t\t\t\t\tself.$refs.publicity.initdataList();\r\n\t\t\t\t} else if (event.data == \"supDecrytion\") {\r\n\t\t\t\t\tself.$refs.decryption.initdataList();\r\n\t\t\t\t} else if (\r\n\t\t\t\t\tevent.data == \"ready\" ||\r\n\t\t\t\t\tevent.data == \"bidPublicity\" ||\r\n\t\t\t\t\tevent.data == \"decryption\" ||\r\n\t\t\t\t\tevent.data == \"nextStep\" ||\r\n\t\t\t\t\tevent.data == \"bidAnnouncement\" ||\r\n\t\t\t\t\tevent.data == \"end\" ||\r\n\t\t\t\t\tevent.data == \"flowLabel\"\r\n\t\t\t\t) {\r\n\t\t\t\t\tself.updateStatus();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tself.initChat();\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t// 连接关闭，自动重连\r\n\t\t\tthis.ws.onclose = function (event) {\r\n\t\t\t\tself.text_content = self.text_content + \"已经关闭开标室连接!\" + \"\\n\";\r\n\t\t\t\tself.isLink = false;\r\n\t\t\t\tclearTimeout(ws_heartCheck.timeoutObj);\r\n\t\t\t\tclearTimeout(ws_heartCheck.serverTimeoutObj);\r\n\t\t\t\t//断开后自动重连\r\n\t\t\t\tws_heartCheck.stop();\r\n\t\t\t\tself.join();\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 主动断开websocket连接\r\n\t\texit() {\r\n\t\t\tif (this.ws) {\r\n\t\t\t\tthis.ws.close();\r\n\t\t\t\tthis.ws = null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 发送消息（自己输入）\r\n\t\tsend() {\r\n\t\t\tif (this.ws) {\r\n\t\t\t\tthis.ws.send(this.message);\r\n\t\t\t\tthis.message = \"\";\r\n\t\t\t\tthis.scrollToBottom();\r\n\t\t\t} else {\r\n\t\t\t\talert(\"未连接到开标室服务器\");\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 发送消息（子组件调用）\r\n\t\toperateSend(message) {\r\n\t\t\tif (this.ws) {\r\n\t\t\t\tthis.ws.send(message);\r\n\t\t\t} else {\r\n\t\t\t\talert(\"未连接到开标室服务器\");\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 初始化聊天记录\r\n\t\tinitChat() {\r\n\t\t\tchatHistory(this.$route.query.projectId).then((response) => {\r\n\t\t\t\tif (response.code == 200) {\r\n\t\t\t\t\tthis.recordContent = response.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.recordContent = [];\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 聊天内容滚动到底部\r\n\t\tscrollToBottom() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst container = this.$refs.messagesContainer;\r\n\t\t\t\tcontainer.scrollTop = container.scrollHeight;\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 获取并定时更新时间\r\n\t\tupdateTime() {\r\n\t\t\t// 清理之前的定时器\r\n\t\t\tif (this.timeInterval) {\r\n\t\t\t\tclearInterval(this.timeInterval);\r\n\t\t\t}\r\n\r\n\t\t\tvar _this = this;\r\n\t\t\tgetSystemTime().then((result) => {\r\n\t\t\t\tif(result.code==200){\r\n\t\t\t\t\tvar ct = new Date(result.data);\r\n\t\t\t\t\tthis.timeInterval = setInterval(function(){\r\n\t\t\t\t\t\tct.setSeconds(ct.getSeconds() + 1);\r\n\t\t\t\t\t\t_this.currentTime = formatDateOption(ct, \"cdatetime\");\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t_this.updateStatus();\r\n\t\t\t\t\t\t_this.initChat();\r\n\r\n\t\t\t\t\t}, 1000); // 每秒更新时间\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\t// 生命周期钩子\r\n\tcreated() { },\r\n\tmounted() {\r\n\t\tthis.init();\r\n\t\tthis.initChat();\r\n\r\n\t\tthis.updateTime();\r\n\t},\r\n\tupdated() {\r\n\t\tthis.scrollToBottom();\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.timeInterval) {\r\n\t\t\tclearInterval(this.timeInterval);\r\n\t\t}\r\n\t\t// 断开WebSocket连接\r\n\t\tthis.exit();\r\n\t},\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// 覆盖el-card body内边距\r\n::v-deep .el-card__body {\r\n\tpadding: 0;\r\n}\r\n</style>\r\n\r\n<style scoped lang=\"scss\">\r\n// 聊天消息激活态\r\n.active {\r\n\tbackground-color: rgba(149, 250, 190, 1);\r\n}\r\n// 开标室主布局\r\n.bidOpeningHall {\r\n\tposition: relative;\r\n\tbackground-color: #f5f5f5;\r\n\tdisplay: flex;\r\n\tflex-wrap: nowrap;\r\n\tjustify-content: center;\r\n\talign-content: flex-start;\r\n\talign-items: flex-start;\r\n}\r\n// 主体卡片样式\r\n.box-card {\r\n\tmin-height: 600px;\r\n\twidth: 50%;\r\n\tmargin: 15px 5px;\r\n}\r\n// 聊天室整体样式\r\n.im {\r\n\t.im-title {\r\n\t\twidth: 100%;\r\n\t\theight: 50px;\r\n\t\tbackground: #176adb;\r\n\r\n\t\tfont-weight: 500;\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #ffffff;\r\n\t\tletter-spacing: 0;\r\n\r\n\t\tline-height: 50px;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.im-content {\r\n\t\tmargin: 10px;\r\n\t\tbackground: #f5f5f5;\r\n\t\theight: 450px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\t.im-operation {\r\n\t\tdisplay: flex;\r\n\t\tmargin: 0 10px;\r\n\t\tmargin-bottom: 10px;\r\n\t\toverflow: auto;\r\n\t}\r\n}\r\n// 聊天内容区样式\r\n.im-content {\r\n\t// 他人消息气泡\r\n\t.word {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 20px;\r\n\r\n\t\timg {\r\n\t\t\twidth: 40px;\r\n\t\t\theight: 40px;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t\t.info {\r\n\t\t\twidth: 47%;\r\n\t\t\tmargin-left: 10px;\r\n\t\t\t.Sender_time {\r\n\t\t\t\tpadding-right: 12px;\r\n\t\t\t\tpadding-top: 5px;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\theight: 20px;\r\n\t\t\t}\r\n\t\t\t.message_time {\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\theight: 20px;\r\n\t\t\t\tline-height: 20px;\r\n\t\t\t\tmargin-top: -5px;\r\n\t\t\t\tmargin-top: 5px;\r\n\t\t\t}\r\n\t\t\t.info-content {\r\n\t\t\t\tword-break: break-all;\r\n\t\t\t\t// max-width: 45%;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tpadding: 10px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-top: 8px;\r\n\t\t\t\tbackground: #dbdbdb;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t}\r\n\t\t\t//小三角形\r\n\t\t\t.info-content::before {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: -8px;\r\n\t\t\t\ttop: 8px;\r\n\t\t\t\tcontent: \"\";\r\n\t\t\t\tborder-right: 10px solid #dbdbdb;\r\n\t\t\t\tborder-top: 8px solid transparent;\r\n\t\t\t\tborder-bottom: 8px solid transparent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// 自己消息气泡\r\n\t.word-my {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t\tmargin-bottom: 20px;\r\n\t\timg {\r\n\t\t\twidth: 40px;\r\n\t\t\theight: 40px;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t\t.info {\r\n\t\t\twidth: 90%;\r\n\t\t\t// margin-left: 10px;\r\n\t\t\ttext-align: right;\r\n\t\t\t// position: relative;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: flex-end;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tflex-direction: column;\r\n\t\t\t.info-content {\r\n\t\t\t\tword-break: break-all;\r\n\t\t\t\tmax-width: 45%;\r\n\t\t\t\tpadding: 10px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\t// float: right;\r\n\t\t\t\tmargin-right: 10px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-top: 8px;\r\n\t\t\t\tbackground: #a3c3f6;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t}\r\n\t\t\t.Sender_time {\r\n\t\t\t\tpadding-right: 12px;\r\n\t\t\t\tpadding-top: 5px;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tcolor: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\theight: 20px;\r\n\t\t\t}\r\n\t\t\t//小三角形\r\n\t\t\t.info-content::after {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: -8px;\r\n\t\t\t\ttop: 8px;\r\n\t\t\t\tcontent: \"\";\r\n\t\t\t\tborder-left: 10px solid #a3c3f6;\r\n\t\t\t\tborder-top: 8px solid transparent;\r\n\t\t\t\tborder-bottom: 8px solid transparent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n"]}]}