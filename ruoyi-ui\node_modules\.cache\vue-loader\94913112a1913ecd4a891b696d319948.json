{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidder\\notice\\add.vue?vue&type=style&index=0&id=46da06bb&lang=css", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidder\\notice\\add.vue", "mtime": 1753957830693}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnRlbmRlciB7DQogIHBhZGRpbmc6IDAgNTBweDsNCn0NCg0KLm1ha2VUZW5zZXJGaWxlIHsNCiAgd2lkdGg6IDIwOHB4Ow0KICBib3JkZXI6IHJnYmEoMCwgMCwgMCwgMSkgc29saWQgMXB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7DQogIGZvbnQtZmFtaWx5OiBNaWNyb3NvZnQgWWFIZWk7DQogIGNvbG9yOiByZ2JhKDgwLCA4MCwgODAsIDEpOw0KICBsaW5lLWhlaWdodDogMTUwJTsNCiAgZm9udC1zaXplOiAxNHB4Ow0KDQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsNCn0NCi5tYWtlVGVuc2VyRmlsZTpob3ZlciA6YWN0aXZlIDpmb2N1cyB7DQogIGNvbG9yOiByZ2JhKDgwLCA4MCwgODAsIDEpOw0KfQ0KDQouYXR0YWNobWVudCB7DQogIGhlaWdodDogMjdweDsNCiAgbGVmdDogNjRweDsNCiAgdG9wOiA2NjhweDsNCiAgY29sb3I6IHJnYmEoODAsIDgwLCA4MCwgMSk7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgbGluZS1oZWlnaHQ6IDE1MCU7DQogIHRleHQtYWxpZ246IGxlZnQ7DQp9DQoubGluZSB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDJweDsNCiAgbGVmdDogNjRweDsNCiAgdG9wOiA3MDBweDsNCiAgY29sb3I6IHJnYmEoODAsIDgwLCA4MCwgMSk7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNTgsIDI1LCAyMzYsIDEpOw0KICBmb250LXNpemU6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAxNTAlOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQoNCiAgbWFyZ2luLWJvdHRvbTogMjVweDsNCn0NCi5vcHRpb24gew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQouc2VsZWN0LW9wdGlvbiB7DQogIHotaW5kZXg6IDk5OSAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmgBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add.vue", "sourceRoot": "src/views/bidder/notice", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"tender\"\r\n    style=\"margin: 20px 0px;\"\r\n    v-loading=\"loading\"\r\n  >\r\n    <el-form\r\n      ref=\"winningBidderNotice\"\r\n      :model=\"formData\"\r\n      :rules=\"rules\"\r\n      size=\"medium\"\r\n      label-width=\"0\"\r\n    >\r\n      <el-col\r\n        :span=\"24\"\r\n        class=\"card-box\"\r\n      >\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-suitcase\"></i>开标情况</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table\r\n              cellspacing=\"0\"\r\n              style=\"width: 100%;table-layout:fixed;\"\r\n            >\r\n              <tbody>\r\n                <tr>\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong style=\"color:red;\">*</strong>项目\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"22\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <el-form-item prop=\"projectId\">\r\n                        <el-select\r\n                          v-model=\"formData.projectId\"\r\n                          placeholder=\"请选择项目\"\r\n                          ref=\"selectProject\"\r\n                          filterable\r\n                          clearable\r\n                          :disabled=\"initProject\"\r\n                          :style=\"{ width: '100%' }\"\r\n                          @change=\"change($event)\"\r\n                        >\r\n                          <el-option\r\n                            v-for=\"(item, index) in projectIdOptions\"\r\n                            :key=\"index\"\r\n                            :label=\"item.projectName\"\r\n                            :value=\"item.projectId\"\r\n                            :disabled=\"item.disabled\"\r\n                          ></el-option>\r\n                        </el-select>\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong style=\"color:red;\">*</strong>公告名称\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"22\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <el-form-item prop=\"noticeName\">\r\n                        <el-input\r\n                          v-model=\"formData.noticeName\"\r\n                          placeholder=\"公告名称\"\r\n                          clearable\r\n                          :style=\"{ width: '100%' }\"\r\n                        >\r\n                        </el-input>\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong style=\"color:red;\">*</strong>项目编号\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"10\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <el-form-item prop=\"projectId\">\r\n                        {{formData.projectCode}}\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong style=\"color:red;\">*</strong>公告发布时间\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"10\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <el-form-item prop=\"noticeStartTime\">\r\n                        <el-date-picker\r\n                          v-model=\"formData.noticeStartTime\"\r\n                          format=\"yyyy-MM-dd\"\r\n                          value-format=\"yyyy-MM-dd\"\r\n                          :picker-options=\"pickerOptionsOne\"\r\n                          :style=\"{ width: '100%' }\"\r\n                          placeholder=\"请选择日期\"\r\n                          clearable\r\n                          disabled\r\n                        ></el-date-picker>\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col\r\n        :span=\"24\"\r\n        class=\"card-box\"\r\n      >\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-document\"></i>中标人信息</span>\r\n          </div>\r\n          <div>\r\n            <el-table\r\n              :data=\"bidderTable\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"100\"\r\n              ></el-table-column>\r\n              <el-table-column\r\n                v-if=\"false\"\r\n                label=\"投标人记录id\"\r\n                prop=\"bidderInfoId\"\r\n              ></el-table-column>\r\n              <el-table-column\r\n                label=\"投标人名称\"\r\n                prop=\"bidderName\"\r\n              ></el-table-column>\r\n              <el-table-column\r\n                label=\"投标报价\"\r\n                prop=\"bidderAmount\"\r\n              ></el-table-column>\r\n\t            <el-table-column\r\n\t\t            label=\"最终得分\"\r\n\t\t            prop=\"score\"\r\n\t            ></el-table-column>\r\n<!--              <el-table-column\r\n                prop=\"score\"\r\n                label=\"评分\"\r\n              > </el-table-column>-->\r\n<!--              <el-table-column\r\n                prop=\"ranking\"\r\n                label=\"名次\"\r\n              > </el-table-column>-->\r\n              <el-table-column label=\"确认中标人\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-checkbox\r\n                    v-model=\"scope.row.isWinCheckBox\"\r\n                    @change=\"winBidder(scope.row.bidderId)\"\r\n                  ></el-checkbox>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n<!-- <el-col\r\n  :span=\"24\"\r\n  class=\"card-box\"\r\n>\r\n  <el-card>\r\n    <div slot=\"header\">\r\n      <span><i class=\"el-icon-document\"></i>公告内容</span>\r\n    </div>\r\n    <div>\r\n      <el-form-item prop=\"noticeContent\">\r\n        <div\r\n          v-html=\"formData.noticeContent\"\r\n          :min-height=\"192\"\r\n        />\r\n      </el-form-item>\r\n    </div>\r\n  </el-card>\r\n</el-col> -->\r\n\r\n      <el-col\r\n        :span=\"24\"\r\n        class=\"card-box\"\r\n      >\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-document\"></i>附件</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table\r\n              cellspacing=\"0\"\r\n              style=\"width: 100%;table-layout:fixed;\"\r\n            >\r\n              <tbody>\r\n                <tr\r\n                  v-for=\"dict in dict.type.busi_bidder_notice_attachment\"\r\n                  :key=\"dict.label\"\r\n                >\r\n                  <td\r\n                    colspan=\"2\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell\">\r\n                      <strong\r\n                        style=\"color:red;\"\r\n                        v-if=\"dict.raw.isEquals==1\"\r\n                      >*</strong>{{ dict.label }}\r\n                    </div>\r\n                  </td>\r\n                  <td\r\n                    colspan=\"22\"\r\n                    class=\"el-table__cell is-leaf\"\r\n                  >\r\n                    <div class=\"cell cell-right-border\">\r\n                      <el-form-item class=\"upload\">\r\n                        <template>\r\n                          <FileUpload\r\n                            :value=\"getImgPath(dict)\"\r\n                            @input=\"handleInput(dict, $event)\"\r\n                            :fileType=\"['pdf', 'doc', 'docx']\"\r\n                            :isShowTip=\"false\"\r\n                          ></FileUpload>\r\n                        </template>\r\n                      </el-form-item>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n    </el-form>\r\n    <div\r\n      slot=\"footer\"\r\n      class=\"dialog-footer\"\r\n      style=\"text-align: center;\"\r\n    >\r\n      <el-button\r\n        type=\"primary\"\r\n        @click=\"submitForm\"\r\n      >发布</el-button>\r\n      <!-- <el-button type=\"primary\" @click=\"TemporaryStorage\">暂存</el-button> -->\r\n      <el-button @click=\"resetForm\">重置</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listProject } from \"@/api/tender/project\";\r\nimport { listInfo } from \"@/api/bidding/info\";\r\nimport {\r\n  addNotice,\r\n  updateNotice,\r\n  createNoticeContent,\r\n} from \"@/api/bidder/notice\";\r\n\r\nexport default {\r\n  components: {},\r\n  dicts: [\"busi_bidder_notice_attachment\", \"price_unit\"],\r\n  props: [],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      initProject: false,\r\n      formData: {\r\n        projectId: \"\",\r\n        noticeName: \"\",\r\n        noticeCode: \"\",\r\n        noticeContent: \"\",\r\n        noticeType: 1,\r\n        noticeStartTime: \"\",\r\n        bidderId: \"\",\r\n        bidderCode: \"\",\r\n        bidderName: \"\",\r\n        bidAmount: \"\",\r\n        ranking: \"\",\r\n        score: \"\",\r\n        attachments: [],\r\n      },\r\n      bidderTable: [],\r\n      noticeName: \"\",\r\n      rules: {\r\n        projectId: [\r\n          {\r\n            required: true,\r\n            message: \"请选择\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        noticeName: [\r\n          {\r\n            required: true,\r\n            message: \"请输入公告名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        noticeStartTime: [\r\n          {\r\n            required: true,\r\n            message: \"请选择日期\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        noticeContent: [\r\n          {\r\n            required: false,\r\n            message: \"请输入项目名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      projectIdOptions: [],\r\n      attachmentsMap: {},\r\n      // 公告开始时间日期选择的禁用日期\r\n      pickerOptionsOne: {\r\n        disabledDate: (time) => {\r\n          return time.getTime() < new Date() - 8.64e7;\r\n        },\r\n      },\r\n    };\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  created() {},\r\n  mounted() {\r\n    this.getList();\r\n    this.formData.noticeStartTime = new Date();\r\n  },\r\n  methods: {\r\n    getSelectedWinBidders() {\r\n      return this.bidderTable.filter(bidder => bidder.isWinCheckBox);\r\n    },\r\n    getList() {\r\n      listProject({ delFlag: 0, projectStatus: 50 }).then((response) => {\r\n        this.projectIdOptions = response.rows;\r\n        this.loading = false;\r\n        if (this.$route.query.projectId) {\r\n          this.formData.projectId = parseInt(this.$route.query.projectId);\r\n          this.$refs.selectProject.$emit(\"change\", this.formData.projectId);\r\n          this.initProject = true;\r\n        }\r\n      });\r\n    },\r\n    change(value) {\r\n      if (value) {\r\n        const selected = this.projectIdOptions.find((option) => {\r\n          return option.projectId == value;\r\n        });\r\n\r\n        this.formData.noticeName = selected.projectName + \"-结果公告\";\r\n        this.formData.projectCode = selected.projectCode;\r\n        listInfo({ projectId: value }).then((response) => {\r\n          this.bidderTable = response.rows;\r\n        });\r\n        // getBidderInfoByProjectID(value).then((result) => {\r\n        //   if (result.code == 200) {\r\n        //    // this.formData.noticeContent = result.data;\r\n        //     console.log(result)\r\n        //     this.bidderTable = result.data; // 将数据赋值给bidderTable\r\n\r\n        //   }\r\n        // });\r\n      }\r\n    },\r\n    resetForm() {\r\n      this.$refs[\"winningBidderNotice\"].resetFields();\r\n    },\r\n    handleInput(dict, data) {\r\n      if (!data || data == \"\") {\r\n        delete this.attachmentsMap[dict.value];\r\n      } else {\r\n        let fileList = data.split(\",\");\r\n        fileList = fileList.map((item) => {\r\n          return {\r\n            fileName: item.substring(item.lastIndexOf(\"/\") + 1),\r\n            fileType: dict.value,\r\n            fileSuffix: item.substring(item.lastIndexOf(\".\") + 1),\r\n            filePath: item,\r\n          };\r\n        });\r\n        this.attachmentsMap[dict.value] = fileList;\r\n      }\r\n    },\r\n    submitForm() {\r\n      for (let item of this.dict.type.busi_bidder_notice_attachment) {\r\n        if (\r\n          item.raw.isEquals == 1 &&\r\n          this.attachmentsMap[item.value] == undefined\r\n        ) {\r\n          this.$message.error(item.label + \" 文件不能为空\");\r\n          return;\r\n        }\r\n      }\r\n      const selectedWinBidders = this.getSelectedWinBidders();\r\n      console.log('选中的中标人数据:', selectedWinBidders);\r\n      if (selectedWinBidders.length !== 1) {\r\n        // 如果不是一条记录，弹出错误信息\r\n        this.$message.error('只能选中一条中标人数据');\r\n      }\r\n\r\n      /*const winningBidder = this.bidderTable.find((bidder) => {\r\n        bidder.isWin == 1;\r\n      });*/\r\n      const winner = this.getSelectedWinBidders()[0];\r\n\r\n      this.$refs[\"winningBidderNotice\"].validate((valid) => {\r\n        if (!valid) return;\r\n\r\n        if (this.$route.params.noticeId == 0) {\r\n          this.formData.attachments = [].concat(\r\n            ...Object.values(this.attachmentsMap)\r\n          );\r\n          this.formData.bidderId = winner.bidderId;\r\n          this.formData.bidderCode = winner.bidderCode;\r\n          this.formData.bidderName = winner.bidderName;\r\n          this.formData.bidAmount = winner.bidderAmount;\r\n          this.formData.ranking = winner.ranking;\r\n          this.formData.score = winner.score;\r\n          console.log(\"submit：\")\r\n          console.log(this.formData)\r\n          addNotice(this.formData).then((response) => {\r\n            this.$modal.msgSuccess(\"新增成功\");\r\n            this.close();\r\n          });\r\n        } else {\r\n          updateNotice(this.formData).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.close();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getImgPath(dict) {\r\n      if (\r\n        this.attachmentsMap[dict.value] &&\r\n        this.attachmentsMap[dict.value].length > 0\r\n      ) {\r\n        let arr = this.attachmentsMap[dict.value];\r\n        return arr\r\n          .map((item) => {\r\n            return item.filePath;\r\n          })\r\n          .join(\",\");\r\n      }\r\n      return \"\";\r\n    },\r\n    winBidder(val) {\r\n      var biddingInfoList = this.bidderTable;\r\n      var winBidder = '';\r\n      for (var index in biddingInfoList) {\r\n        if (biddingInfoList[index].bidderId == val) {\r\n          biddingInfoList[index].isWin = 1;\r\n          winBidder = biddingInfoList[index];\r\n        } else {\r\n          biddingInfoList[index].isWin = 0;\r\n          biddingInfoList[index].isWinCheckBox = false;\r\n        }\r\n      }\r\n      console.log('winBidder',winBidder);\r\n\r\n      createNoticeContent(winBidder.projectId, winBidder.bidderInfoId).then((result) => {\r\n        if (result.code == 200) {\r\n          this.formData.noticeContent = result.data;\r\n        }\r\n      });\r\n    },\r\n    // 关闭当前页\r\n    close() {\r\n      this.$tab.closePage();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style>\r\n.tender {\r\n  padding: 0 50px;\r\n}\r\n\r\n.makeTenserFile {\r\n  width: 208px;\r\n  border: rgba(0, 0, 0, 1) solid 1px;\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  font-family: Microsoft YaHei;\r\n  color: rgba(80, 80, 80, 1);\r\n  line-height: 150%;\r\n  font-size: 14px;\r\n\r\n  text-align: center;\r\n  vertical-align: middle;\r\n}\r\n.makeTenserFile:hover :active :focus {\r\n  color: rgba(80, 80, 80, 1);\r\n}\r\n\r\n.attachment {\r\n  height: 27px;\r\n  left: 64px;\r\n  top: 668px;\r\n  color: rgba(80, 80, 80, 1);\r\n  font-size: 18px;\r\n  line-height: 150%;\r\n  text-align: left;\r\n}\r\n.line {\r\n  width: 100%;\r\n  height: 2px;\r\n  left: 64px;\r\n  top: 700px;\r\n  color: rgba(80, 80, 80, 1);\r\n  background-color: rgba(58, 25, 236, 1);\r\n  font-size: 14px;\r\n  line-height: 150%;\r\n  text-align: center;\r\n\r\n  margin-bottom: 25px;\r\n}\r\n.option {\r\n  text-align: center;\r\n}\r\n.select-option {\r\n  z-index: 999 !important;\r\n}\r\n</style>\r\n<style scoped>\r\n.el-form-item {\r\n  margin-bottom: 0px;\r\n}\r\n/deep/ .el-upload {\r\n  float: right;\r\n}\r\n/deep/ .el-upload-list {\r\n  width: 90%;\r\n}\r\n/deep/ .upload > .el-form-item__content {\r\n  border-bottom: rgba(153, 153, 153, 1) solid 1px;\r\n}\r\n</style>\r\n"]}]}