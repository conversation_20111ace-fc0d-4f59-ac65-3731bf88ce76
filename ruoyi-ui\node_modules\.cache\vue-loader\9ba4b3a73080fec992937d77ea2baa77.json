{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\three.vue?vue&type=style&index=0&id=72c45866&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\compliance\\three.vue", "mtime": 1753948158725}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi50aHJlZSB7DQogIHBhZGRpbmc6IDIwcHggNDBweDsNCiAgZGlzcGxheTogZmxleDsNCn0NCi5lbC1oZWFkZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBjb2xvcjogIzMzMzsNCiAgZm9udC1zaXplOiAyNnB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGxpbmUtaGVpZ2h0OiAxMDBweDsNCiAgYm9yZGVyLWJvdHRvbTogIzMzMyAxcHggc29saWQ7DQp9DQouZWwtbWFpbiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGNvbG9yOiAjMzMzOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGxpbmUtaGVpZ2h0OiA2MHB4Ow0KfQ0KLml0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBtYXJnaW4tYm90dG9tOiA4MHB4Ow0KICAuaXRlbS10aXRsZSB7DQogICAgd2lkdGg6IDEyMHB4Ow0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICB9DQp9DQouaXRlbS1idXR0b24gew0KICB3aWR0aDogMTUwcHg7DQogIGhlaWdodDogNDBweDsNCiAgbWFyZ2luOiAyMHB4IDI4cHg7DQogIGNvbG9yOiAjZmZmOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTc2YWRiOw0KICBib3JkZXI6IDA7DQogICY6aG92ZXIgew0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQp9DQouaXRlbS1idXR0b24tcmVkIHsNCiAgd2lkdGg6IDE1MHB4Ow0KICBoZWlnaHQ6IDQwcHg7DQogIG1hcmdpbjogMjBweCAyOHB4Ow0KICBjb2xvcjogI2ZmZjsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2U5MjkwMDsNCiAgYm9yZGVyOiAwOw0KICAmOmhvdmVyIHsNCiAgICBjb2xvcjogI2ZmZjsNCiAgfQ0KfQ0KLnJlc3VsdCB7DQogIHRleHQtYWxpZ246IGxlZnQ7DQogIG1hcmdpbi1sZWZ0OiAyMHB4Ow0KfQ0KLm9wZXJhdGlvbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KLnRleHQgew0KICA6OnYtZGVlcCAuZWwtdGV4dGFyZWFfX2lubmVyIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Ow0KICAgIGJvcmRlci1yYWRpdXM6IDA7DQogICAgYm9yZGVyOiAxcHggc29saWQgI2Y1ZjVmNTsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["three.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "three.vue", "sourceRoot": "src/views/expertReview/compliance", "sourcesContent": ["<template>\r\n  <div class=\"three\">\r\n    <div style=\"width:70%\">\r\n      <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px\">符合性评审</div>\r\n      <el-table :data=\"tableData\" border style=\"width: 100%\" :header-cell-style=\"headStyle\" :cell-style=\"cellStyle\">\r\n        <el-table-column prop=\"供应商名称\" width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column v-for=\"(item, index) in columns\" :key=\"index\" :prop=\"item.xm\" :label=\"item.xm\">\r\n          <template slot-scope=\"scope\">\r\n            <!-- <span v-if=\"scope.row[item.xm] == '/'\"> -->\r\n<!--              {{ scope.row[item.xm] }}-->\r\n            <!-- 未提交 -->\r\n            <!-- </span> -->\r\n            <!-- <i v-else style=\"color:#176ADB;font-size:20px\" :class=\"getIconClass(scope.row[item.xm])\"></i> -->\r\n\r\n            <span v-if=\"scope.row[item.xm] == '/'\">未提交</span>\r\n            <span v-if=\"scope.row[item.xm] == '1'\">通过</span>\r\n            <span v-if=\"scope.row[item.xm] == '0'\">不通过</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"result\">\r\n        <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;margin:20px 0\">评审结果：</div>\r\n        <div style=\"display: flex;margin-left:30px\">\r\n          <div style=\"margin-right:30px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;\" v-for=\"(item,index) in (Array.isArray(result) ? result : [])\" :key=\"index\">\r\n            {{ item.gys }}：\r\n            <span v-if=\"item.result\" style=\"color:green\">\r\n              通过\r\n            </span>\r\n            <span v-else style=\"color:red\">\r\n              不通过\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"operation\" v-if=\"!finish\">\r\n        <el-button\r\n          class=\"item-button\"\r\n          style=\"background-color: #f5f5f5;color: #176adb;\"\r\n          @click=\"reviewed\"\r\n        >重新评审</el-button>\r\n        <el-button class=\"item-button\" v-if=\"passedSupplierCount >= 3\" @click=\"completed\">节点评审完成</el-button>\r\n        <el-button class=\"item-button-red\"\r\n                   v-if=\"!hasIncompleteExpert\"\r\n                   :disabled=\"passedSupplierCount >= 3\"\r\n                   :style=\"passedSupplierCount >= 3 ? 'background-color: #ccc; color: #fff; cursor: not-allowed;' : ''\"\r\n                   @click=\"flowLabel\">流标</el-button>\r\n      </div>\r\n      <div v-else class=\"operation\">\r\n        <el-button class=\"item-button\" @click=\"back\">返回</el-button>\r\n      </div>\r\n    </div>\r\n    <div style=\"width:30%\">\r\n      <div class=\"result\">\r\n        <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px\">表决结果</div>\r\n        <el-input disabled class=\"text\" type=\"textarea\" :rows=\"20\" placeholder=\"请输入表决结果\" v-model=\"votingResults\">\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n    <el-dialog title=\"流标情况说明\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-input type=\"textarea\" :rows=\"4\" placeholder=\"请输入内容\" v-model=\"reasonFlowBid\">\r\n      </el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmflow\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { leaderSummaryQuery, reEvaluate } from \"@/api/expert/review\";\r\nimport { updateProcess } from \"@/api/evaluation/process\";\r\nimport { abortiveTenderNotice } from \"@/api/bidder/notice\";\r\nimport { reEvaluationTwo } from \"@/api/evaluation/expertStatus\";\r\n\r\nexport default {\r\n  props: {\r\n    finish: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      columns: {},\r\n      result: [], // 修改：初始化为数组而不是对象\r\n\r\n      votingResults: \"\",\r\n      reasonFlowBid: \"\",\r\n      dialogVisible: false,\r\n\r\n      // 定时器ID，用于清除定时器\r\n      intervalId: null,\r\n\r\n      leader: {},\r\n      headStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        background: \"#176ADB\",\r\n        color: \"#fff\",\r\n        \"font-size\": \"16px\",\r\n        \"font-weight\": \"700\",\r\n        border: \"0\",\r\n      },\r\n      cellStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        height: \"60px\",\r\n        color: \"#000\",\r\n        \"font-size\": \"14px\",\r\n        \"font-weight\": \"700\",\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const data = {\r\n        projectId: this.$route.query.projectId,\r\n        itemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      leaderSummaryQuery(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.votingResults = response.data.bjjgsb\r\n          this.tableData = this.transformData(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)\r\n          this.columns = response.data.tableColumns;\r\n          this.result = this.generateResultTable(\r\n            response.data.tableColumns,\r\n            response.data.busiBidderInfos,\r\n            response.data.tableData\r\n          );\r\n          this.result = this.result.filter(item => item.isAbandonedBid == 0)\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    // 转换函数\r\n    transformData(tableColumns, busiBidderInfos, tableData) {\r\n      // 创建一个映射，用于将 bidderId 映射到 bidderName\r\n      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {\r\n        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };\r\n        return acc;\r\n      }, {});\r\n\r\n      // 创建一个映射，用于将 resultId 映射到 itemName\r\n      const columnIdToName = tableColumns.reduce((acc, column) => {\r\n        acc[column.resultId] = column.xm;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 转换数据\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;\r\n        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];\r\n        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };\r\n\r\n        // 只取 tableColumns 中定义的评估项\r\n        tableColumns.forEach((column) => {\r\n          const itemId = column.resultId;\r\n          transformedRow[column.xm] = row[itemId] || \"/\"; // 默认为'/'(没有评完)\r\n        });\r\n\r\n        return transformedRow;\r\n      });\r\n    },\r\n    // 组装评审结果\r\n    //少数服从多数\r\n    generateResultTable(tableColumns, busiBidderInfos, tableData) {\r\n      const entMethodItemIds = tableColumns.map((item) => {\r\n        return item.resultId;\r\n      });\r\n\r\n      // Create a map from bidderId to bidderName\r\n      const bidderMap = new Map(\r\n        busiBidderInfos.map((bidder) => [bidder.bidderId, {\r\n          bidderName: bidder.bidderName,\r\n          isAbandonedBid: bidder.isAbandonedBid || 0 // 如果 isAbandonedBid 不存在，则默认为 0\r\n        }])\r\n      );\r\n\r\n      // 生成结果表，按照少数服从多数规则判断是否通过\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;\r\n        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);\r\n        const totalItems = entMethodItemIds.length;\r\n        let passedCount = 0;\r\n        entMethodItemIds.forEach((key) => {\r\n          if (row[key] == \"1\") {\r\n            passedCount++;\r\n          }\r\n        });\r\n        const result = passedCount >= Math.ceil(totalItems / 2);\r\n        return {\r\n          bidder: supplierId,\r\n          gys: bidderName,\r\n          isAbandonedBid: isAbandonedBid,\r\n          result: result,\r\n        };\r\n      });\r\n    },\r\n/*  只要有一个是不通过就算不同过\r\n    generateResultTable(tableColumns, busiBidderInfos, tableData) {\r\n      const entMethodItemIds = tableColumns.map((item) => {\r\n        return item.resultId;\r\n      });\r\n\r\n      // Create a map from bidderId to bidderName\r\n      const bidderMap = new Map(\r\n        busiBidderInfos.map((bidder) => [bidder.bidderId, {\r\n          bidderName: bidder.bidderName,\r\n          isAbandonedBid: bidder.isAbandonedBid || 0 // 如果 isAbandonedBid 不存在，则默认为 0\r\n        }])\r\n      );\r\n\r\n      // Generate the result table、\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;\r\n        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);\r\n        var result = true;\r\n        const temp = entMethodItemIds.every((key) => {\r\n          return row[key] == \"1\";\r\n        });\r\n        if (!temp) {\r\n          result = false;\r\n        }\r\n        return {\r\n          bidder: supplierId,\r\n          gys: bidderName,\r\n          isAbandonedBid: isAbandonedBid,\r\n          result: result,\r\n        };\r\n      });\r\n    },\r\n*/\r\n    // 节点评审完成\r\n    completed() {\r\n\t\t\t\r\n\t\t\t\r\n      const evaluationProcessId = JSON.parse(\r\n        localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n      );\r\n      const data = {\r\n        evaluationProcessId: evaluationProcessId.evaluationProcessId,\r\n        evaluationResult: JSON.stringify(this.result),\r\n        evaluationState: 2,\r\n        evaluationResultRemark: this.votingResults,\r\n      };\r\n      updateProcess(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$router.push({\r\n            path: \"/expertInfo\",\r\n            query: {\r\n              projectId: this.$route.query.projectId,\r\n              zjhm: this.$route.query.zjhm,\r\n              tips: true,\r\n\t            tenderMode:1\r\n            },\r\n          });\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.$router.push({\r\n        path: \"/expertInfo\",\r\n        query: {\r\n          projectId: this.$route.query.projectId,\r\n          zjhm: this.$route.query.zjhm,\r\n        },\r\n      });\r\n    },\r\n    getIconClass(value) {\r\n\t    if (value == \"1\"){\r\n\t\t    return \"el-icon-check\"           // 通过：显示勾选图标\r\n\t    }\r\n\t    \r\n\t    if (value == \"0\"){\r\n\t\t    return \"el-icon-circle-close\"    // 不通过：显示关闭图标\r\n\t    }\r\n\t    \r\n\t    return value  // 其他情况直接返回原值\r\n    },\r\n    // 重新评审\r\n    reviewed() {\r\n      const query = {\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).projectEvaluationId,\r\n        expertResultId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\"))\r\n          .expertResultId,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).scoringMethodItemId,\r\n      };\r\n      reEvaluationTwo(query).then((res) => {\r\n        if (res.code == 200) {\r\n          const evaluationProcessId = JSON.parse(\r\n            localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n          );\r\n\r\n          reEvaluate(evaluationProcessId.evaluationProcessId).then(\r\n            (response) => {\r\n              if (response.code == 200) {\r\n                // 触发重新评审通知，通知其他专家页面\r\n                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {\r\n                  this.$parent.triggerReEvaluationNotification();\r\n                }\r\n                this.$emit(\"send\", \"one\");\r\n              } else {\r\n                this.$message.warning(response.msg);\r\n              }\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n    flowLabel() {\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确认流标\r\n    confirmflow() {\r\n      if (this.reasonFlowBid == \"\") {\r\n        this.$message.warning(\"请完善情况说明\");\r\n        return;\r\n      }\r\n      // if (this.reasonFlowBid == \"\") {\r\n      const data = {\r\n        projectId: this.$route.query.projectId,\r\n        abortiveType: 3,\r\n        remark: this.reasonFlowBid,\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n      };\r\n      abortiveTenderNotice(data).then((response) => {\r\n        if (response.code == 200) {\r\n          const query = {\r\n            projectId: this.$route.query.projectId,\r\n            zjhm: this.$route.query.zjhm,\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n          };\r\n          this.$router.push({ path: \"/summary\", query: query });\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // } else {\r\n      // }\r\n    },\r\n\r\n    /**\r\n     * 清除定时器的通用方法\r\n     * 在多个生命周期钩子中调用，确保定时器被正确清除\r\n     */\r\n    clearTimer() {\r\n      if (this.intervalId) {\r\n        clearInterval(this.intervalId);\r\n        this.intervalId = null;\r\n        console.log(\"定时器已清除 - compliance/three.vue\");\r\n      }\r\n    },\r\n  },\r\n  computed:{\r\n    passedSupplierCount() {\r\n      console.log(\"this.result:\",this.result);\r\n      // 添加安全检查：确保 result 是数组\r\n      if (!Array.isArray(this.result)) {\r\n        console.warn(\"result is not an array:\", this.result);\r\n        return 0;\r\n      }\r\n      return this.result.filter(item => item.result).length;\r\n    },\r\n\r\n    /**\r\n     * 检查是否有专家未完成评审\r\n     * 遍历tableData检查是否存在\"/\"状态（未评完）\r\n     * @returns {boolean} true表示有未完成评审的专家，false表示所有专家都已完成评审\r\n     */\r\n    hasIncompleteExpert() {\r\n      // 添加安全检查：确保 tableData 是数组\r\n      if (!Array.isArray(this.tableData)) {\r\n        console.warn(\"tableData is not an array:\", this.tableData);\r\n        return true; // 数据异常时，默认禁用流标按钮\r\n      }\r\n\r\n      // 遍历所有供应商的评审数据\r\n      const hasIncomplete = this.tableData.some(row => {\r\n        // 遍历每一行的所有属性，查找是否有\"/\"状态\r\n        return Object.keys(row).some(key => {\r\n          // 排除供应商名称和废标状态字段，只检查专家评审结果\r\n          if (key !== '供应商名称' && key !== 'isAbandonedBid') {\r\n            return row[key] === '/';\r\n          }\r\n          return false;\r\n        });\r\n      });\r\n\r\n      // 输出调试信息\r\n      console.log(\"hasIncompleteExpert:\", hasIncomplete, \"tableData:\", this.tableData);\r\n      return hasIncomplete;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * 组件挂载完成后执行\r\n   * 初始化组件数据和定时刷新\r\n   */\r\n  mounted() {\r\n    // 初始化数据\r\n    this.init();\r\n\r\n    // 设置定时器，每5秒自动刷新数据\r\n    // 用于实时更新评审状态和结果\r\n    this.intervalId = setInterval(()=>{\r\n      this.init();\r\n    },5000)\r\n  },\r\n\r\n  /**\r\n   * 组件销毁前执行\r\n   * 清除定时器，防止内存泄漏\r\n   */\r\n  beforeDestroy() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 组件完全销毁后执行\r\n   * 作为额外的安全措施清除定时器\r\n   */\r\n  destroyed() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 如果父组件使用了keep-alive，在组件失活时清除定时器\r\n   */\r\n  deactivated() {\r\n    this.clearTimer();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.three {\r\n  padding: 20px 40px;\r\n  display: flex;\r\n}\r\n.el-header {\r\n  background-color: #fff;\r\n  color: #333;\r\n  font-size: 26px;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  border-bottom: #333 1px solid;\r\n}\r\n.el-main {\r\n  background-color: #fff;\r\n  color: #333;\r\n  text-align: center;\r\n  line-height: 60px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.item-button {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #176adb;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.item-button-red {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #e92900;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.result {\r\n  text-align: left;\r\n  margin-left: 20px;\r\n}\r\n.operation {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n</style>\r\n"]}]}