{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\business.vue", "mtime": 1753949454666}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["business.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "business.vue", "sourceRoot": "src/views/expertReview", "sourcesContent": ["<template>\r\n  <div>\r\n<!--    <BidHeadthree></BidHeadthree>-->\r\n\t  <div class=\"title\">专家评审系统</div>\r\n    <div class=\"info\">\r\n      <div class=\"content\">\r\n        <one v-if=\"node == 'one'\" @send=\"handleData\"></one>\r\n        <two v-if=\"node == 'two'\" @send=\"handleData\" :isLeader=\"isLeader\" :finish=\"finish\"></two>\r\n        <three v-if=\"node == 'three'\" @send=\"handleData\" :finish=\"finish\"></three>\r\n      </div>\r\n    </div>\r\n    <Foot></Foot>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport one from \"./business/one\";\r\nimport two from \"./business/two\";\r\nimport three from \"./business/three\";\r\nimport { getProject } from \"@/api/tender/project\";\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { getEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\nimport expertReviewWebSocket from \"@/mixins/expertReviewWebSocket\";\r\n\r\nexport default {\r\n  components: { one, two, three },\r\n  mixins: [expertReviewWebSocket],\r\n  name: \"qualification\",\r\n  data() {\r\n    return {\r\n      projectName: \"测试项目\",\r\n      project: {},\r\n      node: \"one\",\r\n      finish: false,\r\n      leader: {},\r\n      isLeader: false,\r\n    };\r\n  },\r\n  methods: {\r\n    async init() {\r\n      try {\r\n        // 根据项目id查询项目信息\r\n        const projectResponse = await getProject(this.$route.query.projectId);\r\n        if (projectResponse.code === 200) {\r\n          this.project = projectResponse.data;\r\n        } else {\r\n          this.$message.warning(projectResponse.msg);\r\n        }\r\n\r\n        // 获取专家信息\r\n        const expertResponse = await expertInfoById({\r\n          projectId: this.$route.query.projectId,\r\n        });\r\n        if (expertResponse.code === 200) {\r\n          this.leader = expertResponse.data.find(\r\n            (item) => item.expertLeader === 1\r\n          );\r\n          console.log(\"this.leader\", this.leader);\r\n\r\n          if (this.leader && this.leader.zjhm === this.$route.query.zjhm) {\r\n            this.isLeader = true;\r\n          }\r\n        } else {\r\n          this.$message.warning(expertResponse.msg);\r\n        }\r\n\r\n        // 设置 finish 和 node 的逻辑\r\n        this.finish = this.$route.query.finish === \"true\";\r\n        console.log(\"this.finish\", this.finish, \"this.isLeader\", this.isLeader);\r\n\t      \r\n\t      // // 判断当前环境\r\n\t      if (process.env.NODE_ENV === \"development\") {\r\n\t\t      this.node = \"one\";\r\n\t\t      return\r\n\t      }\r\n\t\t\t\t\r\n        // 判断是否满足条件\r\n        if (this.finish && this.isLeader) {\r\n          this.node = \"three\";\r\n        } else if (this.finish && !this.isLeader) {\r\n          this.node = \"two\";\r\n        } else {\r\n          this.getEvalExpertStatus();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error during API calls:\", error);\r\n        this.$message.error(\"An error occurred while fetching data.\");\r\n      }\r\n    },\r\n    // 查询专家评审节点信息\r\n    getEvalExpertStatus() {\r\n      // 查询专家评审节点信息\r\n      getEvalExpertScoreInfo({\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        ).projectEvaluationId,\r\n        expertResultId: JSON.parse(localStorage.getItem(\"expertResultId\")),\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        ).scoringMethodItemId,\r\n      }).then((expertStatusResponse) => {\r\n        if (expertStatusResponse.code == 200) {\r\n          localStorage.setItem(\r\n            \"evalExpertScoreInfo\",\r\n            JSON.stringify(expertStatusResponse.data)\r\n          );\r\n          if (expertStatusResponse.data.evalState == 0) {\r\n            this.node = \"one\";\r\n          } else if (expertStatusResponse.data.evalState == 1) {\r\n            this.node = \"two\";\r\n          } else if (expertStatusResponse.data.evalState == 2) {\r\n            if(this.isLeader ){\r\n              this.node = \"three\";\r\n            }else{\r\n              this.node = \"two\";\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    handleData(data) {\r\n      this.node = data;\r\n    },\r\n    // 发送消息给所有专家\r\n    sendMessageToExperts(message) {\r\n      if (this.reviewWebSocket && this.reviewWebSocket.readyState === WebSocket.OPEN) {\r\n        this.reviewWebSocket.send(JSON.stringify(message));\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info {\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.content {\r\n  background-color: #fff;\r\n  width: 90%;\r\n  min-height: 64vh;\r\n  margin: 20px 0;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.little-title {\r\n  color: rgba(80, 80, 80, 1);\r\n  font-size: 14px;\r\n}\r\n.item-button {\r\n  border: #333 1px solid;\r\n  width: 155px;\r\n  height: 48px;\r\n  margin: 20px 28px;\r\n  background-color: rgba(151, 253, 246, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  &:hover {\r\n    color: rgba(0, 0, 0, 1);\r\n  }\r\n}\r\n.item-button-little {\r\n  border: #333 1px solid;\r\n  width: 124px;\r\n  height: 32px;\r\n  background-color: rgba(151, 253, 246, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  &:hover {\r\n    color: rgba(0, 0, 0, 1);\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.title{\r\n\tbackground-color: #c8c9cc;\r\n\tpadding: 10px 5%;\r\n}\r\n</style>"]}]}