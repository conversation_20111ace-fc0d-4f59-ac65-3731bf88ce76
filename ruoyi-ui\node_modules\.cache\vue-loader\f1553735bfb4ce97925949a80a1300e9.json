{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\one.vue?vue&type=style&index=0&id=1b5d009a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\one.vue", "mtime": 1753952311943}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoubWFpbi1jb250YWluZXItb25lIHsNCgltaW4taGVpZ2h0OiA1N3ZoOw0KCWRpc3BsYXk6IGZsZXg7DQp9DQoubGVmdC1wYW5lbCB7DQoJbWluLWhlaWdodDogNTd2aDsNCgl3aWR0aDogNzklOw0KfQ0KLmhlYWRlci1iYXIgew0KCWRpc3BsYXk6IGZsZXg7DQoJanVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KCWFsaWduLWl0ZW1zOiBjZW50ZXI7DQoJYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMxNzZBREI7DQoJcGFkZGluZzogMTVweCAyMHB4Ow0KfQ0KLmhlYWRlci10aXRsZSB7DQoJZGlzcGxheTogZmxleDsNCgloZWlnaHQ6IDM2cHg7DQoJZm9udC13ZWlnaHQ6IDcwMDsNCglmb250LXNpemU6IDI0cHg7DQoJY29sb3I6ICMzMzM7DQp9DQouaGVhZGVyLXN0ZXBzIHsNCglkaXNwbGF5OiBncmlkOw0KCWp1c3RpZnktaXRlbXM6IGNlbnRlcjsNCglwb3NpdGlvbjogcmVsYXRpdmU7DQoJYm90dG9tOiAtMzBweDsNCn0NCi5zdGVwcy10aXAgew0KCWZvbnQtc2l6ZTogMTJweDsNCn0NCi5zdGVwcy1pbWcgew0KCXdpZHRoOiA4MHB4Ow0KCWhlaWdodDogMzBweDsNCgltYXJnaW4tcmlnaHQ6IDIwcHg7DQp9DQouaGVhZGVyLWJ0bnMgew0KCXRleHQtYWxpZ246IHJpZ2h0Ow0KfQ0KLmhlYWRlci1idG5zLWdyb3VwIHsNCgltYXJnaW4tdG9wOiAyMHB4Ow0KfQ0KLml0ZW0tYnV0dG9uLm1haW4gew0KCWJhY2tncm91bmQtY29sb3I6ICMxNzZBREI7DQoJY29sb3I6ICNmZmY7DQoJYm9yZGVyOiAxcHggc29saWQgIzE3NkFEQjsNCn0NCi5wZGYtY29udGFpbmVyIHsNCglkaXNwbGF5OiBmbGV4Ow0KCWp1c3RpZnktY29udGVudDogY2VudGVyOw0KCWhlaWdodDogMTAwJTsNCgltaW4taGVpZ2h0OiA2MDBweDsNCn0NCi5wZGYtdmlldyB7DQoJd2lkdGg6IDQ5JTsNCn0NCi5ib3JkZXItcmlnaHQgew0KCWJvcmRlci1yaWdodDogMXB4IHNvbGlkICMxNzZBREI7DQp9DQouYm9yZGVyLWxlZnQgew0KCWJvcmRlci1sZWZ0OiAxcHggc29saWQgIzE3NkFEQjsNCn0NCi5kaXZpZGVyIHsNCgltaW4taGVpZ2h0OiA1N3ZoOw0KCXdpZHRoOiAxJTsNCgliYWNrZ3JvdW5kLWNvbG9yOiAjRjVGNUY1Ow0KfQ0KLnJpZ2h0LXBhbmVsIHsNCgltaW4taGVpZ2h0OiA1N3ZoOw0KCXdpZHRoOiAyMCU7DQp9DQoucmlnaHQtaGVhZGVyIHsNCglkaXNwbGF5OiBmbGV4Ow0KCWp1c3RpZnktY29udGVudDogY2VudGVyOw0KCWFsaWduLWl0ZW1zOiBjZW50ZXI7DQoJYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMxNzZBREI7DQoJcGFkZGluZzogMTVweCAyMHB4Ow0KfQ0KLnJpZ2h0LWNvbnRlbnQgew0KCXBhZGRpbmc6IDE1cHggMjBweDsNCn0NCi5mYWN0b3ItaXRlbSB7DQoJbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCi5mYWN0b3JzIHsNCglkaXNwbGF5OiBmbGV4Ow0KCWp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCglmbGV4LXdyYXA6IHdyYXA7DQoJYWxpZ24taXRlbXM6IGNlbnRlcjsNCgltYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KLmZhY3Rvci10aXRsZSB7DQoJY3Vyc29yOiBwb2ludGVyOw0KCWZvbnQtZmFtaWx5OiBTb3VyY2VIYW5TYW5zU0MtQm9sZDsNCglmb250LXdlaWdodDogNzAwOw0KCWZvbnQtc2l6ZTogMTZweDsNCgljb2xvcjogIzMzMzsNCglsZXR0ZXItc3BhY2luZzogMDsNCgl3aWR0aDogYXV0bzsNCgl0ZXh0LWFsaWduOiBsZWZ0Ow0KCXRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQoJcGFkZGluZzogNHB4IDhweDsNCglib3JkZXItcmFkaXVzOiA0cHg7DQoNCgkmOmhvdmVyIHsNCgkJYmFja2dyb3VuZC1jb2xvcjogI2YwZjhmZjsNCgkJY29sb3I6ICMxNzZBREI7DQoJCXRyYW5zZm9ybTogdHJhbnNsYXRlWCgycHgpOw0KCX0NCn0NCi5mYWN0b3ItcmFkaW8tZ3JvdXAgew0KCWRpc3BsYXk6IGZsZXg7DQoJd2lkdGg6IDEwMCU7DQoJanVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCn0NCi5mYWN0b3ItZGl2aWRlciB7DQoJaGVpZ2h0OiAxcHg7DQoJYmFja2dyb3VuZC1jb2xvcjogI0RDREZFNjsNCgltYXJnaW4tdG9wOiAxMHB4Ow0KfQ0KLnJpZ2h0LWJ0bnMgew0KCWRpc3BsYXk6IGZsZXg7DQoJbWFyZ2luOiAzMnB4IDA7DQoJanVzdGlmeS1jb250ZW50OiBzcGFjZS1ldmVubHk7DQp9DQoucmV2aWV3LWNvbnRlbnQgew0KCXRleHQtYWxpZ246IGxlZnQ7DQoJZm9udC1zaXplOiAxNHB4Ow0KfQ0KLnJldmlldy10aXRsZSB7DQoJZm9udC1mYW1pbHk6IFNvdXJjZUhhblNhbnNTQy1Cb2xkOw0KCWZvbnQtd2VpZ2h0OiA3MDA7DQoJZm9udC1zaXplOiAxNXB4Ow0KCWNvbG9yOiAjMTc2QURCOw0KCWxldHRlci1zcGFjaW5nOiAwOw0KfQ0KLnJldmlldy1odG1sIHsNCglwYWRkaW5nOiA2cHggMzBweDsNCn0NCi5pdGVtLWJ1dHRvbiB7DQoJYm9yZGVyOiAxcHggc29saWQgIzk3OTc5NzsNCgl3aWR0aDogMTUwcHg7DQoJaGVpZ2h0OiAzNnB4Ow0KCW1hcmdpbjogMCAxMHB4Ow0KCWZvbnQtd2VpZ2h0OiA3MDA7DQoJZm9udC1zaXplOiAxN3B4Ow0KCWJvcmRlci1yYWRpdXM6IDZweDsNCgljb2xvcjogIzMzMzsNCgkmOmhvdmVyIHsNCgkJY29sb3I6ICMzMzM7DQoJfQ0KfQ0KLnF1YWxpZmljYXRpb24tYmx1ZS1idG4gew0KCWJhY2tncm91bmQtY29sb3I6ICMxNzZBREIgIWltcG9ydGFudDsNCgljb2xvcjogI2ZmZiAhaW1wb3J0YW50Ow0KCWJvcmRlcjogMXB4IHNvbGlkICMxNzZBREIgIWltcG9ydGFudDsNCn0NCi5xdWFsaWZpY2F0aW9uLWJsdWUtYnRuLWFjdGl2ZSB7DQoJYmFja2dyb3VuZC1jb2xvcjogI0ZGNkIzNSAhaW1wb3J0YW50Ow0KCWNvbG9yOiAjZmZmICFpbXBvcnRhbnQ7DQoJYm9yZGVyOiAxcHggc29saWQgI0ZGNkIzNSAhaW1wb3J0YW50Ow0KCWJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDI1NSwgMTA3LCA1MywgMC4zKSAhaW1wb3J0YW50Ow0KfQ0KLml0ZW0tYnV0dG9uLWxpdHRsZSB7DQoJd2lkdGg6IDEyNHB4Ow0KCWhlaWdodDogMzZweDsNCglmb250LXdlaWdodDogNzAwOw0KCWZvbnQtc2l6ZTogMThweDsNCgljb2xvcjogI2ZmZjsNCgliYWNrZ3JvdW5kLWNvbG9yOiAjMTc2QURCOw0KCSY6aG92ZXIgew0KCQljb2xvcjogI2ZmZjsNCgl9DQp9DQoudGV4dCB7DQoJOjp2LWRlZXAgLmVsLXRleHRhcmVhX19pbm5lciB7DQoJCWJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7DQoJCWJvcmRlci1yYWRpdXM6IDA7DQoJCWJvcmRlcjogMXB4IHNvbGlkICNmNWY1ZjU7DQoJfQ0KfQ0KDQouZmlsZUxpc3Qgew0KCWRpc3BsYXk6IGZsZXg7DQoJYWxpZ24taXRlbXM6IGNlbnRlcjsNCglnYXA6IDIwcHg7DQoJZmxleDogMTsNCglmbGV4LXdyYXA6IHdyYXA7DQoJLmZpbGVJdGVtIHsNCgkJdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCgkJJjpob3ZlciB7DQoJCQl0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQoJCQlib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7DQoJCX0NCg0KCQk6OnYtZGVlcCAuZWwtY2FyZF9fYm9keSB7DQoJCQlwYWRkaW5nOiAwOw0KCQl9DQoJfQ0KfQ0KDQovLyBQREbmuLLmn5PnirbmgIHmj5DnpLrmoLflvI8NCi5yZW5kZXItc3RhdHVzLXRpcCB7DQoJZGlzcGxheTogZmxleDsNCglhbGlnbi1pdGVtczogY2VudGVyOw0KCXBhZGRpbmc6IDEwcHggMTVweDsNCgltYXJnaW4tYm90dG9tOiAxNXB4Ow0KCWJvcmRlci1yYWRpdXM6IDRweDsNCgliYWNrZ3JvdW5kLWNvbG9yOiAjZmZmN2U2Ow0KCWJvcmRlcjogMXB4IHNvbGlkICNmZmQ1OTE7DQoJY29sb3I6ICNkNDg4MDY7DQoJZm9udC1zaXplOiAxNHB4Ow0KCQ0KCWkgew0KCQltYXJnaW4tcmlnaHQ6IDhweDsNCgkJZm9udC1zaXplOiAxNnB4Ow0KCX0NCgkNCgkmLnN1Y2Nlc3Mgew0KCQliYWNrZ3JvdW5kLWNvbG9yOiAjZjZmZmVkOw0KCQlib3JkZXItY29sb3I6ICNiN2ViOGY7DQoJCWNvbG9yOiAjNTJjNDFhOw0KCX0NCn0NCg0KLy8g56aB55So54q25oCB55qE6K+E5YiG6aG55qCH6aKY5qC35byPDQouZmFjdG9yLXRpdGxlLmRpc2FibGVkIHsNCgljb2xvcjogIzk5OSAhaW1wb3J0YW50Ow0KCWN1cnNvcjogbm90LWFsbG93ZWQgIWltcG9ydGFudDsNCglvcGFjaXR5OiAwLjY7DQoNCgkmOmhvdmVyIHsNCgkJY29sb3I6ICM5OTkgIWltcG9ydGFudDsNCgl9DQp9DQoNCi8vIOaCrOa1ruahhuagt+W8jw0KLmZhY3Rvci10b29sdGlwIHsNCglwb3NpdGlvbjogYWJzb2x1dGU7DQoJcmlnaHQ6IDEwMCU7IC8qIOaYvuekuuWcqOeItuWFg+e0oOW3puS+pyAqLw0KCXRvcDogMDsNCgltYXJnaW4tcmlnaHQ6IDEwcHg7IC8qIOS4juivhOWIhumhueeahOmXtOi3nSAqLw0KCWJhY2tncm91bmQ6ICNmZmY7DQoJYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCglib3JkZXItcmFkaXVzOiA4cHg7DQoJYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOw0KCXdpZHRoOiA0MDBweDsNCgltYXgtaGVpZ2h0OiAzMDBweDsNCglvdmVyZmxvdzogaGlkZGVuOw0KCXotaW5kZXg6IDk5OTk7DQoNCgkudG9vbHRpcC1oZWFkZXIgew0KCQlkaXNwbGF5OiBmbGV4Ow0KCQlqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQoJCWFsaWduLWl0ZW1zOiBjZW50ZXI7DQoJCXBhZGRpbmc6IDEycHggMTZweDsNCgkJYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCgkJYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7DQoNCgkJLnRvb2x0aXAtdGl0bGUgew0KCQkJZm9udC13ZWlnaHQ6IDYwMDsNCgkJCWZvbnQtc2l6ZTogMTRweDsNCgkJCWNvbG9yOiAjMTc2QURCOw0KCQl9DQoNCgkJLnRvb2x0aXAtY2xvc2Ugew0KCQkJY3Vyc29yOiBwb2ludGVyOw0KCQkJY29sb3I6ICM5MDkzOTk7DQoJCQlmb250LXNpemU6IDE0cHg7DQoNCgkJCSY6aG92ZXIgew0KCQkJCWNvbG9yOiAjMTc2QURCOw0KCQkJfQ0KCQl9DQoJfQ0KDQoJLnRvb2x0aXAtY29udGVudCB7DQoJCXBhZGRpbmc6IDE2cHg7DQoJCWZvbnQtc2l6ZTogMTRweDsNCgkJbGluZS1oZWlnaHQ6IDEuNjsNCgkJY29sb3I6ICMzMzM7DQoJCW1heC1oZWlnaHQ6IDI0MHB4Ow0KCQlvdmVyZmxvdy15OiBhdXRvOw0KDQoJCS8vIOe+juWMlua7muWKqOadoQ0KCQkmOjotd2Via2l0LXNjcm9sbGJhciB7DQoJCQl3aWR0aDogNnB4Ow0KCQl9DQoNCgkJJjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KCQkJYmFja2dyb3VuZDogI2YxZjFmMTsNCgkJCWJvcmRlci1yYWRpdXM6IDNweDsNCgkJfQ0KDQoJCSY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCgkJCWJhY2tncm91bmQ6ICNjMWMxYzE7DQoJCQlib3JkZXItcmFkaXVzOiAzcHg7DQoNCgkJCSY6aG92ZXIgew0KCQkJCWJhY2tncm91bmQ6ICNhOGE4YTg7DQoJCQl9DQoJCX0NCgl9DQp9DQoNCi8vIOivhOWIhumhueWuueWZqOebuOWvueWumuS9jQ0KLmZhY3Rvci1pdGVtIHsNCglwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi8vIPCfjq8g6ZyA5rGC5LqM77yaUERG5riy5p+T5pyf6Ze056aB55So54q25oCB5qC35byPDQouaXRlbS1idXR0b246ZGlzYWJsZWQgew0KCW9wYWNpdHk6IDAuNiAhaW1wb3J0YW50Ow0KCWN1cnNvcjogbm90LWFsbG93ZWQgIWltcG9ydGFudDsNCgliYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1ICFpbXBvcnRhbnQ7DQoJY29sb3I6ICNjMGM0Y2MgIWltcG9ydGFudDsNCglib3JkZXItY29sb3I6ICNlNGU3ZWQgIWltcG9ydGFudDsNCn0NCg0KLmVsLXNlbGVjdC5pcy1kaXNhYmxlZCAuZWwtaW5wdXRfX2lubmVyIHsNCgliYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Ow0KCWJvcmRlci1jb2xvcjogI2U0ZTdlZDsNCgljb2xvcjogI2MwYzRjYzsNCgljdXJzb3I6IG5vdC1hbGxvd2VkOw0KfQ0K"}, {"version": 3, "sources": ["one.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA24BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "one.vue", "sourceRoot": "src/views/expertReview/qualification", "sourcesContent": ["<template>\r\n\t<div class=\"main-container-one\">\r\n\t\t<div class=\"left-panel\">\r\n\t\t\t<div class=\"header-bar\">\r\n\t\t\t\t<div class=\"header-title\">\r\n\t\t\t\t\t<div>资格性评审</div>\r\n\t\t\t\t\t<div class=\"header-steps\">\r\n\t\t\t\t\t\t<div class=\"steps-tip\">该页面操作说明</div>\r\n\t\t\t\t\t\t<el-image class=\"steps-img\" :src=\"helpImageList[0]\" :preview-src-list=\"helpImageList\">\r\n\t\t\t\t\t\t</el-image>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 文件列表 -->\r\n\t\t\t\t<div class=\"fileList\" style=\"width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;\">\r\n\t\t\t\t\t<div style=\"font-weight: bold; margin-bottom: 10px; color: #333;\">响应文件附件下载</div>\r\n\t\t\t\t\t<el-card\r\n\t\t\t\t\t\tv-for=\"(item, index) in attachmentsList\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"fileItem\"\r\n\t\t\t\t\t\tshadow=\"hover\"\r\n\t\t\t\t\t\**************=\"downloadFile(item)\"\r\n\t\t\t\t\t\tstyle=\"margin-bottom: 8px; cursor: pointer;\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div style=\"display: flex; align-items: center; padding: 5px;\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409EFF;\"></i>\r\n\t\t\t\t\t\t\t<span style=\"font-size: 12px; flex: 1; word-break: break-all;\">{{ item.fileName }}</span>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-download\" style=\"margin-left: 8px; color: #999;\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-card>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div class=\"header-btns\">\r\n\t\t\t\t\t<el-button class=\"item-button\" @click=\"goToBidInquiry\">询标</el-button>\r\n\t\t\t\t\t<!-- <el-button class=\"item-button\" @click=\"secondOffer\">发起二次报价</el-button> -->\r\n\t\t\t\t\t<div class=\"header-btns-group\">\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'procurement' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t:disabled=\"isAnyPdfRendering()\"\r\n\t\t\t\t\t\t\t@click=\"showProcurementFile\">采购文件</el-button>\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'response' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t:disabled=\"isAnyPdfRendering()\"\r\n\t\t\t\t\t\t\t@click=\"showResponseFile\">响应文件</el-button>\r\n\t\t\t\t\t\t<el-button\r\n\t\t\t\t\t\t\t:class=\"['item-button', activeButton === 'contrast' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']\"\r\n\t\t\t\t\t\t\t:disabled=\"isAnyPdfRendering()\"\r\n\t\t\t\t\t\t\t@click=\"showFileContrast\">对比</el-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div style=\"height:82%\">\r\n\t\t\t\t<!-- PDF预览区域 -->\r\n\t\t\t\t<div class=\"pdf-container\">\r\n\t\t\t\t\t<div v-show=\"isProcurementVisible\" :class=\"['pdf-view', { 'border-right': isDoubleView }]\">\r\n<!--\t\t\t\t\t\t<pdfView ref=\"procurement\" :pdfurl=\"procurementPdfUrl\" :uni_key=\"'procurement'\"></pdfView>-->\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<PdfViewImproved\r\n\t\t\t\t\t\t\tref=\"procurement\"\r\n\t\t\t\t\t\t\t:pdfurl=\"procurementPdfUrl\"\r\n\t\t\t\t\t\t\t:page-height=\"800\"\r\n\t\t\t\t\t\t\t:buffer-size=\"2\"\r\n\t\t\t\t\t\t\t@render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'procurement')\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div v-show=\"isResponseVisible\" :class=\"['pdf-view', { 'border-left': isDoubleView }]\">\r\n<!--\t\t\t\t\t\t<pdfView ref=\"response\" :pdfurl=\"responsePdfUrl\" :uni_key=\"'response'\"></pdfView>-->\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<PdfViewImproved\r\n\t\t\t\t\t\t\tref=\"response\"\r\n\t\t\t\t\t\t\t:pdfurl=\"responsePdfUrl\"\r\n\t\t\t\t\t\t\t:page-height=\"800\"\r\n\t\t\t\t\t\t\t:buffer-size=\"2\"\r\n\t\t\t\t\t\t\t@render-status-change=\"(status) => handlePdfRenderStatusChange(status, 'response')\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"divider\"></div>\r\n\t\t<div class=\"right-panel\">\r\n\t\t\t<div class=\"right-header\">\r\n\t\t\t\t<el-select\r\n\t\t\t\t\tstyle=\"width:100%\"\r\n\t\t\t\t\tv-model=\"selectedSupplierName\"\r\n\t\t\t\t\tplaceholder=\"请选择供应商\"\r\n\t\t\t\t\t:disabled=\"isAnyPdfRendering()\"\r\n\t\t\t\t\t@change=\"handleSupplierChange\">\r\n\t\t\t\t\t<el-option v-for=\"item in supplierOptions\" :key=\"item.bidderName\" :label=\"item.bidderName\" :value=\"item.bidderName\">\r\n\t\t\t\t\t</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"right-content\" v-if=\"isResponseVisible\">\r\n\t\t\t\t<!-- PDF渲染状态提示 -->\r\n\t\t\t\t<div v-if=\"responsePdfUrl && !responsePdfRendered\" class=\"render-status-tip\">\r\n\t\t\t\t\t<i class=\"el-icon-loading\"></i>\r\n\t\t\t\t\t<span>响应文件正在渲染中，请稍候...</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-else-if=\"responsePdfUrl && responsePdfRendered\" class=\"render-status-tip success\">\r\n\t\t\t\t\t<i class=\"el-icon-success\"></i>\r\n\t\t\t\t\t<span>响应文件渲染完成，可以点击跳转</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div v-for=\"(item, index) in scoringSystem.uitems\" :key=\"index\" class=\"factor-item\"\r\n\t\t\t\t\t@mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t<!-- 悬浮框 -->\r\n\t\t\t\t\t<div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t\t\tclass=\"factor-tooltip\"\r\n\t\t\t\t\t\t@mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t\t<div class=\"tooltip-header\">\r\n\t\t\t\t\t\t\t<div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div class=\"factors\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"factor-title\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t\t\t@click=\"jumpToFactorPage(item)\">\r\n\t\t\t\t\t\t\t\t{{ item.itemName }}\r\n\t\t\t\t\t\t\t\t<i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"factor-radio-group\">\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-input v-if=\"(ratingStateMap[item.entMethodItemId].state == 0)\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t<span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t{{checkResultLabel[item.itemName]}}</span>\r\n\t\t\t\t\t\t<div class=\"factor-divider\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"right-btns\">\r\n\t\t\t\t\t<!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n\t\t\t\t\t<div><el-button class=\"item-button-little\" @click=\"submitRating\">提交</el-button></div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"review-content\">\r\n\t\t\t\t\t<div class=\"review-title\">评审内容：</div>\r\n\t\t\t\t\t<div class=\"review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<div class=\"right-content\" v-else>\r\n\t\t\t\t<!-- PDF渲染状态提示 -->\r\n\t\t\t\t<div v-if=\"procurementPdfUrl && !procurementPdfRendered\" class=\"render-status-tip\">\r\n\t\t\t\t\t<i class=\"el-icon-loading\"></i>\r\n\t\t\t\t\t<span>采购文件正在渲染中，请稍候...</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-else-if=\"procurementPdfUrl && procurementPdfRendered\" class=\"render-status-tip success\">\r\n\t\t\t\t\t<i class=\"el-icon-success\"></i>\r\n\t\t\t\t\t<span>采购文件渲染完成，可以点击跳转</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div v-for=\"(item, index) in pageProcurement\" :key=\"index\" class=\"factor-item\"\r\n\t\t\t\t\t@mouseenter=\"showFactorTooltip(item)\"\r\n\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t<!-- 悬浮框 -->\r\n\t\t\t\t\t<div v-if=\"hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId\"\r\n\t\t\t\t\t\tclass=\"factor-tooltip\"\r\n\t\t\t\t\t\t@mouseenter=\"clearTooltipTimer\"\r\n\t\t\t\t\t\t@mouseleave=\"hideFactorTooltip\">\r\n\t\t\t\t\t\t<div class=\"tooltip-header\">\r\n\t\t\t\t\t\t\t<div class=\"tooltip-title\">评审内容</div>\r\n\t\t\t\t\t\t\t<i class=\"el-icon-close tooltip-close\" @click=\"hideFactorTooltip\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"tooltip-content\" v-html=\"item.itemRemark\"></div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div class=\"factors\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclass=\"factor-title\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'disabled': !canJumpToPage() }\"\r\n\t\t\t\t\t\t\t\t@click=\"jumpToFactorPage(item)\">\r\n\t\t\t\t\t\t\t\t{{ item.itemName }}\r\n\t\t\t\t\t\t\t\t<i v-if=\"!canJumpToPage()\" class=\"el-icon-loading\" style=\"margin-left: 5px; font-size: 12px;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"factor-radio-group\">\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"0\"><span style=\"color:red\">不通过</span></el-radio>\r\n\t\t\t\t\t\t\t\t<el-radio v-model=\"ratingStateMap[item.entMethodItemId].state\" label=\"1\"><span style=\"color:green\">通过</span></el-radio>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<el-input v-if=\"(ratingStateMap[item.entMethodItemId].state == 0)\" class=\"text\" type=\"textarea\" :rows=\"3\" placeholder=\"未通过原因\" v-model=\"ratingStateMap[item.entMethodItemId].reason\">\r\n\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t<span v-if=\"Object.keys(checkResult).length > 0\" :style=\"{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }\">\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='1'\" class=\"el-icon-success\"></i>\r\n\t\t\t\t\t\t\t<i v-if=\"getCheckResultState(item.itemName)==='0'\" class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t{{checkResultLabel[item.itemName]}}</span>\r\n\t\t\t\t\t\t<div class=\"factor-divider\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"right-btns\">\r\n\t\t\t\t\t<!-- <div><el-button class=\"item-button-little\" style=\"background-color:#F5F5F5;color:#176ADB\" @click=\"save\">保存</el-button></div> -->\r\n\t\t\t\t\t<div><el-button class=\"item-button-little\" @click=\"submitRating\">提交</el-button></div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"review-content\">\r\n\t\t\t\t\t<div class=\"review-title\">评审内容：</div>\r\n\t\t\t\t\t<div class=\"review-html\" v-html=\"selectedFactorNode.itemRemark\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tsupplierInfo,\r\n\tapprovalProcess,\r\n\tscoringFactors,\r\n\tcheckReviewSummary,\r\n\tfilesById,\r\n} from \"@/api/expert/review\";\r\nimport { getDetailByPsxx } from \"@/api/evaluation/detail/\";\r\nimport { editEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\nimport { resDocReviewFactorsDecision } from \"@/api/docResponse/entInfo\";\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsupplierOptions: [], // 供应商下拉选项\r\n\t\t\tscoringSystem: [], // 当前评分体系\r\n\t\t\tselectedFactorNode: {}, // 当前选中评分项\r\n\t\t\tselectedSupplierName: \"\", // 当前选中供应商名称\r\n\t\t\tselectedSupplier: {}, // 当前选中供应商对象\r\n\t\t\texpertInfo: {}, // 专家信息\r\n\t\t\tratingStateMap: {}, // 评分项状态与原因\r\n\t\t\tfileInfo: {}, // 文件信息\r\n\t\t\tisResponseVisible: false, // 是否显示响应文件\r\n\t\t\tisProcurementVisible: false, // 是否显示采购文件\r\n\t\t\tisDoubleView: false, // 是否对比显示\r\n\t\t\t\r\n\t\t\tentDocResponsePage: {}, // 响应文件页码信息\r\n\t\t\t\r\n\t\t\tfactorDetailList: [], // 评分项详情\r\n\t\t\tfactorsPageMap: {}, // 评分项页码信息\r\n\t\t\t\r\n\t\t\tentDocProcurementPage: {}, // 采购文件页码信息\r\n\t\t\tpageProcurement:[], // 采购文件的评分项\r\n\t\t\tattachmentsList: [], // 文件列表\r\n\t\t\t\r\n\t\t\tsupplierFactorPageMap: {}, // 当前供应商评分项页码\r\n\t\t\tresponsePdfUrl: null, // 响应文件PDF路径\r\n\t\t\tprocurementPdfUrl: null, // 采购文件PDF路径\r\n\r\n\t\t\t// 按钮状态管理\r\n\t\t\tactiveButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'\r\n\r\n\t\t\t// PDF渲染状态管理\r\n\t\t\tresponsePdfRendered: false, // 响应文件PDF是否渲染完成\r\n\t\t\tprocurementPdfRendered: false, // 采购文件PDF是否渲染完成\r\n\r\n\t\t\thelpImageList: [\"/evalution/help.jpg\"], // 步骤图片\r\n\t\t\tfactorKeyMap: { // 评分项与后端字段映射\r\n\t\t\t\t\"特定资格要求\": \"zgzs\",\r\n\t\t\t\t\"响应内容\": \"jsplb\",\r\n\t\t\t\t\"采购需求\": \"jsplb\",\r\n\t\t\t\t\"供货期限\": \"ghqx\",\r\n\t\t\t\t\"投标报价\": \"tbbj\"\r\n\t\t\t},\r\n\t\t\tcheckResult: {}, // 系统初验结果\r\n\t\t\tcheckResultLabel: { // 系统初验结果名称\r\n\t\t\t\t\"符合《中华人民共和国政府采购法》第二十二条规定\": \"系统初验通过\",\r\n\t\t\t\t\"特定资格要求\": \"系统初验通过\",\r\n\t\t\t\t\"信用查询\": \"系统初验通过\",\r\n\t\t\t\t\"响应人名称\": \"系统初验通过\",\r\n\t\t\t\t\"响应内容\": \"系统初验通过\",\r\n\t\t\t\t\"采购需求\": \"系统初验通过\",\r\n\t\t\t\t\"供货期限\": \"系统初验通过\",\r\n\t\t\t\t\"投标报价\": \"系统初验通过\"\r\n\t\t\t},\r\n\r\n\t\t\t// 悬停状态管理\r\n\t\t\thoveredFactorNode: null, // 悬停时的评分项\r\n\t\t\ttooltipTimer: null, // 悬浮框显示定时器\r\n\t\t};\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// ========== 评分相关 ==========\r\n\t\t/**\r\n\t\t * 系统初验结果判断\r\n\t\t * @param {string} factorName 评分项名称\r\n\t\t * @returns {string} 1-通过 0-未通过\r\n\t\t */\r\n\t\tgetCheckResultState(factorName) {\r\n\t\t\tif (!this.checkResult || Object.keys(this.checkResult).length === 0) return \"\"; // 如果没有系统初验结果，则返回空\r\n\t\t\tlet state = \"1\";\r\n\t\t\tconst key = this.factorKeyMap[factorName];\r\n\t\t\tif (key) {\r\n\t\t\t\tstate = this.checkResult[key];\r\n\t\t\t\tif (factorName === \"投标报价\" && state === \"1\") {\r\n\t\t\t\t\tstate = this.checkResult[\"mxbjb\"];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (state === \"0\") {\r\n\t\t\t\tthis.checkResultLabel[factorName] = \"系统初验未通过\";\r\n\t\t\t} else {\r\n\t\t\t\tstate = \"1\";\r\n\t\t\t\tthis.checkResultLabel[factorName] = \"系统初验通过\";\r\n\t\t\t}\r\n\t\t\treturn state;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 校验所有评分项是否填写完整\r\n\t\t * @returns {boolean} 是否全部填写\r\n\t\t */\r\n\t\tvalidateAllRatings() {\r\n\t\t\tfor (const item of this.scoringSystem.uitems) {\r\n\t\t\t\tconst state = this.ratingStateMap[item.entMethodItemId].state;\r\n\t\t\t\tconst reason = this.ratingStateMap[item.entMethodItemId].reason;\r\n\t\t\t\t// 评分结果未填写\r\n\t\t\t\tif (state === null || state === '') {\r\n\t\t\t\t\t// this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t\t// 不通过但未填写原因 - 将评审项设置为空，然后继续执行后续流程\r\n\t\t\t\tif (state === \"0\" && (!reason || reason.trim() === '')) {\r\n\t\t\t\t\t// 将此评审项设置为空（未评审状态）\r\n\t\t\t\t\tthis.ratingStateMap[item.entMethodItemId].state = null;\r\n\t\t\t\t\tthis.ratingStateMap[item.entMethodItemId].reason = \"\";\r\n\t\t\t\t\tconsole.log(`${item.itemName}评审不通过但未填写备注，已将该评审项设置为空`);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\r\n\t\t// 生成保存数据\r\n\t\tgenerateSaveData() {\r\n\t\t\tconst ratingCopy = JSON.parse(JSON.stringify(this.ratingStateMap)); // 评分项状态\r\n\t\t\tconst data = []; // 保存数据\r\n\t\t\tfor (const item of this.scoringSystem.uitems) { // 遍历评分项\r\n\t\t\t\tconst itemId = item.entMethodItemId; // 评分项ID\r\n\t\t\t\tconst evaluationResult = ratingCopy[itemId].state; // 评分项状态\r\n\t\t\t\tif (evaluationResult === null || evaluationResult === \"\") continue; // 如果评分项状态为空，则跳过\r\n\t\t\t\t// 注意：不通过原因的校验已经在validateAllRatings中处理，这里只需要构建数据\r\n\t\t\t\tconst evaluationRemark = ratingCopy[itemId].reason || \"\"; // 评分项备注\r\n\t\t\t\tdata.push({\r\n\t\t\t\t\tscoringMethodUitemId: itemId, // 评分项ID\r\n\t\t\t\t\texpertResultId: this.expertInfo.resultId, // 专家ID\r\n\t\t\t\t\tentId: this.selectedSupplier.bidderId, // 供应商ID\r\n\t\t\t\t\tevaluationResult, // 评分项状态\r\n\t\t\t\t\tevaluationRemark // 评分项备注\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\treturn data; // 返回保存数据\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 临时保存评分结果\r\n\t\t * @returns {Promise}\r\n\t\t */\r\n\t\tsaveRatingTemp() {\r\n\t\t\t// 先校验所有评分项是否填写完整\r\n\t\t\tif (!this.validateAllRatings()) {\r\n\t\t\t\treturn Promise.resolve({ code: 0, success: false }); // 校验失败\r\n\t\t\t}\r\n\r\n\t\t\tconst data = this.generateSaveData(); // 生成保存数据\r\n\t\t\tif (data.length > 0) {\r\n\t\t\t\treturn scoringFactors(data).then(response => {\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tthis.$message.success(\"保存成功\");\r\n\t\t\t\t\t\treturn { code: 200, success: true };\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t\t\treturn { code: response.code, success: false };\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch((error) => {\r\n\t\t\t\t\tthis.$message.error(\"保存失败\");\r\n\t\t\t\t\treturn { code: 0, success: false };\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\treturn Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 提交评分结果\r\n\t\t */\r\n\t\tsubmitRating() {\r\n\t\t\tthis.saveRatingTemp().then((saveResult) => {\r\n\t\t\t\t// 检查保存结果，如果校验失败则不继续提交\r\n\t\t\t\tif (!saveResult || saveResult.success === false) {\r\n\t\t\t\t\treturn; // 校验失败，不继续提交流程\r\n\t\t\t\t}\r\n\t\t\t\tthis.checkAndSubmitReview();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 检查并提交评审汇总\r\n\t\tcheckAndSubmitReview() {\r\n\t\t\t\tconst data = {\r\n\t\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\t\texpertResultId: this.expertInfo.resultId,\r\n\t\t\t\t\tscoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n\t\t\t\t};\r\n\t\t\t\tcheckReviewSummary(data).then((response) => {\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tthis.updateExpertScoreStatus(); // 修改专家进度状态\r\n\t\t\t\t\t\tthis.$emit(\"send\", \"two\"); // 跳转至二次报价\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 修改专家进度状态\r\n\t\tupdateExpertScoreStatus() {\r\n\t\t\tconst status = {\r\n\t\t\t\tevalExpertScoreInfoId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\")).evalExpertScoreInfoId,\r\n\t\t\t\tevalState: 1,\r\n\t\t\t};\r\n\t\t\teditEvalExpertScoreInfo(status).then((res) => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tthis.$message.success(\"提交成功\");\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 初始化相关 ==========\r\n\t\t/**\r\n\t\t * 初始化页面数据\r\n\t\t */\r\n\t\tinitPage() {\r\n\t\t\tthis.initExpertInfo();// 初始化专家信息\r\n\t\t\tthis.initEntDocResponsePage(); // 初始化响应文件页码信息\r\n\t\t\tthis.initEntDocProcurementPage(); // 初始化采购文件页码信息\r\n\t\t\tthis.loadSupplierOptions();// 加载供应商下拉选项\r\n\t\t\tthis.loadScoringSystem(); // 加载评分体系\r\n\t\t\tthis.loadFiles(); // 加载文件信息\r\n\t\t},\r\n\t\t// 初始化专家信息\r\n\t\tinitExpertInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst expertInfoStr = localStorage.getItem(\"expertInfo\");\r\n\t\t\t\tif (expertInfoStr) {\r\n\t\t\t\t\tthis.expertInfo = JSON.parse(expertInfoStr);\r\n\t\t\t\t\tconsole.log(\"专家信息已初始化\", this.expertInfo);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn(\"localStorage中未找到expertInfo\");\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"初始化专家信息失败:\", error);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 初始化响应文件页码信息\r\n\t\tinitEntDocResponsePage() {\r\n\t\t\tthis.entDocResponsePage = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n\t\t},\r\n\t\t// 初始化采购文件页码信息\r\n\t\tinitEntDocProcurementPage() {\r\n\t\t\tthis.entDocProcurementPage = JSON.parse(localStorage.getItem(\"entDocProcurementPage\"));\r\n\t\t},\r\n\t\t// 加载供应商下拉选项\r\n\t\tloadSupplierOptions() {\r\n\t\t\tsupplierInfo({ projectId: this.$route.query.projectId }).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.supplierOptions = response.rows.filter(item => item.isAbandonedBid === 0); // 过滤掉被放弃的投标\r\n\t\t\t\t\tconsole.log(this.supplierOptions);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 加载评分体系及初始化评分项状态\r\n\t\tloadScoringSystem() {\r\n\t\t\tapprovalProcess(this.$route.query.projectId, this.expertInfo.resultId).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t// 文件列表\r\n\t\t\t\t\tthis.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == \"0\");\r\n\r\n\t\t\t\t\tthis.scoringSystem = response.data.scoringMethodUinfo.scoringMethodItems.find(\r\n\t\t\t\t\t\titem => item.scoringMethodItemId == this.$route.query.scoringMethodItemId\r\n\t\t\t\t\t); // 获取当前评分项\r\n\t\t\t\t\tlocalStorage.setItem(\r\n\t\t\t\t\t\t\"evalProjectEvaluationProcess\",\r\n\t\t\t\t\t\tJSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)\r\n\t\t\t\t\t); // 保存评分体系\r\n\t\t\t\t\tconsole.log(this.scoringSystem);\r\n\t\t\t\t\tthis.initRatingStateMapBySystem(); // 初始化评分项状态\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 初始化评分项状态（根据评分体系）\r\n\t\tinitRatingStateMapBySystem() {\r\n\t\t\tthis.ratingStateMap = this.scoringSystem.uitems.reduce((acc, item) => {\r\n\t\t\t\tacc[item.entMethodItemId] = { state: null, reason: \"\" };\r\n\t\t\t\treturn acc;\r\n\t\t\t}, {}); // 初始化评分项状态\r\n\t\t},\r\n\t\t// 加载文件信息\r\n\t\tloadFiles() {\r\n\t\t\tfilesById(this.$route.query.projectId).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.fileInfo = response.data; // 文件信息\r\n\t\t\t\t\t// 🎯 需求一：不自动设置采购文件URL，只在用户点击时才设置\r\n\t\t\t\t\t// if (this.fileInfo.tenderNoticeFilePath) {\r\n\t\t\t\t\t// \tthis.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// 不自动设置响应文件URL，只在需要时才设置\r\n\t\t\t\t\t// if (this.fileInfo.file) {\r\n\t\t\t\t\t// \tthis.responsePdfUrl = this.fileInfo.file[0]; // 响应文件\r\n\t\t\t\t\t// }\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 供应商相关 ==========\r\n\t\t/**\r\n\t\t * 供应商切换时处理\r\n\t\t * @param {string} supplierName 供应商名称\r\n\t\t */\r\n\t\thandleSupplierChange(supplierName) {\r\n\t\t\t// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换供应商\r\n\t\t\tif (this.isAnyPdfRendering()) {\r\n\t\t\t\tthis.$message.warning(\"PDF文件正在渲染中，请稍候再切换供应商\");\r\n\t\t\t\t// 恢复之前的选择\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.selectedSupplierName = this.selectedSupplier.bidderName || \"\";\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.isResponseVisible = true\r\n\t\t\tif (Object.keys(this.selectedSupplier).length !== 0) {\r\n\t\t\t\tthis.saveRatingTemp();\r\n\t\t\t}\r\n\t\t\tthis.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName) || {}; // 获取当前供应商\r\n\t\t\tthis.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码\r\n\t\t\tthis.loadSupplierFactorDetail(supplierName); // 加载当前供应商评分项详情\r\n\t\t\tthis.loadSupplierCheckResult(); // 加载当前供应商系统初验结果\r\n\t\t\tthis.showResponseFile(); // 显示响应文件\r\n\t\t},\r\n\t\t// 加载当前供应商评分项详情\r\n\t\tloadSupplierFactorDetail(bidderName) {\r\n\t\t\tthis.clearRatingStateMap();\r\n\t\t\t\r\n\t\t\tconst detailData = {\r\n\t\t\t\texpertResultId: this.expertInfo.resultId,\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tscoringMethodItemId: this.$route.query.scoringMethodItemId,\r\n\t\t\t};\r\n\t\t\tgetDetailByPsxx(detailData).then((response) => {\r\n\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\tthis.factorDetailList = response.data;\r\n\t\t\t\t\tconst factor = this.factorDetailList.find(item => item.bidderName === bidderName)?.evalExpertEvaluationDetails;\r\n\t\t\t\t\tif (factor) {\r\n\t\t\t\t\t\tthis.setRatingStateMapByFactor(factor);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.clearRatingStateMap();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.warning(response.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 根据评分详情设置评分项状态\r\n\t\tsetRatingStateMapByFactor(factor) {\r\n\t\t\tfactor.forEach(item => {\r\n\t\t\t\tthis.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark;\r\n\t\t\t\tthis.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult;\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 加载当前供应商系统初验结果\r\n\t\tloadSupplierCheckResult() {\r\n\t\t\tconst reviewData = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tentId: this.selectedSupplier.bidderId,\r\n\t\t\t};\r\n\t\t\tresDocReviewFactorsDecision(reviewData).then((res) => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tthis.checkResult = res.data;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 初始化评分项状态（清空）\r\n\t\tclearRatingStateMap() {\r\n\t\t\tObject.keys(this.ratingStateMap).forEach(key => {\r\n\t\t\t\tthis.ratingStateMap[key].state = null;\r\n\t\t\t\tthis.ratingStateMap[key].reason = \"\";\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// ========== 文件相关 ==========\r\n\t\t/**\r\n\t\t * 显示响应文件\r\n\t\t */\r\n\t\tshowResponseFile() {\r\n\t\t\tif (Object.keys(this.selectedSupplier).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请选择供应商\");\r\n\t\t\t} else {\r\n\t\t\t\t// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换\r\n\t\t\t\tif (this.isAnyPdfRendering()) {\r\n\t\t\t\t\tthis.$message.warning(\"PDF文件正在渲染中，请稍候再切换\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.activeButton = 'response'; // 设置当前激活按钮\r\n\t\t\t\tthis.isDoubleView = false; // 关闭对比\r\n\t\t\t\tthis.isProcurementVisible = false; // 关闭采购文件\r\n\t\t\t\tthis.isResponseVisible = true; // 显示响应文件\r\n\r\n\t\t\t\t// 🎯 只在用户点击时才设置响应文件URL，开始渲染\r\n\t\t\t\tthis.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件\r\n\r\n\t\t\t\t// 右侧评分项显示为响应文件的评分项\r\n\t\t\t\tif (Object.keys(this.selectedSupplier).length !== 0) {\r\n\t\t\t\t\tthis.saveRatingTemp();\r\n\t\t\t\t}\r\n\t\t\t\tthis.selectedSupplier = this.supplierOptions.find(item => item.bidderName === this.selectedSupplier.bidderName) || {}; // 获取当前供应商\r\n\t\t\t\tthis.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码\r\n\t\t\t\tthis.loadSupplierFactorDetail(this.selectedSupplier.bidderName); // 加载当前供应商评分项详情\r\n\t\t\t\tthis.loadSupplierCheckResult(); // 加载当前供应商系统初验结果\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 文件对比显示\r\n\t\t */\r\n\t\tshowFileContrast() {\r\n\t\t\tif (Object.keys(this.selectedSupplier).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请选择供应商\");\r\n\t\t\t} else {\r\n\t\t\t\t// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换\r\n\t\t\t\tif (this.isAnyPdfRendering()) {\r\n\t\t\t\t\tthis.$message.warning(\"PDF文件正在渲染中，请稍候再切换\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.activeButton = 'contrast'; // 设置当前激活按钮\r\n\t\t\t\tthis.isDoubleView = true;\r\n\t\t\t\tthis.isProcurementVisible = true;\r\n\t\t\t\tthis.isResponseVisible = true;\r\n\r\n\t\t\t\t// 🎯 只在用户点击对比时才设置文件URL，开始渲染\r\n\t\t\t\tthis.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件\r\n\t\t\t\tif (this.fileInfo.tenderNoticeFilePath) {\r\n\t\t\t\t\tthis.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 查看采购文件\r\n\t\t */\r\n\t\tshowProcurementFile() {\r\n\t\t\t// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换\r\n\t\t\tif (this.isAnyPdfRendering()) {\r\n\t\t\t\tthis.$message.warning(\"PDF文件正在渲染中，请稍候再切换\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.activeButton = 'procurement'; // 设置当前激活按钮\r\n\t\t\tthis.isDoubleView = false;\r\n\t\t\tthis.isResponseVisible = false;\r\n\t\t\tthis.isProcurementVisible = true;\r\n\r\n\t\t\t// 🎯 需求一：只在用户点击时才设置采购文件URL，开始渲染\r\n\t\t\tif (this.fileInfo.tenderNoticeFilePath) {\r\n\t\t\t\tthis.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件\r\n\t\t\t}\r\n\r\n\t\t\t// 右侧评分项显示为采购文件的评分项\r\n\t\t\tlet pageProcurementArr = [];\r\n\t\t\tfor (let item in this.entDocProcurementPage){\r\n\t\t\t\tpageProcurementArr.push({\r\n\t\t\t\t\titemName: item,\r\n\t\t\t\t\tjumpToPage: this.entDocProcurementPage[item]\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log(this.scoringSystem.uitems);\r\n\t\t\tconsole.log(pageProcurementArr)\r\n\t\t\tthis.pageProcurement = [];\r\n\t\t\tfor (let i = 0; i < this.scoringSystem.uitems.length;i++){\r\n\t\t\t\tfor (let j = 0; j < pageProcurementArr.length;j++){\r\n\t\t\t\t\tif (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){\r\n\t\t\t\t\t\tthis.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tconsole.log(this.pageProcurement)\r\n\t\t},\r\n\r\n\t\t// ========== 页面跳转相关 ==========\r\n\t\t/**\r\n\t\t * 跳转到评分项对应页码\r\n\t\t * @param {Object} factorItem 评分项对象\r\n\t\t */\r\n\t\tjumpToFactorPage(factorItem) {\r\n\t\t\t// 检查PDF是否渲染完成\r\n\t\t\tif (!this.canJumpToPage()) {\r\n\t\t\t\tthis.$message.warning(\"PDF页面正在渲染中，请稍候再试\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.selectedFactorNode = factorItem; // 设置当前选中因子\r\n\r\n\t\t\t// 如果只显示采购文件，使用采购文件页码信息\r\n\t\t\tif (this.isProcurementVisible && !this.isResponseVisible) {\r\n\t\t\t\tif (!this.procurementPdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (factorItem.jumpToPage) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页\r\n\t\t\t\t} else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果显示响应文件或对比模式，需要选择供应商\r\n\t\t\tif (!this.supplierFactorPageMap || Object.keys(this.supplierFactorPageMap).length === 0) {\r\n\t\t\t\tthis.$message.warning(\"请先选择供应商\"); // 未选供应商提示\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 跳转到响应文件对应页码\r\n\t\t\tif (this.isResponseVisible && this.$refs.response) {\r\n\t\t\t\tif (!this.responsePdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"响应文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.response.skipPage(this.supplierFactorPageMap[this.selectedFactorNode.itemName]); // 响应文件跳页\r\n\t\t\t}\r\n\r\n\t\t\t// 跳转到采购文件对应页码\r\n\t\t\tif (this.isProcurementVisible && this.$refs.procurement) {\r\n\t\t\t\tif (!this.procurementPdfRendered) {\r\n\t\t\t\t\tthis.$message.warning(\"采购文件正在渲染中，请稍候再试\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码\r\n\t\t\t\tif (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {\r\n\t\t\t\t\tthis.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件\r\n\t\t\t\t\t// 这样可以避免采购文件和响应文件显示不同的内容造成混淆\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 检查是否可以跳转页面\r\n\t\t * @returns {boolean} 是否可以跳转\r\n\t\t */\r\n\t\tcanJumpToPage() {\r\n\t\t\t// 如果只显示采购文件\r\n\t\t\tif (this.isProcurementVisible && !this.isResponseVisible) {\r\n\t\t\t\treturn this.procurementPdfRendered;\r\n\t\t\t}\r\n\t\t\t// 如果只显示响应文件\r\n\t\t\tif (this.isResponseVisible && !this.isProcurementVisible) {\r\n\t\t\t\treturn this.responsePdfRendered;\r\n\t\t\t}\r\n\t\t\t// 如果对比模式（两个都显示）\r\n\t\t\tif (this.isResponseVisible && this.isProcurementVisible) {\r\n\t\t\t\treturn this.responsePdfRendered && this.procurementPdfRendered;\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 🎯 需求二：检查是否有任何PDF正在渲染\r\n\t\t * @returns {boolean} 是否有PDF正在渲染\r\n\t\t */\r\n\t\tisAnyPdfRendering() {\r\n\t\t\t// 检查当前显示的PDF是否正在渲染\r\n\t\t\tif (this.isProcurementVisible && this.procurementPdfUrl && !this.procurementPdfRendered) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\tif (this.isResponseVisible && this.responsePdfUrl && !this.responsePdfRendered) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 处理PDF渲染状态变化\r\n\t\t * @param {boolean} isRendered 是否渲染完成\r\n\t\t * @param {string} pdfType PDF类型：'response' 或 'procurement'\r\n\t\t */\r\n\t\thandlePdfRenderStatusChange(isRendered, pdfType) {\r\n\t\t\tif (pdfType === 'response') {\r\n\t\t\t\tthis.responsePdfRendered = isRendered;\r\n\t\t\t} else if (pdfType === 'procurement') {\r\n\t\t\t\tthis.procurementPdfRendered = isRendered;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (isRendered) {\r\n\t\t\t\tconsole.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 跳转到二次报价\r\n\t\t */\r\n\t\tgoToSecondOffer() {\r\n\t\t\tconst query = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tzjhm: this.$route.query.zjhm,\r\n\t\t\t\tscoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")),\r\n\t\t\t};\r\n\t\t\tthis.$router.push({ path: \"/secondOffer\", query });\r\n\t\t},\r\n\t\t/**\r\n\t\t * 跳转到询标\r\n\t\t */\r\n\t\tgoToBidInquiry() {\r\n\t\t\tconst query = {\r\n\t\t\t\tprojectId: this.$route.query.projectId,\r\n\t\t\t\tzjhm: this.$route.query.zjhm,\r\n\t\t\t\tscoringMethodItemId: JSON.parse(localStorage.getItem(\"tenderOfferScoringMethodItems\")),\r\n\t\t\t};\r\n\t\t\tthis.$router.push({ path: \"/bidInquiry\", query });\r\n\t\t},\r\n\t\t/**\r\n\t\t * 获取因素对应页码\r\n\t\t */\r\n\t\tloadFactorsPageMap() {\r\n\t\t\tthis.factorsPageMap = JSON.parse(localStorage.getItem(\"entDocResponsePage\"));\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 下载文件\r\n\t\t * @param {Object} item - 文件对象\r\n\t\t */\r\n\t\tdownloadFile(item) {\r\n\t\t\tthis.$download.zip(item.filePath, item.fileName);\r\n\t\t},\r\n\r\n\t\t// ========== 悬停相关 ==========\r\n\t\t/**\r\n\t\t * 显示评分项悬浮框\r\n\t\t * @param {Object} factorItem 评分项对象\r\n\t\t */\r\n\t\tshowFactorTooltip(factorItem) {\r\n\t\t\tif (!factorItem.itemRemark) return; // 如果没有评审内容则不显示\r\n\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t}\r\n\r\n\t\t\t// 延迟显示悬浮框，避免快速移动时频繁显示\r\n\t\t\tthis.tooltipTimer = setTimeout(() => {\r\n\t\t\t\tthis.hoveredFactorNode = factorItem;\r\n\t\t\t}, 300); // 300ms延迟\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 隐藏评分项悬浮框\r\n\t\t */\r\n\t\thideFactorTooltip() {\r\n\t\t\t// 清除定时器\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t\tthis.tooltipTimer = null;\r\n\t\t\t}\r\n\r\n\t\t\t// 延迟隐藏，给用户时间移动到悬浮框上\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.hoveredFactorNode = null;\r\n\t\t\t}, 100);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 清除悬浮框定时器（当鼠标移动到悬浮框上时）\r\n\t\t */\r\n\t\tclearTooltipTimer() {\r\n\t\t\tif (this.tooltipTimer) {\r\n\t\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\t\tthis.tooltipTimer = null;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n},\r\n\r\n\tmounted() {\r\n\t\tthis.initPage();\r\n\t\tthis.loadFactorsPageMap();\r\n\t},\r\n\r\n\tbeforeDestroy() {\r\n\t\t// 清理定时器\r\n\t\tif (this.tooltipTimer) {\r\n\t\t\tclearTimeout(this.tooltipTimer);\r\n\t\t\tthis.tooltipTimer = null;\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.main-container-one {\r\n\tmin-height: 57vh;\r\n\tdisplay: flex;\r\n}\r\n.left-panel {\r\n\tmin-height: 57vh;\r\n\twidth: 79%;\r\n}\r\n.header-bar {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tborder-bottom: 2px solid #176ADB;\r\n\tpadding: 15px 20px;\r\n}\r\n.header-title {\r\n\tdisplay: flex;\r\n\theight: 36px;\r\n\tfont-weight: 700;\r\n\tfont-size: 24px;\r\n\tcolor: #333;\r\n}\r\n.header-steps {\r\n\tdisplay: grid;\r\n\tjustify-items: center;\r\n\tposition: relative;\r\n\tbottom: -30px;\r\n}\r\n.steps-tip {\r\n\tfont-size: 12px;\r\n}\r\n.steps-img {\r\n\twidth: 80px;\r\n\theight: 30px;\r\n\tmargin-right: 20px;\r\n}\r\n.header-btns {\r\n\ttext-align: right;\r\n}\r\n.header-btns-group {\r\n\tmargin-top: 20px;\r\n}\r\n.item-button.main {\r\n\tbackground-color: #176ADB;\r\n\tcolor: #fff;\r\n\tborder: 1px solid #176ADB;\r\n}\r\n.pdf-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\theight: 100%;\r\n\tmin-height: 600px;\r\n}\r\n.pdf-view {\r\n\twidth: 49%;\r\n}\r\n.border-right {\r\n\tborder-right: 1px solid #176ADB;\r\n}\r\n.border-left {\r\n\tborder-left: 1px solid #176ADB;\r\n}\r\n.divider {\r\n\tmin-height: 57vh;\r\n\twidth: 1%;\r\n\tbackground-color: #F5F5F5;\r\n}\r\n.right-panel {\r\n\tmin-height: 57vh;\r\n\twidth: 20%;\r\n}\r\n.right-header {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tborder-bottom: 2px solid #176ADB;\r\n\tpadding: 15px 20px;\r\n}\r\n.right-content {\r\n\tpadding: 15px 20px;\r\n}\r\n.factor-item {\r\n\tmargin-bottom: 10px;\r\n}\r\n.factors {\r\n\tdisplay: flex;\r\n\tjustify-content: flex-start;\r\n\tflex-wrap: wrap;\r\n\talign-items: center;\r\n\tmargin-bottom: 10px;\r\n}\r\n.factor-title {\r\n\tcursor: pointer;\r\n\tfont-family: SourceHanSansSC-Bold;\r\n\tfont-weight: 700;\r\n\tfont-size: 16px;\r\n\tcolor: #333;\r\n\tletter-spacing: 0;\r\n\twidth: auto;\r\n\ttext-align: left;\r\n\ttransition: all 0.3s ease;\r\n\tpadding: 4px 8px;\r\n\tborder-radius: 4px;\r\n\r\n\t&:hover {\r\n\t\tbackground-color: #f0f8ff;\r\n\t\tcolor: #176ADB;\r\n\t\ttransform: translateX(2px);\r\n\t}\r\n}\r\n.factor-radio-group {\r\n\tdisplay: flex;\r\n\twidth: 100%;\r\n\tjustify-content: flex-end;\r\n}\r\n.factor-divider {\r\n\theight: 1px;\r\n\tbackground-color: #DCDFE6;\r\n\tmargin-top: 10px;\r\n}\r\n.right-btns {\r\n\tdisplay: flex;\r\n\tmargin: 32px 0;\r\n\tjustify-content: space-evenly;\r\n}\r\n.review-content {\r\n\ttext-align: left;\r\n\tfont-size: 14px;\r\n}\r\n.review-title {\r\n\tfont-family: SourceHanSansSC-Bold;\r\n\tfont-weight: 700;\r\n\tfont-size: 15px;\r\n\tcolor: #176ADB;\r\n\tletter-spacing: 0;\r\n}\r\n.review-html {\r\n\tpadding: 6px 30px;\r\n}\r\n.item-button {\r\n\tborder: 1px solid #979797;\r\n\twidth: 150px;\r\n\theight: 36px;\r\n\tmargin: 0 10px;\r\n\tfont-weight: 700;\r\n\tfont-size: 17px;\r\n\tborder-radius: 6px;\r\n\tcolor: #333;\r\n\t&:hover {\r\n\t\tcolor: #333;\r\n\t}\r\n}\r\n.qualification-blue-btn {\r\n\tbackground-color: #176ADB !important;\r\n\tcolor: #fff !important;\r\n\tborder: 1px solid #176ADB !important;\r\n}\r\n.qualification-blue-btn-active {\r\n\tbackground-color: #FF6B35 !important;\r\n\tcolor: #fff !important;\r\n\tborder: 1px solid #FF6B35 !important;\r\n\tbox-shadow: 0 2px 4px rgba(255, 107, 53, 0.3) !important;\r\n}\r\n.item-button-little {\r\n\twidth: 124px;\r\n\theight: 36px;\r\n\tfont-weight: 700;\r\n\tfont-size: 18px;\r\n\tcolor: #fff;\r\n\tbackground-color: #176ADB;\r\n\t&:hover {\r\n\t\tcolor: #fff;\r\n\t}\r\n}\r\n.text {\r\n\t::v-deep .el-textarea__inner {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 0;\r\n\t\tborder: 1px solid #f5f5f5;\r\n\t}\r\n}\r\n\r\n.fileList {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20px;\r\n\tflex: 1;\r\n\tflex-wrap: wrap;\r\n\t.fileItem {\r\n\t\ttransition: all 0.3s ease;\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\r\n\t\t::v-deep .el-card__body {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// PDF渲染状态提示样式\r\n.render-status-tip {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10px 15px;\r\n\tmargin-bottom: 15px;\r\n\tborder-radius: 4px;\r\n\tbackground-color: #fff7e6;\r\n\tborder: 1px solid #ffd591;\r\n\tcolor: #d48806;\r\n\tfont-size: 14px;\r\n\t\r\n\ti {\r\n\t\tmargin-right: 8px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t&.success {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tborder-color: #b7eb8f;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n}\r\n\r\n// 禁用状态的评分项标题样式\r\n.factor-title.disabled {\r\n\tcolor: #999 !important;\r\n\tcursor: not-allowed !important;\r\n\topacity: 0.6;\r\n\r\n\t&:hover {\r\n\t\tcolor: #999 !important;\r\n\t}\r\n}\r\n\r\n// 悬浮框样式\r\n.factor-tooltip {\r\n\tposition: absolute;\r\n\tright: 100%; /* 显示在父元素左侧 */\r\n\ttop: 0;\r\n\tmargin-right: 10px; /* 与评分项的间距 */\r\n\tbackground: #fff;\r\n\tborder: 1px solid #e4e7ed;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n\twidth: 400px;\r\n\tmax-height: 300px;\r\n\toverflow: hidden;\r\n\tz-index: 9999;\r\n\r\n\t.tooltip-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder-bottom: 1px solid #e4e7ed;\r\n\r\n\t\t.tooltip-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #176ADB;\r\n\t\t}\r\n\r\n\t\t.tooltip-close {\r\n\t\t\tcursor: pointer;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 14px;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: #176ADB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tooltip-content {\r\n\t\tpadding: 16px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #333;\r\n\t\tmax-height: 240px;\r\n\t\toverflow-y: auto;\r\n\r\n\t\t// 美化滚动条\r\n\t\t&::-webkit-scrollbar {\r\n\t\t\twidth: 6px;\r\n\t\t}\r\n\r\n\t\t&::-webkit-scrollbar-track {\r\n\t\t\tbackground: #f1f1f1;\r\n\t\t\tborder-radius: 3px;\r\n\t\t}\r\n\r\n\t\t&::-webkit-scrollbar-thumb {\r\n\t\t\tbackground: #c1c1c1;\r\n\t\t\tborder-radius: 3px;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: #a8a8a8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 评分项容器相对定位\r\n.factor-item {\r\n\tposition: relative;\r\n}\r\n\r\n// 🎯 需求二：PDF渲染期间禁用状态样式\r\n.item-button:disabled {\r\n\topacity: 0.6 !important;\r\n\tcursor: not-allowed !important;\r\n\tbackground-color: #f5f5f5 !important;\r\n\tcolor: #c0c4cc !important;\r\n\tborder-color: #e4e7ed !important;\r\n}\r\n\r\n.el-select.is-disabled .el-input__inner {\r\n\tbackground-color: #f5f5f5;\r\n\tborder-color: #e4e7ed;\r\n\tcolor: #c0c4cc;\r\n\tcursor: not-allowed;\r\n}\r\n</style>\r\n"]}]}