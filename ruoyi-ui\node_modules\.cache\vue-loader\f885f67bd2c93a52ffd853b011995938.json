{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\technical.vue?vue&type=style&index=0&id=2326e64a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\technical.vue", "mtime": 1753956962104}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmluZm8gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCi5jb250ZW50IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgd2lkdGg6IDkwJTsNCiAgbWluLWhlaWdodDogNzF2aDsNCiAgbWFyZ2luOiAyMHB4IDA7DQp9DQouaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBmb250LXNpemU6IDE4cHg7DQogIG1hcmdpbi1ib3R0b206IDgwcHg7DQogIC5pdGVtLXRpdGxlIHsNCiAgICB3aWR0aDogMTIwcHg7DQogICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgIHRleHQtYWxpZ246IGxlZnQ7DQogIH0NCn0NCi5saXR0bGUtdGl0bGUgew0KICBjb2xvcjogcmdiYSg4MCwgODAsIDgwLCAxKTsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KLml0ZW0tYnV0dG9uIHsNCiAgYm9yZGVyOiAjMzMzIDFweCBzb2xpZDsNCiAgd2lkdGg6IDE1NXB4Ow0KICBoZWlnaHQ6IDQ4cHg7DQogIG1hcmdpbjogMjBweCAyOHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDE1MSwgMjUzLCAyNDYsIDEpOw0KICBjb2xvcjogcmdiYSgwLCAwLCAwLCAxKTsNCiAgJjpob3ZlciB7DQogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMSk7DQogIH0NCn0NCi5pdGVtLWJ1dHRvbi1saXR0bGUgew0KICBib3JkZXI6ICMzMzMgMXB4IHNvbGlkOw0KICB3aWR0aDogMTI0cHg7DQogIGhlaWdodDogMzJweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxNTEsIDI1MywgMjQ2LCAxKTsNCiAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMSk7DQogICY6aG92ZXIgew0KICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDEpOw0KICB9DQp9DQouZmFjdG9ycyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLnRpdGxlew0KCWJhY2tncm91bmQtY29sb3I6ICNjOGM5Y2M7DQoJcGFkZGluZzogMTBweCA1JTsNCn0NCg=="}, {"version": 3, "sources": ["technical.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "technical.vue", "sourceRoot": "src/views/expertReview", "sourcesContent": ["<template>\r\n  <div>\r\n<!--    <BidHeadthree></BidHeadthree>-->\r\n\t  <div class=\"title\">专家评审系统</div>\r\n\r\n    <div class=\"info\">\r\n      <div class=\"content\">\r\n        <one v-if=\"node == 'one'\" @send=\"handleData\"></one>\r\n        <two v-if=\"node == 'two'\" @send=\"handleData\" :isLeader=\"isLeader\" :finish=\"finish\"></two>\r\n        <three v-if=\"node == 'three'\" @send=\"handleData\" :finish=\"finish\"></three>\r\n      </div>\r\n    </div>\r\n    <Foot></Foot>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport one from \"./technical/one\";\r\nimport two from \"./technical/two\";\r\nimport three from \"./technical/three\";\r\n\r\nimport { getProject } from \"@/api/tender/project\";\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { getEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\nimport expertReviewWebSocket from \"@/mixins/expertReviewWebSocket\";\r\n\r\nexport default {\r\n  components: { one, two, three },\r\n  mixins: [expertReviewWebSocket],\r\n  name: \"qualification\",\r\n  data() {\r\n    return {\r\n      project: {},\r\n      projectName: \"测试项目111\",\r\n      node: \"one\",\r\n      finish: false,\r\n      leader: {},\r\n      isLeader: false,\r\n    };\r\n  },\r\n  methods: {\r\n    async init() {\r\n      try {\r\n        // 根据项目id查询项目信息\r\n        const projectResponse = await getProject(this.$route.query.projectId);\r\n        if (projectResponse.code === 200) {\r\n          this.project = projectResponse.data;\r\n        } else {\r\n          this.$message.warning(projectResponse.msg);\r\n        }\r\n\r\n        // 获取专家信息\r\n        const expertResponse = await expertInfoById({\r\n          projectId: this.$route.query.projectId,\r\n        });\r\n        if (expertResponse.code === 200) {\r\n          this.leader = expertResponse.data.find(\r\n            (item) => item.expertLeader === 1\r\n          );\r\n          console.log(\"this.leader\", this.leader);\r\n\r\n          if (this.leader && this.leader.zjhm === this.$route.query.zjhm) {\r\n            this.isLeader = true;\r\n          }\r\n        } else {\r\n          this.$message.warning(expertResponse.msg);\r\n        }\r\n\r\n        // 设置 finish 和 node 的逻辑\r\n        this.finish = this.$route.query.finish === \"true\";\r\n        console.log(\"this.finish\", this.finish, \"this.isLeader\", this.isLeader);\r\n\r\n\t\t\t\t// 判断当前环境\r\n\t      if (process.env.NODE_ENV === \"development\") {\r\n\t\t      this.node = \"one\";\r\n\t\t      return\r\n\t      }\r\n\t\t\t\t\r\n        // 判断是否满足条件\r\n        if (this.finish && this.isLeader) {\r\n          this.node = \"three\";\r\n        } else if (this.finish && !this.isLeader) {\r\n          this.node = \"two\";\r\n        } else {\r\n          this.getEvalExpertStatus();\r\n        }\r\n\r\n\t\t\t\t// setTimeout(()=>{\r\n\t\t\t\t// \tthis.init()\r\n\t\t\t\t// },3000)\r\n      } catch (error) {\r\n        console.error(\"Error during API calls:\", error);\r\n        this.$message.error(\"An error occurred while fetching data.\");\r\n      }\r\n    },\r\n    // 查询专家评审节点信息\r\n    getEvalExpertStatus() {\r\n      // 查询专家评审节点信息\r\n      getEvalExpertScoreInfo({\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        ).projectEvaluationId,\r\n        expertResultId: JSON.parse(localStorage.getItem(\"expertResultId\")),\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        ).scoringMethodItemId,\r\n      }).then((expertStatusResponse) => {\r\n        if (expertStatusResponse.code == 200) {\r\n          localStorage.setItem(\r\n            \"evalExpertScoreInfo\",\r\n            JSON.stringify(expertStatusResponse.data)\r\n          );\r\n          if (expertStatusResponse.data.evalState == 0) {\r\n            this.node = \"one\";\r\n          } else if (expertStatusResponse.data.evalState == 1) {\r\n            this.node = \"two\";\r\n          } else if (expertStatusResponse.data.evalState == 2) {\r\n            if(this.isLeader ){\r\n              this.node = \"three\";\r\n            }else{\r\n              this.node = \"two\";\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    handleData(data) {\r\n      this.node = data;\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info {\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.content {\r\n  background-color: #fff;\r\n  width: 90%;\r\n  min-height: 71vh;\r\n  margin: 20px 0;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.little-title {\r\n  color: rgba(80, 80, 80, 1);\r\n  font-size: 14px;\r\n}\r\n.item-button {\r\n  border: #333 1px solid;\r\n  width: 155px;\r\n  height: 48px;\r\n  margin: 20px 28px;\r\n  background-color: rgba(151, 253, 246, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  &:hover {\r\n    color: rgba(0, 0, 0, 1);\r\n  }\r\n}\r\n.item-button-little {\r\n  border: #333 1px solid;\r\n  width: 124px;\r\n  height: 32px;\r\n  background-color: rgba(151, 253, 246, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  &:hover {\r\n    color: rgba(0, 0, 0, 1);\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title{\r\n\tbackground-color: #c8c9cc;\r\n\tpadding: 10px 5%;\r\n}\r\n</style>\r\n"]}]}