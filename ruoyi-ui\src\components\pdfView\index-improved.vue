<!--
  PDF文件查看器组件 - 增强版
  功能：
  1. 支持PDF文件的在线预览
  2. 提供页面导航功能（上一页、下一页、首页）
  3. 支持PDF页面放大查看
  4. 自动计算当前页码
  5. 虚拟滚动和懒加载，防止大文件浏览器卡死
-->
<template>
	<div>
		<!-- PDF文件加载进度条 -->
		<div v-if="isLoading" class="pdf-loading-container">
			<div class="loading-content">
				<div class="loading-text">正在加载PDF文件...</div>
				<el-progress
					:percentage="loadingProgress"
					:stroke-width="8"
					:show-text="true"
					:format="formatProgress"
					status="success"
					class="loading-progress">
				</el-progress>
				<div class="loading-detail">{{ loadingDetail }}</div>
			</div>
		</div>
		
		<!-- PDF主容器：固定高度600px，支持滚动查看多页PDF -->
		<div ref="pdfContainer" id="pdf-container" style="width:100%;overflow: auto;height: 600px;" v-show="!isLoading" @scroll="handleScroll">
			<!-- 导航按钮栏：粘性定位，始终显示在顶部 -->
			<div style="position:sticky;top:0;z-index:99;background:#fff;padding:10px 0;border-bottom:1px solid #eee;">
				<!-- 上一页按钮 -->
				<el-button size="mini" @click="skipPage(curPage - 1)">上一页</el-button>
				<!-- 首页按钮 -->
				<el-button size="mini" @click="skipPage(1)">首页</el-button>
				<!-- 下一页按钮 -->
				<el-button size="mini" @click="skipPage(curPage + 1)">下一页</el-button>
				<!-- 放大当前页按钮 -->
				<el-button size="mini" @click="enlarge(curPage)">放大</el-button>
				<!-- 显示当前页码 -->
				<span style="margin-left: 20px; color: #666;">
					第 {{ curPage }}/{{ totalPages }} 页
				</span>
			</div>
			
			<!-- 直接渲染所有页面，取消虚拟滚动 -->
			<div style="padding: 10px;">
				<div
					v-for="pageNum in totalPages"
					:key="pageNum"
					class="pdf-page"
					style="margin-bottom: 20px; text-align: center;">
					<canvas
						:ref="`canvas-${pageNum}`"
						:style="{ maxWidth: '100%', height: 'auto', border: '1px solid #ddd', borderRadius: '4px' }"
						:data-page="pageNum">
					</canvas>
				</div>
			</div>
		</div>
		
		<!-- PDF放大查看模态框 -->
		<div v-show="display_enlarge" class="canvas-enlarge">
			<!-- 关闭按钮：位于右上角 -->
			<div style="position: absolute; top: 20px; right: 20px; z-index: 1000;">
				<el-button type="danger" icon="el-icon-close" circle @click="close()">
				</el-button>
			</div>
			
			<!-- 放大后的PDF渲染容器：宽度70%，高度56.25rem，支持滚动 -->
			<div id="enlarge" style="width:70%;height:56.25rem;overflow: auto;">
				<!-- 放大模式下的PDF页面渲染 -->
				<template v-if="totalPages > 0">
					<!-- 遍历每一页PDF，为每页创建放大版本的canvas元素 -->
					<div v-for="pageNum in getEnlargeVisiblePages()" :key="`enlarge-${pageNum}`">
						<canvas
							:ref="`enlarge-canvas-${pageNum}`"
							:style="{ width: '100%', marginBottom: '20px' }"
							:data-page="pageNum">
						</canvas>
					</div>
				</template>
			</div>
		</div>
	</div>
</template>

<script>
// 导入PDF.js库，用于PDF文件的解析和渲染
import pdfjsLib from "pdfjs-dist";
import axios from 'axios';
// 获取API基础URL，用于拼接完整的PDF文件URL
const baseURL = process.env.VUE_APP_BASE_API;

export default {
	// 组件名称
	name: 'PdfViewImproved',
	
	// 组件属性定义
	props: {
		// PDF文件URL，父组件传入的PDF文件路径
		pdfurl: {
			type: String,
			default: "",
		},
		// 唯一标识符，用于区分多个PDF查看器实例，确保DOM元素ID不冲突
		uni_key: {
			type: String,
			default: "a",
		},
		// 每页高度（像素），用于虚拟滚动计算
		pageHeight: {
			type: Number,
			default: 800
		},
		// 缓冲区大小（页数）
		bufferSize: {
			type: Number,
			default: 3
		}
	},
	
	// 组件数据
	data() {
		return {
			// 完整的PDF文件URL（包含baseURL）
			url: "",
			// PDF文档对象
			pdfDocument: null,
			// 总页数
			totalPages: 0,
			// 当前页码
			curPage: 1,
			// 是否正在加载PDF
			isLoading: false,
			// 加载进度百分比（0-100）
			loadingProgress: 0,
			// 加载详细信息
			loadingDetail: "",
			// 是否显示放大模式
			display_enlarge: false,
			// 所有页面是否渲染完成
			allPagesRendered: false,
			
			// 页面缓存
			pageCache: new Map(),
			renderedPages: new Set(),
			renderingPages: new Set(),
			
			// 内存管理
			maxCacheSize: 50, // 最大缓存页数
		};
	},
	
	computed: {
		// 移除虚拟滚动相关计算属性
	},
	
	watch: {
		// 监听PDF URL变化
		pdfurl: {
			handler(newVal) {
				if (newVal != null && newVal !== undefined) {
					this.url = newVal;
					console.log("this.url",this.url)
					if (this.url !== "") {
						this.url = baseURL + this.url;
						// 每次切换PDF都重置状态并回到顶部
						this.resetState();
						this.loadPDF();
					}
				}
			},
			immediate: true
		}
		// 移除visiblePages的watch监听器
	},
	
	methods: {
		/**
		 * 处理窗口大小变化
		 */
		handleResize() {
			const container = this.$el.querySelector("#pdf-container");
			if (container) {
				this.containerHeight = container.clientHeight;
			}
		},
		/**
		 * 格式化进度条显示文本
		 */
		formatProgress(percentage) {
			return `${percentage}%`;
		},
		
		/**
		 * 加载PDF文档 - 支持分片加载
		 */
		async loadPDF() {
			try {
				// 立即滚动到顶部，在加载开始前
				this.$nextTick(() => {
					const container = this.$refs.pdfContainer;
					if (container) {
						container.scrollTop = 0;
					}
				});
				
				this.isLoading = true;
				this.loadingProgress = 0;
				this.loadingDetail = "正在初始化PDF加载器...";
				
				// 重置状态
				this.resetState();
				
				// 配置PDF.js - 修复worker路径问题
				this.configurePdfWorker();
				
				// 检测是否使用分片加载
				const useStreaming = await this.shouldUseStreaming();
				console.log("检测是否使用分片加载",useStreaming)
				if (useStreaming) {
					await this.loadPDFWithStreaming();
				} else {
					await this.loadPDFStandard();
				}
				
			} catch (error) {
				console.error("Error loading PDF:", error);
				this.loadingDetail = "PDF加载失败：" + error.message;
				this.isLoading = false;
			}
		},
		
		/**
		 * 配置PDF.js worker
		 */
		configurePdfWorker() {
			try {
				const worker = require('pdfjs-dist/build/pdf.worker.min.js');
				pdfjsLib.GlobalWorkerOptions.workerSrc = worker;
			} catch (e) {
				// 如果require失败，使用CDN
				pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
			}
		},
		
		/**
		 * 判断是否使用分片加载
		 * 直接传入完整文件路径
		 */
		async shouldUseStreaming() {
			try {
				// 从完整URL中提取文件路径部分
				const filePath = this.extractFilePath(this.url);
				const response = await axios.get(`${baseURL}/common/pdf/info`, {
					params: { filePath }
				});
				console.log(response)
				// 文件大于5MB时使用分片加载
				return response.data.fileSize > 5 * 1024 * 1024;
			} catch (error) {
				console.warn("无法获取文件信息，使用标准加载模式", error);
				return false;
			}
		},
		
		/**
		 * 提取文件路径（从完整URL中提取相对路径）
		 */
		extractFilePath(url) {
			// 从完整URL中提取相对路径部分
			const baseURL = process.env.VUE_APP_BASE_API;
			if (url.startsWith(baseURL)) {
				return url.substring(baseURL.length);
			}
			// 如果URL已经是相对路径，直接返回
			return url;
		},
		
		/**
		 * 提取文件名（保留原有方法用于兼容）
		 */
		extractFileName(url) {
			const parts = url.split('/');
			return parts[parts.length - 1];
		},
		
		/**
		 * 分片加载PDF - 真正的HTTP Range分片加载
		 * 使用后端/stream接口，支持断点续传和按需加载
		 */
		async loadPDFWithStreaming() {
			this.loadingProgress = 10;
			this.loadingDetail = "正在初始化分片加载模式...";
			
			const filePath = this.extractFilePath(this.url);
			const streamingUrl = `${baseURL}/common/pdf/stream?filePath=${encodeURIComponent(filePath)}`;
			
			console.log("🚀 使用分片加载模式，URL:", streamingUrl);
			
			// 分片加载专用配置 - 真正的Range分片
			const loadingTask = pdfjsLib.getDocument({
				url: streamingUrl,
				rangeChunkSize: 32 * 1024,    // 32KB 更小分片，网络容错更强
				disableAutoFetch: true,       // 关键：禁止自动获取，只加载需要的部分
				disableStream: false,         // 启用流式传输
				disableRange: false,          // 关键：启用Range请求，支持断点续传
				isEvalSupported: false,       // 安全考虑
				useWorkerFetch: true,         // 使用worker处理网络请求
				maxImageSize: 10 * 1024 * 1024, // 限制图片大小，防止内存溢出
				cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.16.105/cmaps/', // 字体映射
				cMapPacked: true,
				httpHeaders: {
					'Accept': 'application/pdf',
					'Cache-Control': 'no-cache',
					'Pragma': 'no-cache',
				},
				onProgress: (progressData) => {
					// 分片加载的进度更精确，按块显示
					const percent = Math.round((progressData.loaded / progressData.total) * 100);
					this.handleProgress(progressData);
					if (progressData.loaded < progressData.total) {
						this.loadingDetail = `📦 分片下载中... ${percent}% (${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB)`;
					} else {
						this.loadingDetail = `✅ 分片下载完成，正在解析...`;
					}
				}
			});
			
			this.pdfDocument = await loadingTask.promise;
			this.totalPages = this.pdfDocument.numPages;
			
			this.loadingProgress = 100;
			this.loadingDetail = `🎉 PDF分片加载完成！共${this.totalPages}页，内存占用优化`;
			
			setTimeout(() => {
				this.isLoading = false;
				// 分片模式下使用按需加载策略
				this.startStreamingOptimizedRendering();
			}, 500);
		},
		
		/**
		 * 标准加载模式 - 传统的完整文件下载
		 * 直接下载整个PDF文件到内存，适合小文件
		 */
		async loadPDFStandard() {
			this.loadingProgress = 10;
			this.loadingDetail = "正在标准加载模式...";
			
			console.log("使用标准加载模式，URL:", this.url);
			
			// 标准加载专用配置 - 一次性下载整个文件
			const loadingTask = pdfjsLib.getDocument({
				url: this.url,
				rangeChunkSize: 65536 * 16, // 256KB 大块，减少请求次数
				disableAutoFetch: false,    // 允许自动获取
				disableStream: true,       // 禁用流式，一次性加载
				disableRange: true,        // 禁用Range请求，强制完整下载
				disableWorker: false,      // 启用worker处理
				isEvalSupported: false,
				useWorkerFetch: false,
				httpHeaders: {
					'Accept': 'application/pdf',
				},
				onProgress: (progressData) => {
					// 标准加载显示整体进度
					this.handleProgress(progressData);
					this.loadingDetail = `完整下载中... ${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB`;
				}
			});
			
			this.pdfDocument = await loadingTask.promise;
			this.totalPages = this.pdfDocument.numPages;
			
			this.loadingProgress = 100;
			this.loadingDetail = `PDF标准加载完成！共${this.totalPages}页`;
			
			setTimeout(() => {
				this.isLoading = false;
				// 标准模式下使用渐进式渲染
				this.startOptimizedRendering();
			}, 500);
		},
		
		/**
		 * 处理加载进度
		 */
		handleProgress(progressData) {
			if (progressData.total > 0) {
				const progress = Math.round((progressData.loaded / progressData.total) * 80) + 10;
				this.loadingProgress = Math.min(progress, 99);
				this.loadingDetail = `正在下载... ${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB`;
			}
		},
		
		/**
		 * 开始优化渲染（标准模式）
		 */
		async startOptimizedRendering() {
			if (!this.pdfDocument || this.totalPages === 0) return;
			
			console.log(`开始优化渲染 ${this.totalPages} 页PDF...`);
			
			// 分批渲染，避免阻塞UI
			const initialPages = Math.min(5, this.totalPages);
			for (let i = 1; i <= initialPages; i++) {
				await this.renderPage(i);
				await this.sleep(10);
			}
			
			// 延迟加载剩余页面
			this.scheduleLazyRendering();
		},
		
		/**
		 * 分片模式下的优化渲染（按需加载策略）
		 */
		async startStreamingOptimizedRendering() {
			if (!this.pdfDocument || this.totalPages === 0) return;
			
			console.log(`开始分片模式优化渲染 ${this.totalPages} 页PDF...`);
			
			// 分片模式下使用更激进的按需加载策略
			const initialPages = Math.min(3, this.totalPages);
			for (let i = 1; i <= initialPages; i++) {
				await this.renderPage(i);
				await this.sleep(5);
			}
			
			// 分片模式下使用延迟加载策略，减少初始内存占用
			this.scheduleStreamingLazyRendering();
		},
		
		/**
		 * 延迟加载剩余页面（标准模式）
		 */
		scheduleLazyRendering() {
			const remainingPages = [];
			for (let i = 6; i <= this.totalPages; i++) {
				remainingPages.push(i);
			}
			
			const renderNextPage = async () => {
				if (remainingPages.length === 0) return;
				
				const pageNum = remainingPages.shift();
				if (!this.renderedPages.has(pageNum)) {
					await this.renderPage(pageNum);
					await this.sleep(20);
				}
				
				// 继续渲染下一页
				setTimeout(renderNextPage, 100);
			};
			
			// 延迟开始渲染剩余页面
			setTimeout(renderNextPage, 1000);
		},
		
		/**
		 * 分片模式下的延迟加载策略（更激进的按需加载）
		 */
		scheduleStreamingLazyRendering() {
			const remainingPages = [];
			for (let i = 4; i <= this.totalPages; i++) {
				remainingPages.push(i);
			}
			
			const renderNextPage = async () => {
				if (remainingPages.length === 0) {
					this.checkAllPagesRendered();
					return;
				}
				
				// 分片模式下更智能的加载策略：优先加载用户附近页面
				const nearbyPages = this.getNearbyUnrenderedPages();
				if (nearbyPages.length > 0) {
					const pageNum = nearbyPages[0];
					if (!this.renderedPages.has(pageNum) && !this.renderingPages.has(pageNum)) {
						await this.renderPage(pageNum);
						await this.sleep(50);
					}
				} else if (remainingPages.length > 0) {
					// 回退到顺序加载
					const pageNum = remainingPages.shift();
					if (!this.renderedPages.has(pageNum) && !this.renderingPages.has(pageNum)) {
						await this.renderPage(pageNum);
						await this.sleep(30);
					}
				}
				
				// 继续渲染下一页，分片模式下间隔更短
				setTimeout(renderNextPage, 200);
			};
			
			// 延迟开始渲染剩余页面，分片模式下延迟更短
			setTimeout(renderNextPage, 300);
		},
		
		/**
		 * 获取当前页面附近的未渲染页面
		 */
		getNearbyUnrenderedPages() {
			const nearbyPages = [];
			const currentPage = this.curPage;
			const range = 2; // 加载当前页前后2页
			
			for (let i = Math.max(1, currentPage - range); i <= Math.min(this.totalPages, currentPage + range); i++) {
				if (!this.renderedPages.has(i) && !this.renderingPages.has(i)) {
					nearbyPages.push(i);
				}
			}
			
			return nearbyPages;
		},
		
		/**
		 * 重置组件状态
		 */
		resetState() {
			this.totalPages = 0;
			this.curPage = 1;
			this.allPagesRendered = false; // 重置渲染完成状态
			this.pageCache.clear();
			this.renderedPages.clear();
			this.renderingPages.clear();
			this.loadingDetail = ""; // 清空加载详情
			
			// 通知父组件渲染状态变化
			this.$emit('render-status-change', false);
			
			// 清理定时器
			if (this._retryTimer) {
				clearTimeout(this._retryTimer);
			}
			
			// 关键：在重置状态时立即滚动到顶部
			this.$nextTick(() => {
				const container = this.$refs.pdfContainer;
				if (container) {
					container.scrollTop = 0;
					container.scrollTo(0, 0);
				}
			});
		},
		
		
		/**
		 * 处理滚动事件
		 */
		handleScroll(event) {
			const container = event.target;
			const canvases = container.querySelectorAll('canvas[data-page]');
			
			for (let i = 0; i < canvases.length; i++) {
				const canvas = canvases[i];
				const rect = canvas.getBoundingClientRect();
				const containerRect = container.getBoundingClientRect();
				
				// 如果canvas顶部在容器视口内
				if (rect.top >= containerRect.top && rect.top <= containerRect.bottom) {
					this.curPage = parseInt(canvas.getAttribute('data-page'));
					break;
				}
			}
		},
		
		
		
		/**
		 * 渲染所有页面（取消虚拟滚动，解决显示问题）
		 */
		async renderAllPages() {
			if (!this.pdfDocument || this.totalPages === 0) return;
			
			console.log(`开始渲染 ${this.totalPages} 页PDF...`);
			
			// 清空之前的渲染状态
			this.renderedPages.clear();
			this.renderingPages.clear();
			this.allPagesRendered = false;
			
			// 通知父组件开始渲染
			this.$emit('render-status-change', false);
			
			// 创建所有页面的占位符
			this.renderedPages = new Set();
			
			// 批量渲染页面，每批2个页面避免阻塞
			const batchSize = 2;
			for (let i = 1; i <= this.totalPages; i += batchSize) {
				const end = Math.min(i + batchSize - 1, this.totalPages);
				const promises = [];
				
				for (let pageNum = i; pageNum <= end; pageNum++) {
					promises.push(this.renderPage(pageNum));
				}
				
				await Promise.all(promises);
				
				// 给用户界面更新机会
				await this.sleep(50);
			}
			
			// 检查是否所有页面都已渲染完成
			this.checkAllPagesRendered();
			
			console.log('所有页面渲染完成');
		},
		
		/**
		 * 渲染单个页面（简化版）
		 */
		async renderPage(pageNum, isEnlarge = false) {
			if (!this.pdfDocument || pageNum < 1 || pageNum > this.totalPages) {
				return;
			}
			
			if (this.renderingPages.has(pageNum)) return;
			
			this.renderingPages.add(pageNum);
			
			try {
				const cacheKey = `${pageNum}-${isEnlarge ? 'enlarge' : 'normal'}`;
				
				// 检查缓存
				if (this.pageCache.has(cacheKey)) {
					this.renderCanvas(pageNum, this.pageCache.get(cacheKey), isEnlarge);
					return;
				}
				
				// 获取页面
				const page = await this.pdfDocument.getPage(pageNum);
				const scale = isEnlarge ? 2 : 1.2;
				const viewport = page.getViewport({ scale });
				
				// 创建canvas进行渲染
				const canvas = document.createElement('canvas');
				const context = canvas.getContext('2d');
				
				canvas.height = viewport.height;
				canvas.width = viewport.width;
				
				await page.render({
					canvasContext: context,
					viewport: viewport
				}).promise;
				
				// 缓存渲染结果
				this.pageCache.set(cacheKey, canvas);
				
				// 渲染到实际canvas
				this.renderCanvas(pageNum, canvas, isEnlarge);
				
			} catch (error) {
				console.error(`渲染第${pageNum}页失败:`, error);
			} finally {
				this.renderingPages.delete(pageNum);
				this.renderedPages.add(pageNum);
			}
		},
		
		/**
		 * 渲染到实际canvas
		 */
		renderCanvas(pageNum, sourceCanvas, isEnlarge = false) {
			this.$nextTick(() => {
				const refName = isEnlarge ? `enlarge-canvas-${pageNum}` : `canvas-${pageNum}`;
				const canvases = this.$refs[refName];
				
				// 确保canvas存在
				if (!canvases || canvases.length === 0) {
					console.warn(`Canvas for page ${pageNum} not found, retrying...`);
					setTimeout(() => this.renderCanvas(pageNum, sourceCanvas, isEnlarge), 100);
					return;
				}
				
				const canvas = canvases[0];
				if (!canvas) {
					console.warn(`Canvas element for page ${pageNum} is null`);
					return;
				}
				
				const context = canvas.getContext('2d');
				if (!context) {
					console.warn(`Canvas context for page ${pageNum} is null`);
					return;
				}
				
				canvas.height = sourceCanvas.height;
				canvas.width = sourceCanvas.width;
				
				context.clearRect(0, 0, canvas.width, canvas.height);
				context.drawImage(sourceCanvas, 0, 0);
				
				// 确保页面标记为已渲染
				this.renderedPages.add(pageNum);
				
				// 检查是否所有页面都已渲染完成
				this.checkAllPagesRendered();
			});
		},
		
		/**
		 * 检查所有页面是否渲染完成
		 */
		checkAllPagesRendered() {
			if (this.totalPages > 0 && this.renderedPages.size >= this.totalPages) {
				this.allPagesRendered = true;
				console.log('所有PDF页面渲染完成，启用点击事件');
				// 通知父组件所有页面渲染完成
				this.$emit('render-status-change', true);
			}
		},
		
		/**
		 * 管理缓存大小
		 */
		manageCacheSize() {
			if (this.pageCache.size > this.maxCacheSize) {
				const keys = Array.from(this.pageCache.keys());
				const toRemove = keys.slice(0, keys.length - this.maxCacheSize);
				
				toRemove.forEach(key => {
					this.pageCache.delete(key);
				});
			}
		},
		
		/**
		 * 跳转到指定页面
		 */
		async skipPage(pageNum) {
			if (pageNum < 1) pageNum = 1;
			if (pageNum > this.totalPages) pageNum = this.totalPages;
			
			this.curPage = pageNum;
			
			// 滚动到指定页面 - 使用精确的DOM查找
			const container = document.getElementById('pdf-container');
			if (!container) {
				console.error('PDF容器未找到');
				return;
			}
			
			// 使用ref查找canvas元素
			const refName = `canvas-${pageNum}`;
			const targetCanvas = this.$refs[refName];
			
			if (targetCanvas && targetCanvas[0]) {
				// 直接滚动到目标canvas
				targetCanvas[0].scrollIntoView({
					behavior: 'smooth',
					block: 'start',
					inline: 'nearest'
				});
			} else {
				// 使用querySelector作为备选方案
				const canvasSelector = `canvas[data-page="${pageNum}"]`;
				const fallbackCanvas = container.querySelector(canvasSelector);
				
				if (fallbackCanvas) {
					fallbackCanvas.scrollIntoView({
						behavior: 'smooth',
						block: 'start',
						inline: 'nearest'
					});
				} else {
					// 如果找不到canvas，直接滚动到对应位置
					const pageHeight = 800; // 估计的页面高度
					const targetTop = (pageNum - 1) * pageHeight;
					container.scrollTo({
						top: targetTop,
						behavior: 'smooth'
					});
				}
			}
		},
		
		/**
		 * 放大显示
		 * @param {Number} pageNum - 要放大的页码
		 */
		async enlarge(pageNum) {
			// 确保当前页码被更新为要放大的页码
			this.curPage = pageNum;
			
			this.display_enlarge = true;
			document.body.style.overflow = "hidden";
			document.documentElement.style.overflow = "hidden";
			
			// 预渲染放大版本（只渲染当前页）
			await this.renderPage(pageNum, true);
		},
		
		/**
		 * 获取放大模式可见页面
		 * 只返回当前页码，实现只放大当前预览的页面
		 */
		getEnlargeVisiblePages() {
			if (!this.totalPages) return [];
			
			// 只返回当前页码
			return [this.curPage];
		},
		
		/**
		 * 关闭放大模式
		 */
		close() {
			this.display_enlarge = false;
			document.body.style.overflow = "auto";
			document.documentElement.style.overflow = "auto";
		},
		
		/**
		 * 工具方法：延时
		 */
		sleep(ms) {
			return new Promise(resolve => setTimeout(resolve, ms));
		}
	},
	
	mounted() {
		// 添加窗口大小变化监听
		window.addEventListener('resize', this.handleResize);
	},
	
	beforeDestroy() {
		// 清理资源
		this.pageCache.clear();
		this.renderedPages.clear();
		this.renderingPages.clear();
		clearTimeout(this._retryTimer);
		window.removeEventListener('resize', this.handleResize);
	},
};
</script>

<style lang="scss" scoped>
.pdf-loading-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
	background-color: rgba(255, 255, 255, 0.9);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}

.loading-content {
	background: white;
	padding: 40px;
	border-radius: 12px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
	text-align: center;
	min-width: 400px;
	max-width: 500px;
}

.loading-text {
	font-size: 18px;
	font-weight: 600;
	color: #333;
	margin-bottom: 20px;
}

.loading-progress {
	margin: 20px 0;
}

.loading-detail {
	font-size: 14px;
	color: #666;
	margin-top: 15px;
	min-height: 20px;
}

.pdf-page {
	position: relative;
	margin-bottom: 20px;
	text-align: center;
}

.canvas-enlarge {
	z-index: 999;
	background-color: rgba(0, 0, 0, 0.8);
	position: fixed;
	width: 100%;
	height: 100vh;
	top: 0;
	left: 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

#enlarge {
	background: white;
	border-radius: 8px;
	padding: 20px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}
</style>