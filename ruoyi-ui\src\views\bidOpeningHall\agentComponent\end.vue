<!-- 供应商开标结束 -->
<template>
  <div class="end" 
  v-loading="loading">
    <div class="end-line-one">
      <div class="closingRemarks">
        <div class="end-headline">
          开标已结束
        </div>
      </div>
    </div>
    <div class="end-line-two">
      <el-button
        :disabled="show"
        class="end-button"
        @click="bidOpeningEnds"
      >开标结束</el-button>
      <el-button
        class="end-button"
        style="background: #F5F5F5;color: #176ADB;"
        @click="printRecordSheet"
      >下载开标记录表</el-button>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { operationRecord, exportBidOpeningRecords } from "@/api/onlineBidOpening/info";
import { formatDateOption } from "@/utils/index";
import { listRecord } from "@/api/operation/record";

export default {
  //import引入的组件需要注入到对象中才能使用
  data() {
    //这里存放数据
    return {
      loading: false,
      queryParams: {
        projectId: null,
      },
      show: true,
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 开标结束
    bidOpeningEnds() {
      this.loading = true;
      // 记录操作
      operationRecord({
        projectId: this.$route.query.projectId,
        operationType: 6,
        operationTime: formatDateOption(new Date()),
        decryptionTime: formatDateOption(this.decryptionDeadline),
      }).then((response) => {
        if (response.code == 200) {
          this.buttonShow();
          this.$emit("sendMessage", "end");
          this.$modal.msgSuccess("开标结束，可下载开标记录表");
          this.show = false;
        } else {
          this.$modal.msgwarning(response.msg);
        }
        this.loading = false;
      });
    },
    // 打印开标记录表
    printRecordSheet() {
      this.queryParams.projectId = this.$route.query.projectId;
      exportBidOpeningRecords(this.$route.query.projectId).then((result) => {
          console.info(result)
          if(result.code==200){
            let downloads = document.createElement("a");
            downloads.href = "/prod-api"+result.data.attachments[0].filePath;
            let noticeVersion = "";
            downloads.download = result.data.projectName+'-开标记录表.'+result.data.attachments[0].fileSuffix;
            document.body.appendChild(downloads);
            downloads.click();
            document.body.removeChild(downloads);
            window.URL.revokeObjectURL(href);
          }
        })

      // this.download(
      //   "bidding/info/exportBidOpeningRecords",
      //   {
      //     ...this.queryParams,
      //   },
      //   `开标记录表_${new Date().getTime()}.pdf`
      // );
    },
    // 按钮是否显示
    buttonShow() {
      listRecord({
        projectId: this.$route.query.projectId,
        operationType: 6,
      }).then((response) => {
        if (response.code == 200) {
          
          if (response.rows.length == 1) {
            // if (this.$store.getters.agentBidOpenStatus == 5) {
              this.show = true;
            // }
          }else{
            this.show = false;
          }
        }
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.buttonShow();
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.end {
  .end-line-one {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;

    color: #176adb;
    letter-spacing: 0;
    text-align: center;
    .closingRemarks {
      .end-headline {
        font-weight: 700;
        font-size: 35px;
        margin-bottom: 15px;
      }
    }
  }
  .end-line-two {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 0 150px;
    .end-button {
      width: 164px;
      height: 45px;
      background: #176adb;

      color: #fff;
      font-weight: 700;
      font-size: 18px;
    }
  }
}
</style>
