<template>
	<!-- 开标室主页面 -->
	<div>
		<!-- 头部组件，负责流程状态展示和切换 -->
		<BidHeadtwo ref="head" @updateStatus="handleStatus"></BidHeadtwo>
		<div class="bidOpeningHall">
			<!-- 主体区域，包含流程节点内容 -->
			<el-card id="main" class="box-card">
				<!-- 平台当前时间与聊天室连接状态 -->
				<div style="height: 10px;">
					<div style="padding: 5px 0 0 20px; float: left;">平台当前时间：<span >{{ currentTime }}</span></div>
					<div style="padding: 5px 20px 0 0; float: right;">
						连接状态：<span :style="`color:${isLink ? 'green' : 'red'}`">{{
							isLink ? "已连接" : "已断连，请刷新重连"
						}}</span>
					</div></div>
				<!-- 根据流程节点动态渲染不同子组件 -->
				<ready ref="ready" v-if="node == 'ready' && projectInfo" :projectInfo="projectInfo" @sendMessage="operateSend"></ready>
				<publicity ref="publicity" v-if="node == 'publicity'" :projectInfo="projectInfo" @sendMessage="operateSend"></publicity>
				<decryption ref="decryption" v-if="node == 'decryption' && projectInfo" :projectInfo="projectInfo" :userInfo="userInfo" @sendMessage="operateSend"></decryption>
				<bidAnnouncement ref="bidAnnouncement" v-if="node == 'bidAnnouncement'" @sendMessage="operateSend"></bidAnnouncement>
				<end ref="end" v-if="node == 'end'" @sendMessage="operateSend"></end>
			</el-card>
			<!-- 聊天室侧边栏 -->
			<el-card class="box-card" style="width: 15%;">
				<div class="im">
					<div class="im-title">{{ userInfo.nickName }}</div>
					<!-- 聊天内容区域 -->
					<div ref="messagesContainer" class="im-content" :style="{height: syncedHeight }">
						<div v-for="(itemc, indexc) in recordContent" :key="indexc">
							<div class="sysMessage" v-if="itemc.type == 0">
								<!-- 系统消息（可扩展） -->
							</div>
							<div v-else>
								<!-- 他人消息 -->
								<div class="word" v-if="itemc.sendId !== userInfo.entId">
									<div class="info">
										<div class="message_time">
											{{anonymous? "*******":itemc.sendName }}
										</div>
										<div class="info-content">{{ itemc.content }}</div>
										<div class="message_time">
											{{ formatBidOpeningTimeTwo(itemc.sendTime) }}
										</div>
									</div>
								</div>
								<!-- 自己消息 -->
								<div class="word-my" v-else>
									<div class="info">
										<div class="info-content">{{ itemc.content }}</div>
										<div class="Sender_time">
											{{ formatBidOpeningTimeTwo(itemc.sendTime) }}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- 聊天输入与发送 -->
					<div class="im-operation">
						<div style="margin-right:5px">
							<el-input v-model="message" placeholder="输入内容"></el-input>
						</div>
						<el-button style="height: 36px;background: #176ADB;color:#fff" @click="send">发送</el-button>
					</div>
				</div>
			</el-card>
		</div>
		<!-- 页脚 -->
		<Foot></Foot>
	</div>
</template>

<script>
// 引入各流程节点组件
import Ready from "./agentComponent/ready.vue";
import publicity from "./agentComponent/publicity.vue";
import decryption from "./agentComponent/decryption.vue";
import bidAnnouncement from "./agentComponent/bidAnnouncement.vue";
import end from "./agentComponent/end.vue";

// 工具方法与API接口
import {
	formatDateOption,
	getTodayStartWithDate,
	getTodayEndWithDate,
} from "@/utils/index";
import { bidInfo, chatHistory } from "@/api/onlineBidOpening/info";
import { getUserProfile } from "@/api/system/user";
import { getSystemTime } from "@/api/bid/opening";

export default {
	components: { Ready, publicity, decryption, bidAnnouncement, end },
	data() {
		return {
			// 当前流程节点
			node: "ready",
			// 当前用户信息
			userInfo: {},
			// 项目信息
			projectInfo: null,
			// 接口基础地址
			baseUrl: process.env.VUE_APP_BASE_API,
			// 聊天输入内容
			message: "",
			// 文本内容（用于日志等）
			text_content: "",
			// websocket 实例
			ws: null,
			// 是否匿名（根据流程节点控制）
			anonymous: true,
			// 聊天记录内容
			recordContent: [
				// 示例数据，实际会被接口数据覆盖
				{
					sendId: 1,
					content: "Nice to meet you.",
					sendTime: "2024-7-17 11:00:00",
					Sender: "张三",
				},
				{
					sendId: 2,
					content: "Nice to meet you.too",
					sendTime: "2024-7-17 11:01:00",
					Sender: "李四",
				},
				{
					sendId: 2,
					content: "How are you? ",
					sendTime: "2024-7-17 11:02:00",
					Sender: "李四",
				},
				{
					sendId: 1,
					content: "I'am fine,Thank you.",
					sendTime: "2024-7-17 11:03:00",
					Sender: "张三",
				},
			],
			// websocket连接状态
			isLink: false,
			// 聊天内容区高度（与主卡片同步）
			syncedHeight: '450px', // 初始高度
			// 平台当前时间
			currentTime:null,
			// 时间更新定时器
			timeInterval: null
		};
	},
	watch: {
		// 监听流程节点变化，动态调整聊天区高度
		node: {
			handler() {
				setTimeout(() => {
					var element = document.getElementById('main');
					console.log('element.clientHeight', element.offsetHeight);
					this.syncedHeight = element.offsetHeight - 120 + 'px'
				}, 10);

			},
			deep: true
		}
	},
	methods: {
		// 初始化，获取项目信息和用户信息
		init() {
			// 获取开标项目信息
			const promise1 = bidInfo({
				bidOpeningTime: getTodayStartWithDate(),
				bidOpeningEndTime: getTodayEndWithDate(),
				projectId: this.$route.query.projectId,
			}).then((response) => {
				if (response.code == 200) {
					this.projectInfo = response.data;
				} else {
					this.$modal.msgwarning(response.msg);
				}
			});
			// 获取用户信息
			const promise2 = getUserProfile().then((response) => {
				this.userInfo = response.data;
			});

			// 两个接口都完成后，建立websocket连接
			Promise.all([promise1, promise2]).then((result) => {
				this.join();
			});
		},

		// 处理流程节点切换
		handleStatus(data) {
			// 根据流程状态判断是否匿名
			this.anonymous = this.$store.getters.agentBidOpenStatus >= 2 ? false : true;
			switch (data) {
				case "签到":
					this.node = "signIn";
					break;
				case "开标准备":
					this.node = "ready";
					break;
				case "投标人公示":
					this.node = "publicity";
					break;
				case "标书解密":
					this.node = "decryption";
					break;
				case "唱标":
					this.node = "bidAnnouncement";
					break;
				case "开标结束":
					this.node = "end";
					break;
			}
		},
		// 节点更新通知，刷新头部状态
		updateStatus() {
			this.$refs.head.getBidStatus();
		},
		// 格式化开标时间显示 年-月-日
		formatBidOpeningTime(time) {
			return formatDateOption(time, "date");
		},
		// 格式化开标时间显示 时-分-秒
		formatBidOpeningTimeTwo(time) {
			return formatDateOption(time, "time");
		},

		// 建立websocket连接，处理心跳、消息、断线重连等
		join() {
			let socketUrl = this.baseUrl.replace("http", "ws");
			console.log("socketUrl", socketUrl);
			// 拼接websocket地址
			this.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;
			const wsurl = this.url;
			this.ws = new WebSocket(wsurl);
			const self = this;
			// 心跳检测函数，防止连接超时断开
			const ws_heartCheck = {
				timeout: 5000, // 5秒
				timeoutObj: null,
				serverTimeoutObj: null,
				start: function () {
					this.timeoutObj = setTimeout(() => {
						// 发送心跳包
						self.ws.send("ping");
						this.serverTimeoutObj = setTimeout(() => {
							self.ws.close(); // 超时未响应则断开
						}, this.timeout);
					}, this.timeout);
				},
				reset: function () {
					clearTimeout(this.timeoutObj); // 重置心跳
					clearTimeout(this.serverTimeoutObj);
					this.start();
				},
				stop: function () {
					clearTimeout(this.timeoutObj);
					clearTimeout(this.serverTimeoutObj);
				}
			};
			// 连接打开
			this.ws.onopen = function (event) {
				ws_heartCheck.start();
				self.text_content = self.text_content + "已经打开开标室连接!" + "\n";
				self.isLink = true;
				console.log(self.text_content);
			};
			// 收到消息
			this.ws.onmessage = function (event) {
				console.log(event.data);
				if (event.data == "ping") {
					ws_heartCheck.reset(); // 心跳包
				} else if (event.data == "连接成功") {
				} else if (event.data == "signIn") {
					self.$refs.publicity.initdataList();
				} else if (event.data == "supDecrytion") {
					self.$refs.decryption.initdataList();
				} else if (
					event.data == "ready" ||
					event.data == "bidPublicity" ||
					event.data == "decryption" ||
					event.data == "nextStep" ||
					event.data == "bidAnnouncement" ||
					event.data == "end" ||
					event.data == "flowLabel"
				) {
					self.updateStatus();
				} else {
					self.initChat();
				}
			};
			// 连接关闭，自动重连
			this.ws.onclose = function (event) {
				self.text_content = self.text_content + "已经关闭开标室连接!" + "\n";
				self.isLink = false;
				clearTimeout(ws_heartCheck.timeoutObj);
				clearTimeout(ws_heartCheck.serverTimeoutObj);
				//断开后自动重连
				ws_heartCheck.stop();
				self.join();
			};
		},
		// 主动断开websocket连接
		exit() {
			if (this.ws) {
				this.ws.close();
				this.ws = null;
			}
		},
		// 发送消息（自己输入）
		send() {
			if (this.ws) {
				this.ws.send(this.message);
				this.message = "";
				this.scrollToBottom();
			} else {
				alert("未连接到开标室服务器");
			}
		},
		// 发送消息（子组件调用）
		operateSend(message) {
			if (this.ws) {
				this.ws.send(message);
			} else {
				alert("未连接到开标室服务器");
			}
		},
		// 初始化聊天记录
		initChat() {
			chatHistory(this.$route.query.projectId).then((response) => {
				if (response.code == 200) {
					this.recordContent = response.data;
				} else {
					this.recordContent = [];
				}
			});
		},
		// 聊天内容滚动到底部
		scrollToBottom() {
			this.$nextTick(() => {
				const container = this.$refs.messagesContainer;
				container.scrollTop = container.scrollHeight;
			});
		},
		// 获取并定时更新时间
		updateTime() {
			// 清理之前的定时器
			if (this.timeInterval) {
				clearInterval(this.timeInterval);
			}

			var _this = this;
			getSystemTime().then((result) => {
				if(result.code==200){
					var ct = new Date(result.data);
					this.timeInterval = setInterval(function(){
						ct.setSeconds(ct.getSeconds() + 1);
						_this.currentTime = formatDateOption(ct, "cdatetime");
						
						_this.updateStatus();
						_this.initChat();

					}, 1000); // 每秒更新时间
				}
			});
		}
	},
	// 生命周期钩子
	created() { },
	mounted() {
		this.init();
		this.initChat();

		this.updateTime();
	},
	updated() {
		this.scrollToBottom();
	},
	beforeDestroy() {
		// 清理定时器
		if (this.timeInterval) {
			clearInterval(this.timeInterval);
		}
		// 断开WebSocket连接
		this.exit();
	},
};
</script>
<style lang="scss" scoped>
// 覆盖el-card body内边距
::v-deep .el-card__body {
	padding: 0;
}
</style>

<style scoped lang="scss">
// 聊天消息激活态
.active {
	background-color: rgba(149, 250, 190, 1);
}
// 开标室主布局
.bidOpeningHall {
	position: relative;
	background-color: #f5f5f5;
	display: flex;
	flex-wrap: nowrap;
	justify-content: center;
	align-content: flex-start;
	align-items: flex-start;
}
// 主体卡片样式
.box-card {
	min-height: 600px;
	width: 50%;
	margin: 15px 5px;
}
// 聊天室整体样式
.im {
	.im-title {
		width: 100%;
		height: 50px;
		background: #176adb;

		font-weight: 500;
		font-size: 16px;
		color: #ffffff;
		letter-spacing: 0;

		line-height: 50px;
		text-align: center;
	}
	.im-content {
		margin: 10px;
		background: #f5f5f5;
		height: 450px;
		overflow-y: auto;
	}
	.im-operation {
		display: flex;
		margin: 0 10px;
		margin-bottom: 10px;
		overflow: auto;
	}
}
// 聊天内容区样式
.im-content {
	// 他人消息气泡
	.word {
		display: flex;
		margin-bottom: 20px;

		img {
			width: 40px;
			height: 40px;
			border-radius: 50%;
		}
		.info {
			width: 47%;
			margin-left: 10px;
			.Sender_time {
				padding-right: 12px;
				padding-top: 5px;
				font-size: 12px;
				color: rgba(51, 51, 51, 0.8);
				margin: 0;
				height: 20px;
			}
			.message_time {
				font-size: 12px;
				color: rgba(51, 51, 51, 0.8);
				margin: 0;
				height: 20px;
				line-height: 20px;
				margin-top: -5px;
				margin-top: 5px;
			}
			.info-content {
				word-break: break-all;
				// max-width: 45%;
				display: inline-block;
				padding: 10px;
				font-size: 14px;
				background: #fff;
				position: relative;
				margin-top: 8px;
				background: #dbdbdb;
				border-radius: 4px;
			}
			//小三角形
			.info-content::before {
				position: absolute;
				left: -8px;
				top: 8px;
				content: "";
				border-right: 10px solid #dbdbdb;
				border-top: 8px solid transparent;
				border-bottom: 8px solid transparent;
			}
		}
	}
	// 自己消息气泡
	.word-my {
		display: flex;
		justify-content: flex-end;
		margin-bottom: 20px;
		img {
			width: 40px;
			height: 40px;
			border-radius: 50%;
		}
		.info {
			width: 90%;
			// margin-left: 10px;
			text-align: right;
			// position: relative;
			display: flex;
			align-items: flex-end;
			flex-wrap: wrap;
			flex-direction: column;
			.info-content {
				word-break: break-all;
				max-width: 45%;
				padding: 10px;
				font-size: 14px;
				// float: right;
				margin-right: 10px;
				position: relative;
				margin-top: 8px;
				background: #a3c3f6;
				text-align: left;
				border-radius: 4px;
			}
			.Sender_time {
				padding-right: 12px;
				padding-top: 5px;
				font-size: 12px;
				color: rgba(51, 51, 51, 0.8);
				margin: 0;
				height: 20px;
			}
			//小三角形
			.info-content::after {
				position: absolute;
				right: -8px;
				top: 8px;
				content: "";
				border-left: 10px solid #a3c3f6;
				border-top: 8px solid transparent;
				border-bottom: 8px solid transparent;
			}
		}
	}
}
</style>
