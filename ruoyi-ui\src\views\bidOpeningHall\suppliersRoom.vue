<template>
  <div>
    <BidHeadone ref="head" @updateStatus="handleStatus"></BidHeadone>
    <div class="bidOpeningHall">
      <el-card id="main" class="box-card">
        <div style="height: 10px;">
        <div style="padding: 5px 0 0 20px; float: left;">平台当前时间：<span >{{ currentTime }}</span></div>
        <div style="padding: 5px 20px 0 0; float: right;">
          连接状态：<span :style="`color:${isLink ? 'green' : 'red'}`">{{
            isLink ? "已连接" : "已断连，请刷新重连"
          }}</span>
        </div></div>
        <Sign ref="signIn" v-if="shouldRenderSign" :projectInfo="projectInfo" :userInfo="userInfo" @sendMessage="operateSend"></Sign>
        <ready ref="ready" v-if="node == 'ready' && projectInfo" :projectInfo="projectInfo"></ready>
        <publicity ref="publicity" v-if="node == 'publicity'"></publicity>
        <decryption ref="decryption" v-if="node == 'decryption' && projectInfo" :projectInfo="projectInfo" :userInfo="userInfo" :deadline="decryptionDeadline" @sendMessage="operateSend"></decryption>
        <bidAnnouncement ref="bidAnnouncement" v-if="node == 'bidAnnouncement'"></bidAnnouncement>
        <end ref="end" v-if="node == 'end'" @sendData="handleData"></end>

      </el-card>
      <el-card class="box-card" style="width: 15%;">
        <div class="im">
          <div class="im-title">{{ userInfo.ent?userInfo.ent.entName :  ''}}</div>
          <div ref="messagesContainer" class="im-content" :style="{height: syncedHeight }">
            <div v-for="(itemc, indexc) in recordContent" :key="indexc">
              <div class="sysMessage" v-if="itemc.type == 0">
              </div>
              <div v-else>
                <div class="word" v-if="itemc.sendId !== userInfo.entId">
                  <div class="info">
                    <div class="message_time">
                      {{anonymous? "*******":itemc.sendName }}
                    </div>
                    <div class="info-content">{{ itemc.content }}</div>
                    <div class="message_time">
                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}
                    </div>
                  </div>
                </div>
                <div class="word-my" v-else>
                  <div class="info">
                    <div class="info-content">{{ itemc.content }}</div>
                    <div class="Sender_time">
                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="im-operation">
            <div style="margin-right:5px">
              <el-input v-model="message" placeholder="输入内容"></el-input>
            </div>
            <el-button style="height: 36px;background: #176ADB;color:#fff" @click="send">发送</el-button>
          </div>
        </div>
      </el-card>
    </div>
    <Foot></Foot>

    <el-dialog :visible.sync="secondQuoteVisible" width="60%">
      <!-- 显示倒计时 -->
      <el-table :data="quoteList" border style="width: 100%;" :cell-style="cellStyle" :header-cell-style="headStyle">
        <el-table-column label="二次报价">
          <el-table-column type="index" label="序号" width="100">
          </el-table-column>
          <el-table-column label="历史报价" prop="quoteAmount" align="center">
          </el-table-column>
          <el-table-column label="报价大写" align="center">
            <template slot-scope="scope">
              {{ formatAmount(scope.row.quoteAmount) }}
            </template>
          </el-table-column>
          <el-table-column label="报价时间" prop="createTime" align="center">
          </el-table-column>
        </el-table-column>
      </el-table>

      <div class="decryption-countdown">
        <el-statistic @finish="finishSecondQuote" format="二次报价结束倒计时：HH小时mm分ss秒" :value="countdown" time-indices>
        </el-statistic>
      </div>

      <el-row :gutter="20">
        <el-form ref="form" :model="form" :rules="rules" label-width="90px">
          <el-col :span="6" :offset="3">
            <el-form-item label="报价金额：">
              <el-input v-model="form.quoteAmount" placeholder="请输入报价金额"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="大写金额：">
              <el-input :disabled="true" :value="returnConvertToChineseCurrency(form.quoteAmount)" placeholder="请输入报价金额大写"></el-input>
            </el-form-item>
          </el-col>
        </el-form>
        <el-col :span="5">
          <div style="display:flex;justify-content: center;align-items: center;">
            <el-button class="quote-button" :disabled="isCountdownEnded"  @click="submitQuote">确 定</el-button>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import Sign from "./supplierComponent/sign.vue";
import Ready from "./supplierComponent/ready.vue";
import publicity from "./supplierComponent/publicity.vue";
import decryption from "./supplierComponent/decryption.vue";
import bidAnnouncement from "./supplierComponent/bidAnnouncement.vue";
import end from "./supplierComponent/end.vue";

import {
  formatDateOption,
  getTodayStartWithDate,
  getTodayEndWithDate,
} from "@/utils/index";
import { bidInfo, chatHistory } from "@/api/onlineBidOpening/info";
import { getUserProfile } from "@/api/system/user";
import { convertToChineseCurrency } from "@/utils/amount";
import { listInfo } from "@/api/evaluation/info";
import { listQuote, addQuote } from "@/api/again/quote";
import { expertInfoById } from "@/api/expert/review";
import { approvalProcess,inquiringBidList } from "@/api/expert/review";

const baseUrl = process.env.VUE_APP_BASE_API;
const socketUrl = baseUrl.replace("http", "ws").replace("/prod-api", "");
export default {
  components: { Sign, Ready, publicity, decryption, bidAnnouncement, end },
  data() {
    return {
      node: "sign",
      userInfo: {},
      projectInfo: null,

      // 二次报价表单
      form: {
        quoteAmount: "",
        projectEvaluationId: "",
        quoteAmountStr: "",
      },
      // 二次报价金额
      rules: {
        quoteAmount: [
          { required: true, message: "请输入金额", trigger: "blur" },
        ],
        quoteAmountStr: [
          { required: true, message: "请输入金额大写", trigger: "blur" },
        ],
      },
      progress: [
        {
          itemName: "资格性评审",
          status: 0,
        },
        {
          itemName: "符合性评审",
          status: 0,
        },
        {
          itemName: "技术标评审",
          status: 0,
        },
        {
          itemName: "商务标评审",
          status: 0,
        },
        {
          itemName: "投标报价打分",
          status: 0,
        },
      ],
      // 二次报价历史报价
      quoteList: [],
      // 显示二次报价弹框
      secondQuoteVisible: false,

      decryptionDeadline: "",

      url: "",
      message: "",
      text_content: "",
      ws: null,

      recordContent: [],
      evaluationInfo: {},
      timer: null, // 存储定时器引用
      isCountdownEnded: false,
      // 专家信息
      expertList: {},
      intervalId: null,
      countdown: "",
      num: 0,
      isLink: false,
      anonymous: true,
      syncedHeight: '450px', // 初始高度
      currentTime:null

    };
  },
  computed: {
    shouldRenderSign() {
      return (
        this.node === "signIn" &&
        this.projectInfo &&
        this.userInfo &&
        Object.keys(this.userInfo).length > 0
      );
    },
  },
  watch: {
    node: {
      handler() {
        setTimeout(() => {
          var element = document.getElementById('main');
          console.log('element.clientHeight', element.offsetHeight);
          this.syncedHeight = element.offsetHeight - 120 + 'px'
        }, 10);

      },
      deep: true
    }
  },
  methods: {
    finishSecondQuote(isOk){
      // this.isCountdownEnded = isOk;
    },
    // 初始化
    init() {
      // 获取开标项目信息
      const promise1 = bidInfo({
        bidOpeningTime: getTodayStartWithDate(),
        bidOpeningEndTime: getTodayEndWithDate(),
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code == 200) {
          this.projectInfo = response.data;
        } else {
          this.$modal.msgwarning(response.msg);
        }
      });
      // 获取用户信息
      const promise2 = getUserProfile().then((response) => {
        this.userInfo = response.data;
        localStorage.setItem("userInfo", JSON.stringify(this.userInfo));
      });
      // 获取项目评审信息
      const promise3 = listInfo({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code === 200) {
          if (response.rows && response.rows.length > 0) {
            this.evaluationInfo = response.rows[response.rows.length - 1];
          } else {
            this.evaluationInfo = {};
            // this.$message.error("未查询到项目评审信息");
          }
        } else {
          this.$message.error(response.msg);
        }
      });
      // 获取专家信息
      const promise4 = expertInfoById({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code == 200) {
          this.expertList = response.data;
          localStorage.setItem("expertList", JSON.stringify(this.expertList));
        } else {
          this.$message.warning(response.msg);
        }
      });

      Promise.all([promise1, promise2, promise3, promise4]).then((result) => {
        this.join();
        if (this.node == "end") {
          this.$refs.end.getExpertReviewProgress();
        }
      });
    },
    // 获取当前开标室流程
    handleStatus(data) {
      this.anonymous = this.$store.getters.supplierBidOpenStatus >= 2 ? false : true;
      switch (data) {
        case "签到":
          this.node = "signIn";
          break;
        case "开标准备":
          this.node = "ready";
          break;
        case "投标人公示":
          this.node = "publicity";
          break;
        case "标书解密":
          this.node = "decryption";
          break;
        case "唱标":
          this.node = "bidAnnouncement";
          break;
        case "开标结束":
          this.node = "end";
          break;
      }
    },
    // 节点更新通知
    updateStatus() {
      this.$refs.head.getBidStatus();
    },

    // 转换大写的报价金额
    returnConvertToChineseCurrency(money) {
      return convertToChineseCurrency(money);
    },
    // 提交报价
    submitQuote() {
      if (this.isCountdownEnded) {
        this.$message.warning("倒计时结束，禁止报价");
        this.secondQuoteVisible = false;
        return;
      }
      var previousPrice = 0;

      if(this.quoteList.length===0){
        previousPrice = this.projectInfo.project.budgetAmount;
      }else{
        previousPrice = this.quoteList[this.quoteList.length-1].quoteAmount;
      }
      if(this.form.quoteAmount>previousPrice){
        this.$confirm("本次报价超过上次报价 "+ previousPrice + " 元，是否继续提交？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 用户确认后执行提交报价
          this.doSubmitQuote();
        }).catch(() => {
          // 用户取消，不执行任何操作
          return;
        });
        return;
      }
      //提交报价
      this.doSubmitQuote();
    },
    doSubmitQuote() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.evaluationInfo.projectEvaluationId == null) {
            listInfo({
              projectId: this.$route.query.projectId,
            }).then((response) => {
              if (response.code === 200) {
                if (response.rows && response.rows.length > 0) {
                  this.evaluationInfo = response.rows[response.rows.length - 1];
                  this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;
                } else {
                  this.$message.error("没有评审信息！");
                  return
                }
              } else {
                this.$message.success(response.msg);
              }
            });
          } else {
            this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;
          }
          addQuote(this.form)
            .then((result) => {
              if (result.code === 200) {
                this.$message.success(result.msg);
                this.getQuoteList();
                this.secondQuoteVisible = false;
              } else {
                this.$message.error(result.msg);
              }
            })
            .catch((err) => { });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
      // 关闭弹窗时，清除定时器
      if (this.timer) {
        clearInterval(this.timer);
      }
    },
    // 转换大写的报价金额
    formatAmount(amount) {
      return convertToChineseCurrency(amount);
    },

    // 格式化开标时间显示
    formatBidOpeningTime(time) {
      return formatDateOption(time, "date");
    },
    // 格式化开标时间显示 时-分-秒
    formatBidOpeningTimeTwo(time) {
      return formatDateOption(time, "time");
    },
    // 获取历史报价
    getQuoteList() {
      const queryParams = {
        pageNum: 1,
        pageSize: 999,
        params: {
          projectId: this.$route.query.projectId,
          entId: this.userInfo.entId,
        },
      };
      listQuote(queryParams)
        .then((result) => {
          //存入数据
          this.quoteList = result.rows;
          console.log(this.quoteList.length + 1 == this.num);
          if (this.quoteList.length + 1 == this.num) {
            this.secondQuoteVisible = true;
            // this.isCountdownEnded = true;
          } else {
            // this.isCountdownEnded = false;
            this.secondQuoteVisible = false;
          }
        })
        .catch((err) => { });
    },
    getExpertReviewProgress() {
      approvalProcess(this.$route.query.projectId, this.expertList[0].resultId).then(
        (response) => {
          if (response.code == 200 && response.data.scoringMethodUinfo.scoringMethodItems) {
            
            const existingItemNames = response.data.scoringMethodUinfo.scoringMethodItems.map(item => item.itemName);
            this.progress = this.progress.filter(item => existingItemNames.includes(item.itemName));

            const evalProjectEvaluationProcess = this.progress.find((item) => {
              return item.itemName == "投标报价打分";
            }).evalProjectEvaluationProcess;
            if (evalProjectEvaluationProcess) {
              let startTime = new Date(
                evalProjectEvaluationProcess.startTime.replace(" ", "T") + "Z"
              );
              // 将 Date 对象加上30秒
              startTime.setMinutes(startTime.getMinutes() + evalProjectEvaluationProcess.minutes);
              var endTime = startTime
                .toISOString()
                .replace("T", " ")
                .replace("Z", "")
                .split(".")[0];
              // 将更新后的时间转换回字符串格式
              this.num = evalProjectEvaluationProcess.num;
              this.handleData({
                startTime: evalProjectEvaluationProcess.startTime,
                endTime: endTime,
                num: this.num,
                isAbandonedBid: 0
              });
            }
          } else {

          }
        }
      );

    },
    // 判断是否打开二次报价弹窗
    handleData({ startTime, endTime, num, isAbandonedBid }) {
      if (this.secondQuoteVisible == true) {
        return;
      } else {
        const now = new Date(); // 当前时间
        const start = new Date(startTime); // 开始时间
        const end = new Date(endTime); // 结束时间
        console.log("isAbandonedBid",isAbandonedBid);
        
        if (now >= start && now <= end && isAbandonedBid==0) {
          this.$nextTick(() => {
            this.countdown = new Date(endTime);
          });
          this.num = num;
          console.log("this.num", this.num);
          this.form = {};
          this.getQuoteList();
        } else {
          this.secondQuoteVisible = false;
        }
      }
    },
    // 连接websocket
    join() {
      // this.url = `${socketUrl}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;
      this.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;
      const wsurl = this.url;
      this.ws = new WebSocket(wsurl);
      const self = this;
      // 心跳检测函数
      const ws_heartCheck = {
        timeout: 5000, // 5秒
        timeoutObj: null,
        serverTimeoutObj: null,
        start: function () {
          this.timeoutObj = setTimeout(() => {
            // 这里发送一个心跳包
            self.ws.send("ping");
            this.serverTimeoutObj = setTimeout(() => {
              self.ws.close(); // 如果超过一定时间还没重置，视为断开连接
            }, this.timeout);
          }, this.timeout);
        },
        reset: function () {
          clearTimeout(this.timeoutObj);
          clearTimeout(this.serverTimeoutObj);
          this.start();
        },
        stop: function () {
          clearTimeout(this.timeoutObj);
          clearTimeout(this.serverTimeoutObj);
        }
      };
      this.ws.onopen = function (event) {
        ws_heartCheck.start();
        self.text_content = self.text_content + "已经打开开标室连接!" + "\n";
        self.isLink = true;

        console.log(self.text_content);
      };
      this.ws.onmessage = function (event) {
        console.log(event.data);
        if(event.data == "ping"){
          // 心跳响应
          ws_heartCheck.reset();
        }else if (event.data == "连接成功") {
          console.log("socketUrl", socketUrl);
        } else if (event.data == "signIn") {
          self.updateStatus();
        } else if (event.data == "bidPublicity") {
          self.updateStatus();
        } else if (event.data.includes("decryption")) {
          self.updateStatus();
        } else if (event.data == "supDecrytion") {
          self.$refs.decryption.initdataList();
        } else if (event.data == "nextStep") {
          // 不做任何操作
        } else if (
          event.data == "bidAnnouncement" ||
          event.data == "flowLabel"
        ) {
          self.updateStatus();
        } else if (event.data == "end") {
          self.updateStatus();
        } else if (event.data == "evalAgainQuote") {
          self.getExpertReviewProgress();
          // //打开二次报价弹窗
          // self.form = {};
          // //加载报价记录
          // self.getQuoteList();
        } else {
          self.initChat();
        }
      };
      this.ws.onclose = function (event) {
        self.text_content = self.text_content + "已经关闭开标室连接!" + "\n";
        self.isLink = false;
        console.log(self.text_content);
        //断开后自动重连
        ws_heartCheck.stop();
        self.join();
      };
    },
    // 断开websocket连接
    exit() {
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
    },
    // 发送消息
    send() {
      if (this.ws) {
        this.ws.send(this.message);
        this.message = "";
        this.scrollToBottom();
      } else {
        alert("未连接到开标室服务器");
      }
    },
    // 发送消息
    operateSend(message) {
      if (this.ws) {
        this.ws.send(message);
      } else {
        alert("未连接到开标室服务器");
      }
    },
    // 初始化聊天记录
    initChat() {
      chatHistory(this.$route.query.projectId).then((response) => {
        this.recordContent = response.data;
      });
    },
    // 处理滚动
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer;
        container.scrollTop = container.scrollHeight;
      });
    },
    // 表格头颜色
    headStyle({ row, column, rowIndex, columnIndex }) {
      console.log(row, column, rowIndex, columnIndex);
      if (rowIndex === 0 && columnIndex === 0) {
        return {
          "text-align": "center",
          background: "#1C57A7",
          color: "#fff",
          "font-size": "16px",
          "font-weight": "700",
        };
      } else {
        return {
          "text-align": "center",
          background: "#176ADB",
          color: "#fff",
          "font-size": "16px",
          "font-weight": "700",
          border: "0px",
        };
      }
    },
    // 表格样式
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (rowIndex % 2 === 0) {
        return {
          "text-align": "center",
          "font-weight": "700",
          color: "#000",
          "font-size": "14px",
          background: "#FFFFFF",
        };
      } else {
        return {
          "text-align": "center",
          "font-weight": "700",
          color: "#000",
          "font-size": "14px",
          background: "#F5F5F5",
        };
      }
    },
    startProgressInterval() {
      this.intervalId = setInterval(() => {
        if (this.node == "end") {
          this.$refs.end.getExpertReviewProgress();
          //在这里增加 供应商消息刷新页面接口

        }
      }, 5000); // 每隔 5 秒调用一次
    },
    stopProgressInterval() {
      clearInterval(this.intervalId);
    },
    updateTime() {
      var ct = new Date();
      var _this = this;

      setInterval(function(){
        ct.setSeconds(ct.getSeconds() + 1);
        _this.currentTime = formatDateOption(ct, "cdatetime");
      }, 1000); // 每秒更新时间
    }
  },
  created() { },
  mounted() {
    this.init();
    this.initChat();
    // 在组件挂载后启动定时器
    this.startProgressInterval();
    this.updateTime();
  },
  updated() {
    this.scrollToBottom();
  },
  beforeDestroy() {
    // 在组件销毁前清除定时器
    this.stopProgressInterval();
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-card__body {
  padding: 0;
}
</style>

<style scoped lang="scss">
.bidOpeningHall {
  position: relative;
  background-color: #f5f5f5;
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: flex-start;
  align-items: flex-start;
}
.box-card {
  min-height: 600px;
  width: 50%;
  margin: 15px 5px;
}
.im {
  .im-title {
    width: 100%;
    height: 50px;
    background: #176adb;

    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    letter-spacing: 0;

    line-height: 50px;
    text-align: center;
  }
  .im-content {
    margin: 10px;
    background: #f5f5f5;
    // height: 450px;
    overflow-y: auto;
  }
  .im-operation {
    display: flex;
    margin: 0 10px;
    margin-bottom: 10px;
    overflow: auto;
  }
}
.im-content {
  .word {
    display: flex;
    margin-bottom: 30px;

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    .info {
      width: 47%;
      margin-left: 10px;
      .Sender_time {
        padding-right: 12px;
        padding-top: 5px;
        font-size: 12px;
        color: rgba(51, 51, 51, 0.8);
        margin: 0;
        height: 20px;
      }
      .message_time {
        font-size: 12px;
        color: rgba(51, 51, 51, 0.8);
        margin: 0;
        height: 20px;
        line-height: 20px;
        margin-top: -5px;
        margin-top: 5px;
      }
      .info-content {
        word-break: break-all;
        // max-width: 45%;
        display: inline-block;
        padding: 10px;
        font-size: 14px;
        background: #fff;
        position: relative;
        margin-top: 8px;
        background: #dbdbdb;
        border-radius: 4px;
      }
      //小三角形
      .info-content::before {
        position: absolute;
        left: -8px;
        top: 8px;
        content: "";
        border-right: 10px solid #dbdbdb;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
      }
    }
  }

  .word-my {
    display: flex;
    justify-content: flex-end;
    // margin-bottom: 30px;
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    .info {
      width: 90%;
      // margin-left: 10px;
      text-align: right;
      // position: relative;
      display: flex;
      align-items: flex-end;
      flex-wrap: wrap;
      flex-direction: column;
      .info-content {
        word-break: break-all;
        max-width: 45%;
        padding: 10px;
        font-size: 14px;
        // float: right;
        margin-right: 10px;
        position: relative;
        margin-top: 8px;
        background: #a3c3f6;
        text-align: left;
        border-radius: 4px;
      }
      .Sender_time {
        padding-right: 12px;
        padding-top: 5px;
        font-size: 12px;
        color: rgba(51, 51, 51, 0.8);
        margin: 0;
        height: 20px;
      }
      //小三角形
      .info-content::after {
        position: absolute;
        right: -8px;
        top: 8px;
        content: "";
        border-left: 10px solid #a3c3f6;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
      }
    }
  }
}
.countdown-timer {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin: 30px 0;

  width: 30%;
  height: 70px;

  background: #176adb;
  display: flex;
  align-items: center;
  justify-content: center;
}
.quote-button {
  width: 100px;
  background: #176adb;

  color: #fff;
  font-weight: 700;

  border-radius: 5px;
}
.exper-title {
  height: 45px;
  background: #176adb;
  display: flex;
  justify-content: center;

  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 16px;
  color: #ffffff;
  letter-spacing: 0;
  align-items: center;
}
.expert-title-second {
  height: 40px;
  background: #1c57a7;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
.decryption-countdown {
  width: 50%;
  height: 105px;
  margin: 10px 25%;

  // background: #176adb;
  display: flex;
  align-items: center;
  justify-content: center;
  ::v-deep .number {
    color: #000;
    font-weight: 700;
  }
}
</style>
