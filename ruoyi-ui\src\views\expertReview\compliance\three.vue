<template>
  <div class="three">
    <div style="width:70%">
      <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px">符合性评审</div>
      <el-table :data="tableData" border style="width: 100%" :header-cell-style="headStyle" :cell-style="cellStyle">
        <el-table-column prop="供应商名称" width="180">
        </el-table-column>
        <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.xm" :label="item.xm">
          <template slot-scope="scope">
            <!-- <span v-if="scope.row[item.xm] == '/'"> -->
<!--              {{ scope.row[item.xm] }}-->
            <!-- 未提交 -->
            <!-- </span> -->
            <!-- <i v-else style="color:#176ADB;font-size:20px" :class="getIconClass(scope.row[item.xm])"></i> -->

            <span v-if="scope.row[item.xm] == '/'">未提交</span>
            <span v-if="scope.row[item.xm] == '1'">通过</span>
            <span v-if="scope.row[item.xm] == '0'">不通过</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="result">
        <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;margin:20px 0">评审结果：</div>
        <div style="display: flex;margin-left:30px">
          <div style="margin-right:30px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;" v-for="(item,index) in (Array.isArray(result) ? result : [])" :key="index">
            {{ item.gys }}：
            <span v-if="item.result" style="color:green">
              通过
            </span>
            <span v-else style="color:red">
              不通过
            </span>
          </div>
        </div>
      </div>
      <div class="operation" v-if="!finish">
        <el-button
          class="item-button"
          style="background-color: #f5f5f5;color: #176adb;"
          @click="reviewed"
        >重新评审</el-button>
        <el-button class="item-button" v-if="passedSupplierCount >= 3" @click="completed">节点评审完成</el-button>
        <el-button class="item-button-red"
                   v-if="!hasIncompleteExpert"
                   :disabled="passedSupplierCount >= 3"
                   :style="passedSupplierCount >= 3 ? 'background-color: #ccc; color: #fff; cursor: not-allowed;' : ''"
                   @click="flowLabel">流标</el-button>
      </div>
      <div v-else class="operation">
        <el-button class="item-button" @click="back">返回</el-button>
      </div>
    </div>
    <div style="width:30%">
      <div class="result">
        <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px">表决结果</div>
        <el-input disabled class="text" type="textarea" :rows="20" placeholder="请输入表决结果" v-model="votingResults">
        </el-input>
      </div>
    </div>
    <el-dialog title="流标情况说明" :visible.sync="dialogVisible" width="30%">
      <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="reasonFlowBid">
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmflow">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import { leaderSummaryQuery, reEvaluate } from "@/api/expert/review";
import { updateProcess } from "@/api/evaluation/process";
import { abortiveTenderNotice } from "@/api/bidder/notice";
import { reEvaluationTwo } from "@/api/evaluation/expertStatus";

export default {
  props: {
    finish: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
      columns: {},
      result: [], // 修改：初始化为数组而不是对象

      votingResults: "",
      reasonFlowBid: "",
      dialogVisible: false,

      // 定时器ID，用于清除定时器
      intervalId: null,

      leader: {},
      headStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      cellStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
    };
  },
  methods: {
    init() {
      const data = {
        projectId: this.$route.query.projectId,
        itemId: this.$route.query.scoringMethodItemId,
      };
      leaderSummaryQuery(data).then((response) => {
        if (response.code == 200) {
          this.votingResults = response.data.bjjgsb
          this.tableData = this.transformData(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );
          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)
          this.columns = response.data.tableColumns;
          this.result = this.generateResultTable(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );
          this.result = this.result.filter(item => item.isAbandonedBid == 0)
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    // 转换函数
    transformData(tableColumns, busiBidderInfos, tableData) {
      // 创建一个映射，用于将 bidderId 映射到 bidderName
      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {
        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };
        return acc;
      }, {});

      // 创建一个映射，用于将 resultId 映射到 itemName
      const columnIdToName = tableColumns.reduce((acc, column) => {
        acc[column.resultId] = column.xm;
        return acc;
      }, {});

      // 转换数据
      return tableData.map((row) => {
        const supplierId = row.gys;
        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];
        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };

        // 只取 tableColumns 中定义的评估项
        tableColumns.forEach((column) => {
          const itemId = column.resultId;
          transformedRow[column.xm] = row[itemId] || "/"; // 默认为'/'(没有评完)
        });

        return transformedRow;
      });
    },
    // 组装评审结果
    //少数服从多数
    generateResultTable(tableColumns, busiBidderInfos, tableData) {
      const entMethodItemIds = tableColumns.map((item) => {
        return item.resultId;
      });

      // Create a map from bidderId to bidderName
      const bidderMap = new Map(
        busiBidderInfos.map((bidder) => [bidder.bidderId, {
          bidderName: bidder.bidderName,
          isAbandonedBid: bidder.isAbandonedBid || 0 // 如果 isAbandonedBid 不存在，则默认为 0
        }])
      );

      // 生成结果表，按照少数服从多数规则判断是否通过
      return tableData.map((row) => {
        const supplierId = row.gys;
        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);
        const totalItems = entMethodItemIds.length;
        let passedCount = 0;
        entMethodItemIds.forEach((key) => {
          if (row[key] == "1") {
            passedCount++;
          }
        });
        const result = passedCount >= Math.ceil(totalItems / 2);
        return {
          bidder: supplierId,
          gys: bidderName,
          isAbandonedBid: isAbandonedBid,
          result: result,
        };
      });
    },
/*  只要有一个是不通过就算不同过
    generateResultTable(tableColumns, busiBidderInfos, tableData) {
      const entMethodItemIds = tableColumns.map((item) => {
        return item.resultId;
      });

      // Create a map from bidderId to bidderName
      const bidderMap = new Map(
        busiBidderInfos.map((bidder) => [bidder.bidderId, {
          bidderName: bidder.bidderName,
          isAbandonedBid: bidder.isAbandonedBid || 0 // 如果 isAbandonedBid 不存在，则默认为 0
        }])
      );

      // Generate the result table、
      return tableData.map((row) => {
        const supplierId = row.gys;
        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);
        var result = true;
        const temp = entMethodItemIds.every((key) => {
          return row[key] == "1";
        });
        if (!temp) {
          result = false;
        }
        return {
          bidder: supplierId,
          gys: bidderName,
          isAbandonedBid: isAbandonedBid,
          result: result,
        };
      });
    },
*/
    // 节点评审完成
    completed() {
			
			
      const evaluationProcessId = JSON.parse(
        localStorage.getItem("evalProjectEvaluationProcess")
      );
      const data = {
        evaluationProcessId: evaluationProcessId.evaluationProcessId,
        evaluationResult: JSON.stringify(this.result),
        evaluationState: 2,
        evaluationResultRemark: this.votingResults,
      };
      updateProcess(data).then((response) => {
        if (response.code == 200) {
          this.$router.push({
            path: "/expertInfo",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
              tips: true,
	            tenderMode:1
            },
          });
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    // 返回
    back() {
      this.$router.push({
        path: "/expertInfo",
        query: {
          projectId: this.$route.query.projectId,
          zjhm: this.$route.query.zjhm,
        },
      });
    },
    getIconClass(value) {
	    if (value == "1"){
		    return "el-icon-check"           // 通过：显示勾选图标
	    }
	    
	    if (value == "0"){
		    return "el-icon-circle-close"    // 不通过：显示关闭图标
	    }
	    
	    return value  // 其他情况直接返回原值
    },
    // 重新评审
    reviewed() {
      const query = {
        projectEvaluationId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).projectEvaluationId,
        expertResultId: JSON.parse(localStorage.getItem("evalExpertScoreInfo"))
          .expertResultId,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).scoringMethodItemId,
      };
      reEvaluationTwo(query).then((res) => {
        if (res.code == 200) {
          const evaluationProcessId = JSON.parse(
            localStorage.getItem("evalProjectEvaluationProcess")
          );

          reEvaluate(evaluationProcessId.evaluationProcessId).then(
            (response) => {
              if (response.code == 200) {
                // 触发重新评审通知，通知其他专家页面
                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {
                  this.$parent.triggerReEvaluationNotification();
                }
                this.$emit("send", "one");
              } else {
                this.$message.warning(response.msg);
              }
            }
          );
        }
      });
    },
    flowLabel() {
      this.dialogVisible = true;
    },
    // 确认流标
    confirmflow() {
      if (this.reasonFlowBid == "") {
        this.$message.warning("请完善情况说明");
        return;
      }
      // if (this.reasonFlowBid == "") {
      const data = {
        projectId: this.$route.query.projectId,
        abortiveType: 3,
        remark: this.reasonFlowBid,
        scoringMethodItemId: this.$route.query.scoringMethodItemId,
      };
      abortiveTenderNotice(data).then((response) => {
        if (response.code == 200) {
          const query = {
            projectId: this.$route.query.projectId,
            zjhm: this.$route.query.zjhm,
            scoringMethodItemId: this.$route.query.scoringMethodItemId,
          };
          this.$router.push({ path: "/summary", query: query });
        } else {
          this.$message.warning(response.msg);
        }
      });
      // } else {
      // }
    },

    /**
     * 清除定时器的通用方法
     * 在多个生命周期钩子中调用，确保定时器被正确清除
     */
    clearTimer() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
        console.log("定时器已清除 - compliance/three.vue");
      }
    },
  },
  computed:{
    passedSupplierCount() {
      console.log("this.result:",this.result);
      // 添加安全检查：确保 result 是数组
      if (!Array.isArray(this.result)) {
        console.warn("result is not an array:", this.result);
        return 0;
      }
      return this.result.filter(item => item.result).length;
    },

    /**
     * 检查是否有专家未完成评审
     * 遍历tableData检查是否存在"/"状态（未评完）
     * @returns {boolean} true表示有未完成评审的专家，false表示所有专家都已完成评审
     */
    hasIncompleteExpert() {
      // 添加安全检查：确保 tableData 是数组
      if (!Array.isArray(this.tableData)) {
        console.warn("tableData is not an array:", this.tableData);
        return true; // 数据异常时，默认禁用流标按钮
      }

      // 遍历所有供应商的评审数据
      const hasIncomplete = this.tableData.some(row => {
        // 遍历每一行的所有属性，查找是否有"/"状态
        return Object.keys(row).some(key => {
          // 排除供应商名称和废标状态字段，只检查专家评审结果
          if (key !== '供应商名称' && key !== 'isAbandonedBid') {
            return row[key] === '/';
          }
          return false;
        });
      });

      // 输出调试信息
      console.log("hasIncompleteExpert:", hasIncomplete, "tableData:", this.tableData);
      return hasIncomplete;
    }
  },

  /**
   * 组件挂载完成后执行
   * 初始化组件数据和定时刷新
   */
  mounted() {
    // 初始化数据
    this.init();

    // 设置定时器，每5秒自动刷新数据
    // 用于实时更新评审状态和结果
    this.intervalId = setInterval(()=>{
      this.init();
    },5000)
  },

  /**
   * 组件销毁前执行
   * 清除定时器，防止内存泄漏
   */
  beforeDestroy() {
    this.clearTimer();
  },

  /**
   * 组件完全销毁后执行
   * 作为额外的安全措施清除定时器
   */
  destroyed() {
    this.clearTimer();
  },

  /**
   * 如果父组件使用了keep-alive，在组件失活时清除定时器
   */
  deactivated() {
    this.clearTimer();
  },
};
</script>

<style lang="scss" scoped>
.three {
  padding: 20px 40px;
  display: flex;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #fff;
  background-color: #176adb;
  border: 0;
  &:hover {
    color: #fff;
  }
}
.item-button-red {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #fff;
  background-color: #e92900;
  border: 0;
  &:hover {
    color: #fff;
  }
}
.result {
  text-align: left;
  margin-left: 20px;
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
</style>
