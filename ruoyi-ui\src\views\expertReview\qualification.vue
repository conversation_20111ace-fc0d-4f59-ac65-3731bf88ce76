<template>
  <div>
<!--    <BidHeadthree></BidHeadthree>-->
	  <div class="title">专家评审系统</div>
    <div class="info">
      <div class="content">
        <one v-if="node == 'one'" @send="handleData"></one>
        <two v-if="node == 'two'" @send="handleData" :isLeader="isLeader" :finish="finish"></two>
        <three ref="threeComponent" v-if="node == 'three'" @send="handleData" :finish="finish"></three>
      </div>
    </div>
    <Foot></Foot>
  </div>

</template>

<script>
import one from "./qualification/one";
import two from "./qualification/two";
import three from "./qualification/three";

import { getProject } from "@/api/tender/project";
import { expertInfoById } from "@/api/expert/review";
import { getEvalExpertScoreInfo } from "@/api/evaluation/expertStatus";
import expertReviewWebSocket from "@/mixins/expertReviewWebSocket";

export default {
  components: { one, two, three },
  mixins: [expertReviewWebSocket],
  name: "qualification",
  data() {
    return {
      project: {},
      projectName: "测试项目111",
      node: "one",
      finish: false,
      leader: {},
      isLeader: false,
    };
  },
  methods: {
    async init() {
      try {
        // 根据项目id查询项目信息
        const projectResponse = await getProject(this.$route.query.projectId);
        if (projectResponse.code === 200) {
          this.project = projectResponse.data;
        } else {
          this.$message.warning(projectResponse.msg);
        }

        // 获取专家信息
        const expertResponse = await expertInfoById({
          projectId: this.$route.query.projectId,
        });
        if (expertResponse.code === 200) {
          this.leader = expertResponse.data.find(
            (item) => item.expertLeader === 1
          );
          console.log("this.leader", this.leader);

          if (this.leader && this.leader.zjhm === this.$route.query.zjhm) {
            this.isLeader = true;
          }
        } else {
          this.$message.warning(expertResponse.msg);
        }

        // 设置 finish 和 node 的逻辑
        this.finish = this.$route.query.finish === "true";
        console.log("this.finish", this.finish, "this.isLeader", this.isLeader);
	      
	      // 判断当前环境
	      if (process.env.NODE_ENV === "development") {
		      this.node = "one";
		      return
	      }
				
				
        // 判断是否满足条件
        if (this.finish && this.isLeader) {
          this.node = "three";
        } else if (this.finish && !this.isLeader) {
          this.node = "two";
        } else {
          this.getEvalExpertStatus();
        }
      } catch (error) {
        console.error("Error during API calls:", error);
        // this.$message.error("An error occurred while fetching data.");
      }
    },
    // 查询专家评审节点信息
    getEvalExpertStatus() {
      // 查询专家评审节点信息
      getEvalExpertScoreInfo({
        projectEvaluationId: JSON.parse(
          localStorage.getItem("evalProjectEvaluationProcess")
        ).projectEvaluationId,
        expertResultId: JSON.parse(localStorage.getItem("expertResultId")),
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("evalProjectEvaluationProcess")
        ).scoringMethodItemId,
      }).then((expertStatusResponse) => {
        if (expertStatusResponse.code == 200) {
          localStorage.setItem(
            "evalExpertScoreInfo",
            JSON.stringify(expertStatusResponse.data)
          );
          if (expertStatusResponse.data.evalState == 0) {
            this.node = "one";
          } else if (expertStatusResponse.data.evalState == 1) {
            this.node = "two";
          }else if (expertStatusResponse.data.evalState == 2) {
	          if (this.isLeader) {
		          this.node = "three";
	          } else {
		          this.node = "two";
	          }
          }
        }
      });
    },
    // 跳转到二次报价
    secondOffer() {
      const query = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("tenderOfferScoringMethodItems")
        ),
      };
      this.$router.push({ path: "/secondOffer", query: query });
    },
    handleData(data) {
      // 在切换节点之前，主动清除当前组件的定时器
      if (this.node === 'three' && this.$refs.threeComponent) {
        this.$refs.threeComponent.clearTimer();
      }
      this.node = data;
    },
  },
  mounted() {
    this.init();
    // setInterval(()=>{
    // 	this.init();
    // },5000)
  },

  // 路由离开前的守卫
  beforeRouteLeave(to, from, next) {
    // 清除当前活跃组件的定时器
    if (this.node === 'three' && this.$refs.threeComponent) {
      this.$refs.threeComponent.clearTimer();
    }
    next();
  },
};
</script>

<style lang="scss" scoped>
.info {
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}
.content {
  background-color: #fff;
  width: 90%;
  min-height: 71vh;
  margin: 20px 0;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.little-title {
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
}
.item-button {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #176adb;
  background-color: #f5f5f5;
  border: 0;
  font-weight: 700;
  &:hover {
    color: #176adb;
  }
}
.item-button-little {
  border: #333 1px solid;
  width: 124px;
  height: 32px;
  background-color: rgba(151, 253, 246, 1);
  color: rgba(0, 0, 0, 1);
  &:hover {
    color: rgba(0, 0, 0, 1);
  }
}
.factors {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title{
	background-color: #c8c9cc;
	padding: 10px 5%;
}
</style>